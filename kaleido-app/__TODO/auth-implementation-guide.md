# Authentication Implementation Guide

## Quick Start Implementation

This guide provides concrete, copy-paste ready code for implementing the new authentication architecture.

## Step 1: Create Route Configuration

### `/src/config/routes.config.ts`

```typescript
import { z } from 'zod';

// Define route authentication levels
export type AuthLevel = 'public' | 'protected' | 'admin';

// Define user roles
export enum UserRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ADMIN = 'ADMIN',
  EMPLOYER = 'EMPLOYER',
  JOB_SEEKER = 'JOB_SEEKER',
  GRADUATE = 'GRADUATE',
  REFERRAL_PARTNER = 'REFERRAL_PARTNER',
}

// Route configuration interface
interface RouteConfig {
  auth: AuthLevel;
  roles?: UserRole[];
  pattern?: RegExp;
  exact?: boolean;
}

// Centralized route configuration
export const ROUTES: Record<string, RouteConfig> = {
  // ============ PUBLIC ROUTES ============
  // Landing and marketing pages
  '/': { auth: 'public', exact: true },
  '/about': { auth: 'public' },
  '/contact': { auth: 'public' },
  '/pricing': { auth: 'public' },
  
  // Auth endpoints
  '/api/auth/login': { auth: 'public' },
  '/api/auth/logout': { auth: 'public' },
  '/api/auth/callback': { auth: 'public' },
  '/api/auth/register': { auth: 'public' },
  
  // Public API endpoints
  '/api/jobs/public': { auth: 'public' },
  '/api/jobs/open-jobs': { auth: 'public' },
  '/api/jobs/company/public': { auth: 'public' },
  '/api/jobs/slug/*': { auth: 'public', pattern: /^\/api\/jobs\/slug\/.+$/ },
  '/api/jobs/*/public-application': { 
    auth: 'public', 
    pattern: /^\/api\/jobs\/[^\/]+\/public-application$/ 
  },
  '/api/companies/public': { auth: 'public' },
  '/api/companies/profile/*': { 
    auth: 'public', 
    pattern: /^\/api\/companies\/profile\/.+$/ 
  },
  
  // Public candidate endpoints
  '/api/candidates/public-profile/*': { 
    auth: 'public',
    pattern: /^\/api\/candidates\/public-profile\/.+$/
  },
  '/api/job-seekers/public/*': { 
    auth: 'public',
    pattern: /^\/api\/job-seekers\/public\/.+$/
  },
  
  // Health and monitoring
  '/api/health': { auth: 'public' },
  '/api/status': { auth: 'public' },
  
  // ============ PROTECTED ROUTES ============
  // Dashboard routes
  '/dashboard': { auth: 'protected' },
  '/dashboard/*': { auth: 'protected', pattern: /^\/dashboard\/.+$/ },
  
  // API endpoints requiring authentication
  '/api/auth/me': { auth: 'protected' },
  '/api/auth/refresh': { auth: 'protected' },
  '/api/auth/sync-role': { auth: 'protected' },
  
  // Job management
  '/api/jobs': { auth: 'protected' },
  '/api/jobs/by-status': { auth: 'protected' },
  '/api/jobs/*': { 
    auth: 'protected', 
    pattern: /^\/api\/jobs\/[^\/]+$/,
    // Exclude public patterns
    exclude: [
      /^\/api\/jobs\/public/,
      /^\/api\/jobs\/slug\//,
      /^\/api\/jobs\/company\/public/,
      /^\/api\/jobs\/[^\/]+\/public-application$/
    ]
  },
  
  // Company management
  '/api/companies/client': { auth: 'protected' },
  '/api/companies/*': { 
    auth: 'protected',
    pattern: /^\/api\/companies\/.+$/,
    exclude: [
      /^\/api\/companies\/public/,
      /^\/api\/companies\/profile\//
    ]
  },
  
  // Candidate management
  '/api/candidates': { auth: 'protected' },
  '/api/candidates/*': {
    auth: 'protected',
    pattern: /^\/api\/candidates\/.+$/,
    exclude: [
      /^\/api\/candidates\/public-profile\//,
      /^\/api\/candidates\/download-profile/
    ]
  },
  
  // User profile
  '/api/profile': { auth: 'protected' },
  '/api/profile/*': { auth: 'protected', pattern: /^\/api\/profile\/.+$/ },
  
  // ============ ADMIN ROUTES ============
  '/admin': { auth: 'admin', roles: [UserRole.ADMIN, UserRole.SUPER_ADMIN] },
  '/admin/*': { 
    auth: 'admin', 
    roles: [UserRole.ADMIN, UserRole.SUPER_ADMIN],
    pattern: /^\/admin\/.+$/
  },
  '/api/admin/*': { 
    auth: 'admin', 
    roles: [UserRole.ADMIN, UserRole.SUPER_ADMIN],
    pattern: /^\/api\/admin\/.+$/
  },
};

// Route checking utility
export class RouteChecker {
  /**
   * Check if a route is public
   */
  static isPublic(path: string): boolean {
    return this.getRouteConfig(path)?.auth === 'public';
  }
  
  /**
   * Check if a route is protected
   */
  static isProtected(path: string): boolean {
    const config = this.getRouteConfig(path);
    return config?.auth === 'protected' || config?.auth === 'admin';
  }
  
  /**
   * Get route configuration for a path
   */
  static getRouteConfig(path: string): RouteConfig | null {
    // Clean the path
    const cleanPath = path.split('?')[0].split('#')[0];
    
    // Check exact matches first
    if (ROUTES[cleanPath]) {
      return ROUTES[cleanPath];
    }
    
    // Check pattern matches
    for (const [route, config] of Object.entries(ROUTES)) {
      // Skip if no pattern
      if (!config.pattern) continue;
      
      // Check if pattern matches
      if (config.pattern.test(cleanPath)) {
        // Check exclusions
        if (config.exclude) {
          const isExcluded = config.exclude.some(excludePattern => 
            excludePattern.test(cleanPath)
          );
          if (isExcluded) continue;
        }
        
        return config;
      }
    }
    
    // Default to protected for safety
    return { auth: 'protected' };
  }
  
  /**
   * Check if a user role has access to a route
   */
  static hasAccess(path: string, userRole: UserRole | null): boolean {
    const config = this.getRouteConfig(path);
    
    // Public routes are accessible to all
    if (config?.auth === 'public') {
      return true;
    }
    
    // Must be authenticated for protected routes
    if (!userRole) {
      return false;
    }
    
    // Super admin has access to everything
    if (userRole === UserRole.SUPER_ADMIN) {
      return true;
    }
    
    // Check role requirements
    if (config?.roles && config.roles.length > 0) {
      return config.roles.includes(userRole);
    }
    
    // Protected routes without specific role requirements
    return true;
  }
}

// Export route checking functions for backward compatibility
export const isPublicEndpoint = (path: string): boolean => {
  return RouteChecker.isPublic(path);
};

export const checkRouteAuth = (path: string): AuthLevel => {
  return RouteChecker.getRouteConfig(path)?.auth || 'protected';
};
```

## Step 2: Update API Helper

### `/src/lib/apiHelper.ts`

```typescript
import axios, { AxiosInstance, AxiosRequestConfig, AxiosError } from 'axios';
import { RouteChecker } from '@/config/routes.config';
import { AuthClient } from './auth-client';

class ApiHelper {
  private axiosInstance: AxiosInstance;
  private authClient: AuthClient;
  
  constructor() {
    this.authClient = AuthClient.getInstance();
    
    this.axiosInstance = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL_BASE || 'http://localhost:8080/api',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      withCredentials: true,
    });
    
    this.setupInterceptors();
  }
  
  private setupInterceptors(): void {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        const endpoint = config.url || '';
        
        // Only add auth header for protected routes
        if (!RouteChecker.isPublic(endpoint)) {
          try {
            const token = await this.authClient.getAccessToken();
            if (token) {
              config.headers.Authorization = `Bearer ${token}`;
            } else {
              console.warn(`No token available for protected route: ${endpoint}`);
            }
          } catch (error) {
            console.error('Failed to get access token:', error);
            // Let the request proceed - backend will handle unauthorized
          }
        }
        
        return config;
      },
      (error) => Promise.reject(error)
    );
    
    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      async (error: AxiosError) => {
        const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };
        
        // Handle 401 errors
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          try {
            // Attempt to refresh the token
            await this.authClient.refreshToken();
            
            // Retry the original request with new token
            const token = await this.authClient.getAccessToken();
            if (token && originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${token}`;
            }
            
            return this.axiosInstance(originalRequest);
          } catch (refreshError) {
            // Refresh failed, redirect to login
            this.authClient.logout();
            return Promise.reject(refreshError);
          }
        }
        
        // Handle 403 errors
        if (error.response?.status === 403) {
          // Check if it's a role-based access issue
          const endpoint = originalRequest.url || '';
          const userRole = await this.authClient.getUserRole();
          
          if (!RouteChecker.hasAccess(endpoint, userRole)) {
            // User doesn't have the required role
            console.error(`Access denied to ${endpoint} for role ${userRole}`);
            // Optionally redirect to an error page
            window.location.href = '/access-denied';
          }
        }
        
        return Promise.reject(error);
      }
    );
  }
  
  // Public methods
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axiosInstance.get<T>(url, config);
    return response.data;
  }
  
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axiosInstance.post<T>(url, data, config);
    return response.data;
  }
  
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axiosInstance.put<T>(url, data, config);
    return response.data;
  }
  
  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axiosInstance.patch<T>(url, data, config);
    return response.data;
  }
  
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axiosInstance.delete<T>(url, config);
    return response.data;
  }
}

// Export singleton instance
export const apiHelper = new ApiHelper();
export default apiHelper;
```

## Step 3: Implement Auth Client

### `/src/lib/auth-client.ts`

```typescript
import { UserRole } from '@/config/routes.config';

interface TokenData {
  accessToken: string;
  expiresAt: number;
  refreshToken?: string;
}

interface UserData {
  id: string;
  email: string;
  role: UserRole;
  [key: string]: any;
}

export class AuthClient {
  private static instance: AuthClient;
  private tokenData: TokenData | null = null;
  private userData: UserData | null = null;
  private refreshPromise: Promise<void> | null = null;
  
  private constructor() {
    // Initialize from localStorage if available
    this.loadFromStorage();
  }
  
  static getInstance(): AuthClient {
    if (!AuthClient.instance) {
      AuthClient.instance = new AuthClient();
    }
    return AuthClient.instance;
  }
  
  /**
   * Get the current access token
   */
  async getAccessToken(): Promise<string | null> {
    // Check if we have a valid token
    if (this.tokenData && this.isTokenValid()) {
      return this.tokenData.accessToken;
    }
    
    // Try to refresh the token
    try {
      await this.refreshToken();
      return this.tokenData?.accessToken || null;
    } catch (error) {
      console.error('Failed to get access token:', error);
      return null;
    }
  }
  
  /**
   * Refresh the access token
   */
  async refreshToken(): Promise<void> {
    // Prevent concurrent refresh attempts
    if (this.refreshPromise) {
      return this.refreshPromise;
    }
    
    this.refreshPromise = this.performTokenRefresh();
    
    try {
      await this.refreshPromise;
    } finally {
      this.refreshPromise = null;
    }
  }
  
  private async performTokenRefresh(): Promise<void> {
    try {
      // First try to get token from /api/auth/me
      const meResponse = await fetch('/api/auth/me', {
        credentials: 'include',
      });
      
      if (meResponse.ok) {
        const data = await meResponse.json();
        
        if (data.accessToken) {
          this.setTokenData({
            accessToken: data.accessToken,
            expiresAt: data.expiresAt || Date.now() + 3600000, // 1 hour default
          });
          
          this.setUserData({
            id: data.user?.sub || data.user?.id,
            email: data.user?.email,
            role: this.extractUserRole(data.user),
            ...data.user,
          });
          
          return;
        }
      }
      
      // If /api/auth/me didn't return a token, try explicit refresh
      const refreshResponse = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include',
      });
      
      if (!refreshResponse.ok) {
        throw new Error('Token refresh failed');
      }
      
      const refreshData = await refreshResponse.json();
      
      this.setTokenData({
        accessToken: refreshData.accessToken,
        expiresAt: refreshData.expiresAt,
      });
      
    } catch (error) {
      console.error('Token refresh failed:', error);
      // Clear invalid token data
      this.clearAuth();
      throw error;
    }
  }
  
  /**
   * Get the current user's role
   */
  async getUserRole(): Promise<UserRole | null> {
    if (this.userData?.role) {
      return this.userData.role;
    }
    
    // Try to fetch user data
    await this.refreshToken();
    return this.userData?.role || null;
  }
  
  /**
   * Extract user role from JWT claims
   */
  private extractUserRole(user: any): UserRole {
    const namespace = process.env.NEXT_PUBLIC_AUTH0_NAMESPACE || 'https://kaleidotalent.com';
    const role = user?.[`${namespace}/role`] || user?.role;
    
    // Map to UserRole enum
    if (role && Object.values(UserRole).includes(role)) {
      return role as UserRole;
    }
    
    // Default to job seeker if no role found
    return UserRole.JOB_SEEKER;
  }
  
  /**
   * Check if the current token is valid
   */
  private isTokenValid(): boolean {
    if (!this.tokenData) {
      return false;
    }
    
    // Check if token expires in more than 1 minute
    return this.tokenData.expiresAt > Date.now() + 60000;
  }
  
  /**
   * Set token data and persist to storage
   */
  private setTokenData(data: TokenData): void {
    this.tokenData = data;
    
    // Persist to localStorage
    localStorage.setItem('auth_token', JSON.stringify(data));
  }
  
  /**
   * Set user data and persist to storage
   */
  private setUserData(data: UserData): void {
    this.userData = data;
    
    // Persist to localStorage
    localStorage.setItem('auth_user', JSON.stringify(data));
  }
  
  /**
   * Load auth data from storage
   */
  private loadFromStorage(): void {
    try {
      const tokenStr = localStorage.getItem('auth_token');
      if (tokenStr) {
        this.tokenData = JSON.parse(tokenStr);
      }
      
      const userStr = localStorage.getItem('auth_user');
      if (userStr) {
        this.userData = JSON.parse(userStr);
      }
    } catch (error) {
      console.error('Failed to load auth data from storage:', error);
      this.clearAuth();
    }
  }
  
  /**
   * Clear all auth data
   */
  clearAuth(): void {
    this.tokenData = null;
    this.userData = null;
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_user');
    localStorage.removeItem('auth_session');
  }
  
  /**
   * Logout the user
   */
  logout(): void {
    this.clearAuth();
    window.location.href = '/api/auth/logout';
  }
}
```

## Step 4: Update Middleware

### `/src/middleware.ts`

```typescript
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0/edge';
import { RouteChecker, UserRole } from './config/routes.config';

// Security headers
const SECURITY_HEADERS = {
  'X-DNS-Prefetch-Control': 'on',
  'X-Frame-Options': 'SAMEORIGIN',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'X-XSS-Protection': '1; mode=block',
};

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Create response with security headers
  const response = NextResponse.next();
  
  // Apply security headers to all responses
  Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  
  // Get route configuration
  const routeConfig = RouteChecker.getRouteConfig(pathname);
  
  // Public routes don't need authentication
  if (routeConfig?.auth === 'public') {
    return response;
  }
  
  // Get session for protected routes
  const session = await getSession(request, response);
  
  // No session - redirect to login
  if (!session?.user) {
    // For API routes, return 401
    if (pathname.startsWith('/api/')) {
      return new NextResponse(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // For page routes, redirect to login
    const url = new URL('/api/auth/login', request.url);
    url.searchParams.set('returnTo', pathname);
    return NextResponse.redirect(url);
  }
  
  // Extract user role
  const namespace = process.env.NEXT_PUBLIC_AUTH0_NAMESPACE || 'https://kaleidotalent.com';
  const userRole = session.user[`${namespace}/role`] || session.user['role'];
  
  // Check role-based access
  if (routeConfig?.roles && routeConfig.roles.length > 0) {
    if (!userRole || !routeConfig.roles.includes(userRole as UserRole)) {
      // For API routes, return 403
      if (pathname.startsWith('/api/')) {
        return new NextResponse(
          JSON.stringify({ error: 'Forbidden' }),
          { status: 403, headers: { 'Content-Type': 'application/json' } }
        );
      }
      
      // For page routes, redirect to access denied
      return NextResponse.redirect(new URL('/access-denied', request.url));
    }
  }
  
  // Add user context to headers for downstream use
  response.headers.set('X-User-Id', session.user.sub || '');
  response.headers.set('X-User-Role', userRole || '');
  response.headers.set('X-User-Email', session.user.email || '');
  
  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization)
     * - favicon.ico, robots.txt (metadata files)
     * - public folder
     * - images and other static assets
     */
    '/((?!_next/static|_next/image|favicon.ico|robots.txt|public/|.*\\.(png|jpg|jpeg|gif|svg|webp|ico|css|js)$).*)',
  ],
};
```

## Step 5: Migration Steps

### 5.1 Remove Old Code

Remove from `/src/lib/apiHelper.utils.ts`:
```typescript
// DELETE THIS:
export const PUBLIC_ENDPOINTS = [
  // ... all hardcoded endpoints
];

// DELETE THIS:
export const isPublicEndpoint = (endpoint: string): boolean => {
  // ... old implementation
};
```

### 5.2 Update Imports

Update all files importing from `apiHelper.utils.ts`:
```typescript
// OLD:
import { isPublicEndpoint } from '@/lib/apiHelper.utils';

// NEW:
import { RouteChecker } from '@/config/routes.config';
// Use: RouteChecker.isPublic(path)
```

### 5.3 Environment Variables

Add to `.env.local`:
```bash
# Auth0 Configuration
AUTH0_SECRET=your-secret-here
AUTH0_BASE_URL=http://localhost:3000
AUTH0_ISSUER_BASE_URL=https://your-domain.auth0.com
AUTH0_CLIENT_ID=your-client-id
AUTH0_CLIENT_SECRET=your-client-secret
AUTH0_AUDIENCE=https://api.kaleidotalent.com
NEXT_PUBLIC_AUTH0_NAMESPACE=https://kaleidotalent.com

# Feature Flags (for gradual rollout)
NEXT_PUBLIC_USE_NEW_AUTH=false  # Set to true when ready
```

## Step 6: Testing

### Unit Tests

```typescript
// __tests__/config/routes.test.ts
import { RouteChecker, UserRole } from '@/config/routes.config';

describe('RouteChecker', () => {
  describe('isPublic', () => {
    it('should identify public routes', () => {
      expect(RouteChecker.isPublic('/')).toBe(true);
      expect(RouteChecker.isPublic('/api/auth/login')).toBe(true);
      expect(RouteChecker.isPublic('/api/jobs/public')).toBe(true);
    });
    
    it('should identify protected routes', () => {
      expect(RouteChecker.isPublic('/dashboard')).toBe(false);
      expect(RouteChecker.isPublic('/api/jobs/by-status')).toBe(false);
      expect(RouteChecker.isPublic('/api/companies/client')).toBe(false);
    });
    
    it('should handle pattern matching', () => {
      expect(RouteChecker.isPublic('/api/jobs/123/public-application')).toBe(true);
      expect(RouteChecker.isPublic('/api/jobs/123/manage')).toBe(false);
    });
  });
  
  describe('hasAccess', () => {
    it('should allow access to public routes', () => {
      expect(RouteChecker.hasAccess('/', null)).toBe(true);
      expect(RouteChecker.hasAccess('/api/jobs/public', null)).toBe(true);
    });
    
    it('should check role requirements', () => {
      expect(RouteChecker.hasAccess('/admin', UserRole.EMPLOYER)).toBe(false);
      expect(RouteChecker.hasAccess('/admin', UserRole.ADMIN)).toBe(true);
      expect(RouteChecker.hasAccess('/admin', UserRole.SUPER_ADMIN)).toBe(true);
    });
  });
});
```

## Step 7: Gradual Rollout

### Feature Flag Implementation

```typescript
// /src/lib/feature-flags.ts
export function useNewAuth(): boolean {
  return process.env.NEXT_PUBLIC_USE_NEW_AUTH === 'true';
}

// In apiHelper.ts
import { useNewAuth } from '@/lib/feature-flags';
import { RouteChecker } from '@/config/routes.config';
import { isPublicEndpoint as legacyIsPublic } from './apiHelper.utils';

// In request interceptor
const isPublic = useNewAuth() 
  ? RouteChecker.isPublic(endpoint)
  : legacyIsPublic(endpoint);
```

## Rollout Schedule

### Week 1: Development Environment
- Deploy new system with feature flag OFF
- Run parallel testing
- Monitor logs for discrepancies

### Week 2: Staging Environment
- Enable for 10% of requests
- Compare auth success rates
- Fix any edge cases

### Week 3: Production Canary
- Enable for 5% of production traffic
- Monitor error rates
- Gather performance metrics

### Week 4: Production Rollout
- Gradually increase to 100%
- Keep legacy code for quick rollback
- Monitor all metrics

### Week 5: Cleanup
- Remove feature flags
- Delete legacy code
- Update documentation

## Monitoring Checklist

- [ ] Auth success rate > 99.5%
- [ ] Token refresh success rate > 99%
- [ ] Average auth latency < 100ms
- [ ] No increase in 401/403 errors
- [ ] No increase in logout rates
- [ ] No customer complaints

## Rollback Plan

If issues arise:
1. Set `NEXT_PUBLIC_USE_NEW_AUTH=false`
2. Deploy immediately (no code changes needed)
3. Investigate issues in staging
4. Fix and re-attempt rollout

## Success Metrics

- 70% reduction in auth-related code
- 50% reduction in auth-related bugs
- 90% reduction in route misconfiguration issues
- Improved developer productivity
- Better security posture