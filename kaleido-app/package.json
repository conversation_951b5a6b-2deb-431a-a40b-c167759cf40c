{"name": "kaleido-app", "version": "0.1.0", "private": true, "scripts": {"redis:start": "docker-compose up -d", "redis:stop": "docker-compose down", "worker": "NODE_PATH=./src tsx --tsconfig tsconfig.worker.json src/workers/index.ts", "worker:build": "tsc --project tsconfig.worker.json", "worker:start": "node dist/workers/index.js", "worker:candidate-search": "NODE_PATH=./src tsx --tsconfig tsconfig.worker.json src/workers/start-worker.ts", "worker:candidate-search:build": "tsc --project tsconfig.worker.json", "worker:candidate-search:start": "node dist/workers/start-worker.js", "dev:webpack": "NEXT_FAST_REFRESH=true NODE_ENV=development NODE_OPTIONS='--inspect' next dev", "dev": "node scripts/dev-with-banner.js --turbo", "dev:clean": "rm -rf .next && NEXT_FAST_REFRESH=true NODE_ENV=development NODE_OPTIONS='--inspect' next dev --turbo", "dev:banner": "node scripts/dev-with-banner.js", "dev:banner:turbo": "node scripts/dev-with-banner.js --turbo", "dev:banner:webpack": "node scripts/dev-with-banner.js --webpack", "dev:custom": "node scripts/next-server-wrapper.js", "test:banner": "node test-banner.js", "prebuild": "node scripts/prebuild.js", "build": "NODE_OPTIONS='--max-old-space-size=8192' next build --turbo", "build:webpack": "NODE_OPTIONS='--max-old-space-size=8192' next build", "build:turbo": "NODE_OPTIONS='--max-old-space-size=8192' next build --turbo", "build:analyze": "ANALYZE=true NODE_OPTIONS='--max-old-space-size=8192' next build", "build:profile": "NEXT_TELEMETRY_DISABLED=1 NODE_OPTIONS='--max-old-space-size=8192' next build --profile", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss}\" && pnpm lint:fix", "check-types": "tsc --noEmit", "validate": "pnpm run lint && pnpm run check-types", "clean:logs": "node scripts/remove-console-logs-improved.js", "clean:logs:dry": "node scripts/remove-console-logs-improved.js --dry-run", "clean:logs:old": "node scripts/remove-console-logs.js", "clean:logs:backend": "node scripts/remove-console-logs-backend.js", "clean:logs:backend:dry": "node scripts/remove-console-logs-backend.js --dry-run", "test": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "test:watch": "jest --watch", "test:coverage": "jest --coverage --detect<PERSON><PERSON>Handles --forceExit", "test:ci": "jest --ci --coverage --watchAll=false --detectOpenHandles --forceExit", "test:timeout": "timeout 300 jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "test:dashboard": "jest --coverage --detectOpenHandles --forceExit --passWithNoTests", "test:dashboard:watch": "jest --coverage --detectOpenHandles --forceExit --passWithNoTests --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:auth": "./scripts/test-auth-flow.sh", "test:auth-modal": "./scripts/test-auth-modal.sh", "playwright:install": "playwright install", "playwright:install-deps": "playwright install-deps", "typeorm": "tsx ./node_modules/typeorm/cli.js", "migration:generate": "tsx ./node_modules/typeorm/cli.js migration:generate -d src/lib/database/migration.config.ts", "migration:run": "tsx ./node_modules/typeorm/cli.js migration:run -d src/lib/database/migration.config.ts", "migration:revert": "tsx ./node_modules/typeorm/cli.js migration:revert -d src/lib/database/migration.config.ts", "fix-all": "pnpm run format && pnpm run lint:fix", "cache:clean": "rm -rf .turbo-cache && rm -rf .next/cache", "cache:prune": "find .turbo-cache -type f -atime +30 -delete && find .next/cache -type f -atime +30 -delete", "deploy:turbo": "./deploy-turbopack.sh", "optimize:images": "node scripts/optimize-images-cli.js"}, "dependencies": {"@auth0/nextjs-auth0": "^3.5.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@google-cloud/video-intelligence": "^5.3.0", "@heroicons/react": "^2.1.5", "@intercom/messenger-js-sdk": "^0.0.14", "@material-tailwind/react": "^2.1.10", "@mediapipe/selfie_segmentation": "^0.1.1675465747", "@microsoft/clarity": "^1.0.0", "@mui/icons-material": "^5.16.7", "@mui/material": "^5.16.7", "@mui/system": "^6.0.2", "@phosphor-icons/react": "^2.1.7", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.1.5", "@react-pdf/renderer": "^4.3.0", "@react-spring/three": "^9.7.5", "@react-three/drei": "^9.118.0", "@react-three/fiber": "^8.17.10", "@rollup/rollup-darwin-x64": "^4.40.0", "@sentry/nextjs": "^9.2.0", "@shadcn/ui": "^0.0.4", "@splinetool/react-spline": "^4.0.0", "@tanstack/react-query": "^5.59.20", "@tensorflow-models/body-segmentation": "^1.0.2", "@tensorflow/tfjs-backend-webgl": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "@types/chance": "^1.1.6", "@types/pubsub-js": "^1.8.6", "@types/three": "^0.170.0", "auth0": "^4.27.0", "autoprefixer": "^10.4.20", "aws-sdk": "^2.1691.0", "axios": "^1.7.7", "axios-mock-adapter": "^2.1.0", "bottleneck": "^2.19.5", "bull": "^4.16.4", "chance": "^1.1.12", "chart.js": "^4.5.0", "chartjs-plugin-datalabels": "^2.2.0", "class-variance-authority": "^0.7.0", "cookies-next": "^4.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "dialog": "^0.3.1", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "formidable": "^3.5.2", "framer-motion": "^11.11.11", "html2canvas": "^1.4.1", "immer": "^10.1.1", "jspdf": "^3.0.1", "limiter": "^2.1.0", "lodash": "^4.17.21", "lucide-react": "^0.441.0", "mailparser": "^3.7.1", "mammoth": "^1.8.0", "markdown-to-jsx": "^7.7.12", "next": "15.4.5", "node-loader": "^2.0.0", "null-loader": "^4.0.1", "openai": "^4.64.0", "pdf-parse": "^1.1.1", "pdf.js-extract": "^0.2.1", "pdfkit": "^0.15.1", "peopledatalabs": "^9.3.6", "pg": "^8.13.1", "pubsub-js": "^1.9.5", "puppeteer": "^23.6.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-adblocker": "^2.13.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "qrcode": "^1.5.4", "react": "^18", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-confetti": "^6.1.0", "react-day-picker": "^9.5.1", "react-dom": "^18", "react-dropzone": "^14.3.3", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-markdown": "^10.1.0", "react-quill": "^2.0.0", "react-responsive": "^10.0.0", "react-spinners": "^0.14.1", "recharts": "^2.15.0", "reflect-metadata": "^0.2.2", "remark-gfm": "^4.0.1", "sanitize-html": "^2.13.1", "three": "^0.171.0", "tsconfig-paths": "^4.2.0", "tsx": "^4.7.1", "unzipper": "^0.12.3", "uuid": "^10.0.0", "zod": "^3.23.8", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@next/eslint-plugin-next": "^14.2.6", "@playwright/test": "^1.48.0", "@swc/core": "^1.12.1", "@swc/jest": "^0.2.38", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/chance": "^1.1.6", "@types/fluent-ffmpeg": "^2.1.26", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.9", "@types/mailparser": "^3.4.5", "@types/node": "^20.17.6", "@types/pg": "^8.11.10", "@types/react": "^18.3.4", "@types/react-dom": "^18.3.0", "@types/sanitize-html": "^2.11.0", "@types/unzipper": "^0.10.9", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.2.0", "babel-jest": "^30.0.0", "boxen": "^5.1.2", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "chalk": "^4.1.2", "cli-table3": "^0.6.5", "clsx": "^2.1.1", "crypto-browserify": "^3.12.0", "dotenv": "^16.5.0", "eslint": "^8", "eslint-config-next": "14.2.6", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^4.6.2", "figlet": "^1.8.2", "gradient-string": "^2.0.2", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-transform-stub": "^2.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "postcss": "^8.4.41", "prettier": "^3.3.3", "sharp": "^0.34.3", "stream-browserify": "^3.0.0", "string-replace-loader": "^3.1.0", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.10", "tailwindcss-animate": "^1.0.7", "typeorm": "^0.3.20", "typescript": "5.5.3", "worker-loader": "^3.0.8"}, "resolutions": {"eslint": "^8.57.0", "typescript": "5.5.3"}, "packageManager": "pnpm@8.15.1", "engines": {"node": "20.x"}}