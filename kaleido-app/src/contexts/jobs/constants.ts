import React from 'react';

import {
  Briefcase,
  Building,
  CheckCircle,
  Code,
  FileText,
  GraduationCap,
  UserCircle,
} from 'lucide-react';

import { StepValidation } from './types';

export const steps = [
  {
    name: 'Basic Job Information',
    component: React.lazy(() => import('@/components/steps/jd-creations/BasicJobInformation')),
    icon: Briefcase,
  },
  {
    name: 'Skills & Responsibilities',
    component: React.lazy(() => import('@/components/steps/jd-creations/4_JobResponsibilities')),
    icon: Code,
  },
  {
    name: 'Candidate Qualifications',
    component: React.lazy(() => import('@/components/steps/jd-creations/CandidateQualifications')),
    icon: GraduationCap,
  },
  {
    name: 'Job Environment',
    component: React.lazy(() => import('@/components/steps/jd-creations/JobEnvironment')),
    icon: Building,
  },
  {
    name: 'Hiring Manager Introduction',
    component: React.lazy(
      () => import('@/components/steps/jd-creations/15_HiringManagerDescription')
    ),
    icon: UserCircle,
  },
  {
    name: 'Final Draft',
    component: React.lazy(() => import('@/components/steps/preview/FinalDraft')),
    icon: FileText,
    isFinal: false,
  },
  {
    name: 'Success',
    component: React.lazy(() => import('@/components/steps/Success')),
    icon: CheckCircle,
    isFinal: true,
    path: 'published',
  },
];

export const stepValidations: Record<string, StepValidation> = {
  'company-description': {
    isValid: false,
    requiredFields: ['companyName', 'companyDescription'],
  },
  'basic-job-information': {
    isValid: false,
    requiredFields: ['typeOfHiring', 'typeOfJob', 'department', 'jobType', 'experience'],
  },
  'skills-&-responsibilities': {
    isValid: false,
    requiredFields: ['skills', 'jobResponsibilities'],
  },
  'candidate-qualifications': {
    isValid: false,
    requiredFields: ['experience'],
  },
  'job-environment': {
    isValid: true,
    requiredFields: [],
  },
  'hiring-manager-description': {
    isValid: true,
    requiredFields: [],
  },
};
