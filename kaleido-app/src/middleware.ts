// Import the Auth0 Turbopack polyfill
import './lib/auth0-turbopack-polyfill';

import { getSession } from '@auth0/nextjs-auth0/edge';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { getMiddlewareIgnorePaths } from './types/publicRoutes';
import { UserRole } from './types/roles';

// Security headers following OWASP recommendations
const securityHeaders = {
  'X-DNS-Prefetch-Control': 'on',
  'X-Frame-Options': 'SAMEORIGIN',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'X-XSS-Protection': '1; mode=block',
  // Prevent caching of authenticated pages to avoid cross-login issues
  'Cache-Control': 'no-store, no-cache, must-revalidate, private',
  Pragma: 'no-cache',
  Expires: '0',
};

// Role-based route access (server-side validation)
const ROLE_ROUTE_ACCESS: Record<UserRole, string[]> = {
  [UserRole.SUPER_ADMIN]: ['*'], // Access to all routes
  [UserRole.ADMIN]: [
    '/admin',
    '/dashboard',
    '/companies',
    '/candidates',
    '/jobs',
    '/job-description-creation',
    '/video-jd',
    '/match-rank',
    '/culture-fit',
    '/talent-hub',
    '/my-candidates',
    '/company-settings',
    '/open-jobs',
    '/marketplace',
  ],
  [UserRole.EMPLOYER]: ['/dashboard', '/company-settings', '/candidates', '/jobs', '/my-jobs'],
  [UserRole.JOB_SEEKER]: ['/dashboard', '/jobs/search', '/profile'],
  [UserRole.GRADUATE]: ['/dashboard', '/jobs/search', '/profile'],
  [UserRole.REFERRAL_PARTNER]: ['/referral-partner', '/dashboard'],
};

export async function middleware(request: NextRequest) {
  // Skip authentication for API routes, auth routes, and static files
  const { pathname } = request.nextUrl;
  const ignorePaths = getMiddlewareIgnorePaths();

  if (pathname && ignorePaths.some(p => p && pathname.startsWith(p))) {
    return NextResponse.next();
  }

  // Create response with security headers
  const response = NextResponse.next();

  // Apply security headers to all responses
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // Get the session from secure HTTP-only cookie
  const session = await getSession(request, response);

  // If no session, redirect to login
  if (!session?.user) {
    const url = new URL('/api/auth/login', request.url);
    url.searchParams.set('returnTo', request.nextUrl.pathname + request.nextUrl.search);
    return NextResponse.redirect(url);
  }

  // Extract role from JWT claims (industry standard)
  const namespace = process.env.NEXT_PUBLIC_AUTH0_NAMESPACE || 'https://kaleidotalent.com';
  const userRole = session.user[`${namespace}/role`] || session.user['role'];

  // Validate role-based access (server-side validation)
  if (userRole && userRole !== UserRole.SUPER_ADMIN) {
    const allowedRoutes = ROLE_ROUTE_ACCESS[userRole as UserRole] || [];
    const hasAccess = allowedRoutes.some(
      route => route === '*' || (pathname && pathname.startsWith(route))
    );

    if (!hasAccess) {
      // User doesn't have access to this route
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
  }

  // Add user info to request headers for downstream use
  response.headers.set('X-User-Id', session.user.sub || '');
  response.headers.set('X-User-Role', userRole || '');

  return response;
}

export const config = {
  matcher: [
    // Only apply to dashboard, profile, jobs, candidates, companies, and admin routes
    // Explicitly NOT matching any API routes, company-profile routes, or public profile pages
    '/dashboard/:path*',
    '/profile/:path*',
    '/jobs/:path*',
    '/my-jobs/:path*',
    '/candidates/:path*',
    '/companies/:path*',
    '/admin/:path*',
    '/employer/:path*',
    '/graduate/:path*',
    '/referral-partner/:path*',
    '/company-settings',
    // Note: onboarding pages handle their own auth checks
    // Note: accept-invitation is NOT matched, it's a public route
  ],
};
