import ImprovedCareerInsightsPage from '@/app/career-insights/improved-page';
import { InsightStatus, InsightType } from '@/lib/career-insights/config';
import { useCareerInsightsStore } from '@/stores/careerInsightsStore';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import React from 'react';

// Mock the next/navigation module
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(() => '/career-insights'),
  useSearchParams: jest.fn(() => ({
    get: jest.fn(() => null),
  })),
}));

// Mock the career insights store
jest.mock('@/stores/careerInsightsStore', () => ({
  useCareerInsightsStore: jest.fn(),
}));

// Mock useUser hook
jest.mock('@/hooks/useUser', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User',
    },
    isLoading: false,
    error: null,
  })),
}));

// Mock Auth0
jest.mock('@auth0/nextjs-auth0/client', () => ({
  UserProvider: ({ children }: any) => children,
  useUser: () => ({
    user: {
      sub: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User',
    },
    isLoading: false,
    error: null,
  }),
  withPageAuthRequired: (component: any) => component,
}));

// Mock useRole hook - export both default and named export
jest.mock('@/hooks/useRole', () => {
  const mockUseRole = jest.fn(() => ({
    role: 'JOB_SEEKER',
    loading: false,
    isJobSeeker: true,
    isEmployer: false,
    isGraduate: false,
    isAdmin: false,
    isSuperAdmin: false,
    hasChecked: true,
  }));

  return {
    __esModule: true,
    default: mockUseRole,
    useRole: mockUseRole,
  };
});

// Mock framer-motion to avoid animation-related test issues
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock the BaseLayout component
jest.mock('@/components/steps/layout/BaseLayout', () => {
  return function MockBaseLayout({ children }: { children: React.ReactNode }) {
    return <div data-testid="base-layout">{children}</div>;
  };
});

// Mock the AppLayout component
jest.mock('@/components/steps/layout/AppLayout', () => {
  return function MockAppLayout({ children }: { children: React.ReactNode }) {
    return <div data-testid="app-layout">{children}</div>;
  };
});

// Mock the ColourfulLoader component
jest.mock('@/components/Layouts/ColourfulLoader', () => {
  return function MockColourfulLoader() {
    return <div data-testid="colourful-loader">Loading...</div>;
  };
});

// Mock Image component
jest.mock('next/image', () => {
  return function MockImage({ alt, fill, ...props }: any) {
    // Convert fill boolean to proper string for img element
    const imgProps = { ...props };
    if (fill) {
      imgProps.style = { ...imgProps.style, objectFit: 'cover', width: '100%', height: '100%' };
    }
    // eslint-disable-next-line @next/next/no-img-element
    return <img alt={alt} {...imgProps} />;
  };
});

const mockPush = jest.fn();
const mockFetchInsights = jest.fn().mockResolvedValue(undefined);

const mockInsights = [
  {
    id: '1',
    jobSeekerId: 'job-seeker-1',
    type: InsightType.SKILL_GAP_ANALYSIS,
    status: InsightStatus.READY,
    title: 'Skill Gap Analysis',
    summary: 'Analysis of your skill gaps',
    viewCount: 5,
    createdAt: '2023-10-01T10:00:00Z',
    updatedAt: '2023-10-01T10:00:00Z',
  },
  {
    id: '2',
    jobSeekerId: 'job-seeker-1',
    type: InsightType.CAREER_PATH_RECOMMENDATION,
    status: InsightStatus.PROCESSING,
    title: 'Career Path Final Summary',
    summary: 'Recommended career paths for you',
    viewCount: 3,
    createdAt: '2023-10-02T10:00:00Z',
    updatedAt: '2023-10-02T10:00:00Z',
  },
  {
    id: '3',
    jobSeekerId: 'job-seeker-1',
    type: InsightType.MARKET_TREND_ANALYSIS,
    status: InsightStatus.READY,
    title: 'Market Trend Analysis',
    summary: 'Current market trends analysis',
    viewCount: 8,
    createdAt: '2023-10-03T10:00:00Z',
    updatedAt: '2023-10-03T10:00:00Z',
  },
];

describe('ImprovedCareerInsightsPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    });

    (useCareerInsightsStore as jest.Mock).mockReturnValue({
      insights: mockInsights,
      isLoading: false,
      fetchInsights: mockFetchInsights,
    });
  });

  describe('Page Rendering', () => {
    it('renders the page title and description', () => {
      render(<ImprovedCareerInsightsPage />);

      expect(screen.getByText('Career Insights')).toBeInTheDocument();
      expect(
        screen.getByText(
          'AI-powered insights to accelerate your career growth and unlock opportunities'
        )
      ).toBeInTheDocument();
    });

    it('displays insight statistics when insights are available', () => {
      render(<ImprovedCareerInsightsPage />);

      expect(screen.getByText('3 Total Insights')).toBeInTheDocument();
      expect(screen.getByText('16 Total Views')).toBeInTheDocument(); // 5 + 3 + 8
    });

    it('renders all insight type cards for creating new insights', () => {
      render(<ImprovedCareerInsightsPage />);

      // The Generate New button should be present
      const generateButton = screen.getByText('Generate New');
      expect(generateButton).toBeInTheDocument();

      // The existing insights should be rendered
      expect(screen.getByText('Skill Gap Analysis')).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    it('shows loading spinner when isLoading is true', () => {
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        insights: [],
        isLoading: true,
        fetchInsights: mockFetchInsights,
      });

      render(<ImprovedCareerInsightsPage />);

      expect(screen.getByTestId('colourful-loader')).toBeInTheDocument();
    });

    it('shows empty state when no insights exist', () => {
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        insights: [],
        isLoading: false,
        fetchInsights: mockFetchInsights,
      });

      render(<ImprovedCareerInsightsPage />);

      // Check that the page renders without insights
      expect(screen.getByText('Career Insights')).toBeInTheDocument();
      // The "Your Insights History" section should not be present when there are no insights
      expect(screen.queryByText('Your Insights History')).not.toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('calls fetchInsights on component mount', () => {
      render(<ImprovedCareerInsightsPage />);

      expect(mockFetchInsights).toHaveBeenCalledTimes(1);
    });

    it('navigates to create page when insight card is clicked', () => {
      render(<ImprovedCareerInsightsPage />);

      // Click on Generate New button
      const generateButton = screen.getByText('Generate New');
      fireEvent.click(generateButton);

      // Since activeTab is 'all' by default, it should show the dropdown
      // The dropdown contains insight type options
      // We can verify the navigation happens when clicking an insight card
      const existingInsight = screen.getByText('Skill Gap Analysis');
      if (existingInsight.closest('[role="button"]')) {
        fireEvent.click(existingInsight.closest('[role="button"]')!);
        expect(mockPush).toHaveBeenCalled();
      }
    });

    it('navigates to detail page when existing insight is clicked', () => {
      render(<ImprovedCareerInsightsPage />);

      // Look for the second Skill Gap Analysis card which should be the existing insight
      const skillGapCards = screen.getAllByText('Skill Gap Analysis');
      const existingInsightCard = skillGapCards[1]?.closest('div'); // Second one should be the existing insight

      if (existingInsightCard) {
        fireEvent.click(existingInsightCard);
        expect(mockPush).toHaveBeenCalledWith('/career-insights/1');
      }
    });

    it('expands and collapses insight groups', async () => {
      render(<ImprovedCareerInsightsPage />);

      // Verify that insights are displayed in the table
      await waitFor(() => {
        expect(screen.getByText('Skill Gap Analysis')).toBeInTheDocument();
      });

      // Verify that other insights are also displayed
      expect(screen.getByText('Career Path Final Summary')).toBeInTheDocument();
      expect(screen.getByText('Market Trend Analysis')).toBeInTheDocument();
    });
  });

  describe('Insight Grouping and Display', () => {
    it('groups insights by type correctly', () => {
      render(<ImprovedCareerInsightsPage />);

      // Should show the insights in the table view
      expect(screen.getByText('Skill Gap Analysis')).toBeInTheDocument();
      expect(screen.getByText('Career Path Final Summary')).toBeInTheDocument();
      expect(screen.getByText('Market Trend Analysis')).toBeInTheDocument();

      // Verify that the tab navigation shows the counts
      expect(screen.getByText(/All Insights \(3\)/)).toBeInTheDocument();
    });

    it('shows correct status badges for insights', () => {
      render(<ImprovedCareerInsightsPage />);

      // Look for status indicators
      // The READY status should appear for insights 1 and 3
      // The PROCESSING status should appear for insight 2
      const readyBadges = screen.queryAllByText('Ready');
      const processingBadges = screen.queryAllByText('Processing');

      expect(readyBadges.length).toBeGreaterThanOrEqual(1);
      expect(processingBadges.length).toBeGreaterThanOrEqual(1);
    });

    it('displays view counts correctly', () => {
      render(<ImprovedCareerInsightsPage />);

      // Check for the total views in the statistics section
      expect(screen.getByText('16 Total Views')).toBeInTheDocument();

      // Individual view counts might be displayed in various formats
      // Just verify that view count information exists
      const viewText = screen.getAllByText(/view/i);
      expect(viewText.length).toBeGreaterThan(0);
    });

    it('formats dates correctly', () => {
      render(<ImprovedCareerInsightsPage />);

      // The dates should be formatted as relative time (e.g., "2 days ago")
      // Since our mock dates are from October 2023, they should show as older dates
      const dateElements = screen.getAllByText(/ago|Today|Yesterday/);
      expect(dateElements.length).toBeGreaterThan(0);
    });
  });

  describe('Responsive Design', () => {
    it('renders properly on different screen sizes', () => {
      render(<ImprovedCareerInsightsPage />);

      // Simple test to ensure the page renders
      expect(screen.getByText('Career Insights')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles missing insight data gracefully', () => {
      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        insights: [
          {
            id: '1',
            type: InsightType.SKILL_GAP_ANALYSIS,
            status: InsightStatus.READY,
            title: 'Incomplete Insight',
            // Missing some optional fields but has required dates
            createdAt: '2023-10-01T10:00:00Z',
            updatedAt: '2023-10-01T10:00:00Z',
            viewCount: 0,
            summary: '',
            jobSeekerId: 'test-user',
          },
        ],
        isLoading: false,
        fetchInsights: mockFetchInsights,
      });

      expect(() => render(<ImprovedCareerInsightsPage />)).not.toThrow();
    });

    it('handles fetch insights error gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      mockFetchInsights.mockRejectedValue(new Error('Network error'));

      render(<ImprovedCareerInsightsPage />);

      expect(mockFetchInsights).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(<ImprovedCareerInsightsPage />);

      // Check for proper heading structure
      expect(screen.getByRole('heading', { name: /Career Insights/i })).toBeInTheDocument();
    });

    it('supports keyboard navigation', () => {
      render(<ImprovedCareerInsightsPage />);

      // Simple test to ensure the page renders
      expect(screen.getByText('Career Insights')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('does not cause memory leaks with component unmounting', () => {
      const { unmount } = render(<ImprovedCareerInsightsPage />);

      expect(() => unmount()).not.toThrow();
    });

    it('handles large numbers of insights efficiently', () => {
      const manyInsights = Array.from({ length: 50 }, (_, index) => ({
        id: `insight-${index}`,
        jobSeekerId: 'job-seeker-1',
        type: InsightType.SKILL_GAP_ANALYSIS,
        status: InsightStatus.READY,
        title: `Insight ${index}`,
        summary: `Summary ${index}`,
        viewCount: index,
        createdAt: new Date(Date.now() - index * 86400000).toISOString(), // Different dates
        updatedAt: new Date(Date.now() - index * 86400000).toISOString(),
      }));

      (useCareerInsightsStore as jest.Mock).mockReturnValue({
        insights: manyInsights,
        isLoading: false,
        fetchInsights: mockFetchInsights,
      });

      const startTime = Date.now();
      render(<ImprovedCareerInsightsPage />);
      const renderTime = Date.now() - startTime;

      // Ensure rendering completes within reasonable time (< 1000ms)
      expect(renderTime).toBeLessThan(1000);
    });
  });
});
