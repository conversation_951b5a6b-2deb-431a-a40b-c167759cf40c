// Simple browser-based video converter using Canvas API
// This avoids the complexity of FFmpeg.js while still providing basic conversion

export interface VideoConversionOptions {
  platform: string;
  quality?: 'high' | 'medium' | 'low';
  onProgress?: (progress: number) => void;
}

export interface AspectRatioConfig {
  width: number;
  height: number;
  videoBitrate: string;
  audioBitrate: string;
}

// Platform-specific aspect ratio configurations
const ASPECT_RATIO_CONFIGS: Record<string, AspectRatioConfig> = {
  linkedin: {
    width: 1280,
    height: 720, // 16:9 for LinkedIn
    videoBitrate: '2000k',
    audioBitrate: '128k',
  },
  facebook: {
    width: 1280,
    height: 720, // 16:9 for Facebook
    videoBitrate: '2500k',
    audioBitrate: '128k',
  },
  instagram: {
    width: 1080,
    height: 1080, // 1:1 square for Instagram
    videoBitrate: '2000k',
    audioBitrate: '128k',
  },
  stories: {
    width: 1080,
    height: 1920, // 9:16 vertical for Stories
    videoBitrate: '1500k',
    audioBitrate: '96k',
  },
};

export class VideoConverter {
  private isLoaded = false;

  constructor() {
    this.isLoaded = true; // Canvas API is always available
  }

  async initialize(): Promise<void> {
    // Canvas API is always available in browsers, no initialization needed
    this.isLoaded = true;
  }

  async convertForPlatform(videoUrl: string, options: VideoConversionOptions): Promise<Blob> {
    const { platform, onProgress } = options;

    const config = ASPECT_RATIO_CONFIGS[platform];
    if (!config) {
      throw new Error(`Unsupported platform: ${platform}`);
    }

    try {
      onProgress?.(5); // Starting

      // Create video element to load the source
      const video = document.createElement('video');
      // Don't set crossOrigin if it's not needed to avoid CORS issues
      // video.crossOrigin = 'anonymous';
      video.muted = true;
      video.preload = 'metadata';

      // Load video with better error handling
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Video loading timeout'));
        }, 10000); // 10 second timeout

        video.onloadedmetadata = () => {
          clearTimeout(timeout);
          resolve();
        };

        video.onerror = e => {
          clearTimeout(timeout);
          console.error('Video load error:', e);
          // More specific error based on the video's error
          let errorMsg = 'Failed to load video';
          if (video.error) {
            switch (video.error.code) {
              case video.error.MEDIA_ERR_ABORTED:
                errorMsg = 'Video loading was aborted';
                break;
              case video.error.MEDIA_ERR_NETWORK:
                errorMsg = 'Network error while loading video';
                break;
              case video.error.MEDIA_ERR_DECODE:
                errorMsg = 'Video format not supported';
                break;
              case video.error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                errorMsg = 'Video source not supported or CORS blocked';
                break;
              default:
                errorMsg = 'Unknown video loading error';
            }
          }
          reject(new Error(errorMsg));
        };

        video.onabort = () => {
          clearTimeout(timeout);
          reject(new Error('Video loading aborted'));
        };

        video.src = videoUrl;
      });

      onProgress?.(15); // Video loaded

      // Create canvas with target dimensions
      const canvas = document.createElement('canvas');
      canvas.width = config.width;
      canvas.height = config.height;
      const ctx = canvas.getContext('2d')!;

      // Calculate aspect ratio and positioning
      const videoAspect = video.videoWidth / video.videoHeight;
      const targetAspect = config.width / config.height;

      let drawWidth, drawHeight, drawX, drawY;

      if (videoAspect > targetAspect) {
        // Video is wider, fit by height
        drawHeight = config.height;
        drawWidth = drawHeight * videoAspect;
        drawX = (config.width - drawWidth) / 2;
        drawY = 0;
      } else {
        // Video is taller, fit by width
        drawWidth = config.width;
        drawHeight = drawWidth / videoAspect;
        drawX = 0;
        drawY = (config.height - drawHeight) / 2;
      }

      onProgress?.(25); // Setup complete

      // Create MediaRecorder to capture canvas
      const stream = canvas.captureStream(30); // 30 FPS

      // Try different codec options if vp9 isn't supported
      let mimeType = 'video/webm;codecs=vp9';
      if (!MediaRecorder.isTypeSupported(mimeType)) {
        mimeType = 'video/webm;codecs=vp8';
        if (!MediaRecorder.isTypeSupported(mimeType)) {
          mimeType = 'video/webm';
        }
      }

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType,
        videoBitsPerSecond: parseInt(config.videoBitrate.replace('k', '')) * 1000,
      });

      const chunks: Blob[] = [];
      mediaRecorder.ondataavailable = event => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      // Start recording
      mediaRecorder.start();

      // Play video and draw frames
      video.currentTime = 0;
      video.play();

      let lastProgressTime = 0;
      const startTime = Date.now();

      const drawFrame = () => {
        if (video.ended || video.paused) {
          mediaRecorder.stop();
          return;
        }

        try {
          // Fill canvas with black background
          ctx.fillStyle = 'black';
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          // Draw video frame (this might fail due to CORS)
          ctx.drawImage(video, drawX, drawY, drawWidth, drawHeight);
        } catch (drawError) {
          console.error('Failed to draw video frame:', drawError);
          mediaRecorder.stop();
          return;
        }

        // Update progress
        const currentProgress = Math.min((video.currentTime / video.duration) * 70 + 25, 95);
        const now = Date.now();
        if (now - lastProgressTime > 100) {
          // Update every 100ms
          onProgress?.(Math.round(currentProgress));
          lastProgressTime = now;
        }

        requestAnimationFrame(drawFrame);
      };

      requestAnimationFrame(drawFrame);

      // Wait for recording to complete
      const recordedBlob = await new Promise<Blob>(resolve => {
        mediaRecorder.onstop = () => {
          const blob = new Blob(chunks, { type: 'video/webm' });
          resolve(blob);
        };
      });

      onProgress?.(100); // Complete

      // Clean up
      video.pause();
      video.src = '';
      stream.getTracks().forEach(track => track.stop());

      return recordedBlob;
    } catch (error) {
      console.error('Video conversion failed:', error);
      throw new Error(`Failed to convert video for ${platform}: ${error}`);
    }
  }

  async terminate(): Promise<void> {
    // No cleanup needed for Canvas API
    this.isLoaded = false;
  }
}

// Singleton instance
let converterInstance: VideoConverter | null = null;

export const getVideoConverter = (): VideoConverter => {
  if (!converterInstance) {
    converterInstance = new VideoConverter();
  }
  return converterInstance;
};

export const cleanupConverter = async (): Promise<void> => {
  if (converterInstance) {
    await converterInstance.terminate();
    converterInstance = null;
  }
};
