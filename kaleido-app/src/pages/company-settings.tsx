'use client';

import { CreditCard, Database, Layout, Settings, Share2, Users } from 'lucide-react';
import React, { useEffect, useMemo, useState } from 'react';

import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import SocialMediaConnectors from '@/components/Settings/SocialMediaConnectors';
import { showToast } from '@/components/Toaster';
import PageHeader from '@/components/common/PageHeader';
import { SaveChangesSlider } from '@/components/common/SaveChangesSlider';
import { TabNavigation } from '@/components/common/TabNavigation';
import ATSConfiguration from '@/components/company-settings/ATSConfiguration';
import CompanyProfile from '@/components/company-settings/CompanyProfile';
import PublicProfileSettingsV2, {
  ProfileStyleSettings,
} from '@/components/company-settings/PublicProfileSettingsV2';
import SubscriptionPayments from '@/components/company-settings/SubscriptionPayments';
import TeamManagement from '@/components/company-settings/TeamManagement';
import AppLayout from '@/components/steps/layout/AppLayout';
import { useJobs } from '@/contexts/jobs/JobsContext';
import apiHelper from '@/lib/apiHelper';
import { useCurrentUser } from '@/services/user.service';
import { UserRole } from '@/types/roles';
import { useUser } from '@auth0/nextjs-auth0/client';
import { useRouter } from 'next/router';

// Helper function to convert company name to slug format
function slugify(text: string) {
  return text
    .toString()
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
    .replace(/--+/g, '-')
    .replace(/^-+/, '')
    .replace(/-+$/, '');
}

export interface ATSConfig {
  id: string;
  name: string;
  baseUrl: string;
  jobEndpoint: string;
  applicantEndpoint: string;
  authType: 'Basic' | 'Bearer' | 'Manual';
  authHeaderKey?: { 'x-api-key'?: string; Authorization?: string };
  fullLogoUrl?: string;
  logoUrl?: string;
  supportedAuthTypes?: ('Basic' | 'Bearer')[];
}

export const AtsConfigurations: ATSConfig[] = [
  {
    id: '1',
    name: 'Greenhouse',
    baseUrl: 'https://harvest.greenhouse.io/v1',
    jobEndpoint: '/job_posts',
    applicantEndpoint: '/candidates',
    authType: 'Basic',
    fullLogoUrl: '/images/ats/greenhouse-logo-full.png',
    logoUrl: '/images/ats/greenhouse-logo.png',
  },
  {
    id: '2',
    name: 'Lever',
    baseUrl: 'https://api.lever.co/v1',
    jobEndpoint: '/postings',
    applicantEndpoint: '/candidates',
    authType: 'Basic',
    fullLogoUrl: '/images/ats/lever-logo-full.webp',
    logoUrl: '/images/ats/lever-logo.webp',
    supportedAuthTypes: ['Basic', 'Bearer'],
  },
  {
    id: '3',
    name: 'BambooHR',
    baseUrl: 'https://api.bamboohr.com/api/gateway.php/<subdomain>/v1',
    jobEndpoint: '/jobs',
    applicantEndpoint: '/applicants',
    authType: 'Basic',
    fullLogoUrl: '/images/ats/bambooHR-logo-full.png',
    logoUrl: '/images/ats/bambooHR-logo.png',
  },
  {
    id: '4',
    name: 'ClearCompany',
    baseUrl: 'https://api.clearcompany.com/v2',
    jobEndpoint: '/requisitions',
    applicantEndpoint: '/candidates',
    authType: 'Bearer',
    fullLogoUrl: '/images/ats/clearco-logo-full.png',
    logoUrl: '/images/ats/clearco-logo.png',
  },
  {
    id: '8',
    name: 'Ashby',
    baseUrl: 'https://api.ashbyhq.com',
    jobEndpoint: '/jobs',
    applicantEndpoint: '/applications',
    authType: 'Bearer',
    fullLogoUrl: '/images/ats/ashby-logo-full.webp',
    logoUrl: '/images/ats/ashby-logo.png',
  },
  // {
  //   id: '6',
  //   name: 'RecruitCRM',
  //   baseUrl: 'https://api.recruitcrm.io/v1',
  //   jobEndpoint: '/jobs',
  //   applicantEndpoint: '/candidates',
  //   authType: 'Bearer',
  // },
  // {
  //   id: '7',
  //   name: 'Manatal',
  //   baseUrl: 'https://api.manatal.com/open/v3',
  //   jobEndpoint: '/jobs',
  //   applicantEndpoint: '/candidates',
  //   authType: 'Bearer',
  // },
  {
    id: '12',
    name: 'Workable',
    baseUrl: 'https://<subdomain>.workable.com/spi/v3',
    jobEndpoint: '/jobs',
    applicantEndpoint: '/candidates',
    authType: 'Bearer',
    fullLogoUrl: '/images/ats/workable-logo-full.png',
    logoUrl: '/images/ats/workable-logo.png',
  },
];

// Extend the CompanyData type to include the new fields
type ExtendedCompanyData = {
  id: string;
  clientId: string;
  companyName: string;
  companyWebsite: string;
  size: string;
  contactName: string;
  contactEmail: string;
  phoneNumber: string;
  industry: string;
  description: string;
  companyValues?: string[];
  location: string;
  logo: string;
  layoutPreference?: string;
  primaryColor?: string;
  secondaryColor?: string;
  accentColor?: string;
  heroImage?: string;
  featuredImages?: string[];
  customCss?: string;
  atsProvider?: string;
  atsApiKey?: string;
  atsSubdomain?: string;
  atsCompanyId?: string;
  atsAuthType?: string;
  atsConfig?: Record<string, any>;
  isDemoMode?: boolean;
  footerImage?: string;
};

const companySizes = [
  '1-10 employees',
  '11-50 employees',
  '51-200 employees',
  '201-500 employees',
  '501-1000 employees',
  '1000+ employees',
];

const SettingsPage: React.FC = () => {
  const router = useRouter();
  const { company, updateCompanyData } = useJobs();
  const { user, isLoading: userLoading } = useUser();

  // Tab configuration for the TabComponent
  const tabs = [
    {
      id: 'company-profile',
      label: 'Company Profile',
      icon: <Settings className="w-5 h-5" />,
    },
    {
      id: 'team-members',
      label: 'Team Members',
      icon: <Users className="w-5 h-5" />,
    },
    {
      id: 'subscription-payments',
      label: 'Subscription',
      icon: <CreditCard className="w-5 h-5" />,
    },
    {
      id: 'social-media',
      label: 'Social Media',
      icon: <Share2 className="w-5 h-5" />,
    },
    {
      id: 'ats-config',
      label: 'ATS Integration',
      icon: <Database className="w-5 h-5" />,
    },
    {
      id: 'public-profile',
      label: 'Public Profile',
      icon: <Layout className="w-5 h-5" />,
    },
  ];

  const [activeTab, setActiveTab] = useState(() => {
    // Initialize from URL if available, otherwise default to 'company-profile'
    return (
      (typeof window !== 'undefined' && new URLSearchParams(window.location.search).get('tab')) ||
      'company-profile'
    );
  });
  const [companyData, setCompanyData] = useState<ExtendedCompanyData | null>(null);

  // Sync URL changes with active tab
  useEffect(() => {
    const tabFromUrl = router.query.tab as string;
    if (tabFromUrl && tabs.some(tab => tab.id === tabFromUrl)) {
      setActiveTab(tabFromUrl);
    }
  }, [router.query.tab]);

  const [formData, setFormData] = useState({
    companyName: '',
    companyWebsite: '',
    size: '',
    contactName: '',
    contactEmail: '',
    phoneNumber: '',
    industry: '',
    footerImage: '',
    description: '',
    companyValues: [] as string[],
    location: '',
    logo: '',
    layoutPreference: 'default',
    // New fields for public profile customization
    primaryColor: '#6366f1', // Default indigo color
    secondaryColor: '#4f46e5',
    accentColor: '#4338ca',
    heroImage: '',
    featuredImages: [] as string[],
    customCss: '',
    // New fields for ATS configuration
    atsProvider: '',
    atsApiKey: '',
    atsSubdomain: '',
    atsCompanyId: '',
    atsAuthType: 'Basic',
    atsConfig: {} as Record<string, any>,
    // Demo mode setting
    isDemoMode: true,
  });
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [previewUrl, setPreviewUrl] = useState('');
  const [isFetchingDescription, setIsFetchingDescription] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const currentUser = useCurrentUser();

  // Check if the current user is an employer or admin
  const isEmployer = useMemo(() => {
    if (!user) return false;

    // Helper to normalize role strings
    const normalizeRole = (role: string) => role?.toLowerCase().replace(/\s+/g, '-');

    // Check direct role property
    if (
      user.role &&
      typeof user.role === 'string' &&
      (normalizeRole(user.role) === UserRole.EMPLOYER ||
        normalizeRole(user.role) === UserRole.ADMIN)
    )
      return true;

    // Check pendingRole property (for users who haven't completed onboarding)
    if (
      (user as any).pendingRole &&
      typeof (user as any).pendingRole === 'string' &&
      (normalizeRole((user as any).pendingRole) === UserRole.EMPLOYER ||
        normalizeRole((user as any).pendingRole) === UserRole.ADMIN)
    )
      return true;

    // Check localStorage for cached role
    try {
      if (user.sub) {
        const cachedRoleData = localStorage.getItem(`userRole_${user.sub}`);
        if (cachedRoleData) {
          const { role } = JSON.parse(cachedRoleData);
          if (
            role &&
            typeof role === 'string' &&
            (normalizeRole(role) === UserRole.EMPLOYER || normalizeRole(role) === UserRole.ADMIN)
          )
            return true;
        }
      }
    } catch (error) {
      console.error('Error checking cached role:', error);
    }

    return false;
  }, [user]);

  // Redirect non-employers/non-admins to dashboard
  useEffect(() => {
    if (!userLoading && user && !isEmployer) {
      router.push('/dashboard');
    }
  }, [user, userLoading, isEmployer, router]);

  // Show loading while checking user role
  if (userLoading || !user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[80vh]">
          <ColorfulSmokeyOrbLoader />
        </div>
      </AppLayout>
    );
  }

  // Don't render anything for non-employers/non-admins (they will be redirected)
  if (!isEmployer) {
    return null;
  }

  useEffect(() => {
    const fetchCompanyData = async () => {
      if (!currentUser) {
        return;
      }
      try {
        setIsLoading(true);
        const fetchedCompanyData = await apiHelper.get<ExtendedCompanyData>(
          `/companies/${decodeURIComponent(currentUser?.sub)}/by-user-profile`
        );
        setCompanyData(fetchedCompanyData);

        // If there's an atsConfig, populate the ATS form fields from it
        let atsProvider = '';
        let atsApiKey = '';
        let atsSubdomain = '';
        let atsCompanyId = '';
        let atsAuthType = 'Basic';

        if (fetchedCompanyData.atsConfig && Object.keys(fetchedCompanyData.atsConfig).length > 0) {
          const config = fetchedCompanyData.atsConfig;

          // Special handling for providers - match by name primarily to avoid ID conflicts
          if (config.provider) {
            // Find the ATS provider by name first
            const providerByName = AtsConfigurations.find(
              ats => ats.name.toLowerCase() === config.provider.toLowerCase()
            );

            if (providerByName) {
              atsProvider = providerByName.id;
            } else {
              // Fallback to ID if name not found
              atsProvider = config.providerId || '';
            }
          } else {
            atsProvider = config.providerId || '';
          }

          atsApiKey = config.apiKey || '';
          atsSubdomain = config.subdomain || '';
          atsCompanyId = config.companyId || '';
          atsAuthType = config.authType || 'Basic';
        }

        setFormData({
          companyName: fetchedCompanyData.companyName || '',
          companyWebsite: fetchedCompanyData.companyWebsite || '',
          size: fetchedCompanyData.size || '',
          contactName: (fetchedCompanyData as any).contactName || '',
          contactEmail: fetchedCompanyData.contactEmail || '',
          phoneNumber: fetchedCompanyData.phoneNumber || '',
          industry: fetchedCompanyData.industry || '',
          description: fetchedCompanyData.description || '',
          companyValues: fetchedCompanyData.companyValues || [],
          location: fetchedCompanyData.location || '',
          logo: fetchedCompanyData.logo || '',
          layoutPreference: fetchedCompanyData.layoutPreference || 'default',
          // Load public profile customization fields if they exist
          primaryColor: fetchedCompanyData.primaryColor || '#6366f1',
          secondaryColor: fetchedCompanyData.secondaryColor || '#4f46e5',
          accentColor: fetchedCompanyData.accentColor || '#4338ca',
          heroImage: fetchedCompanyData.heroImage || '',
          featuredImages: fetchedCompanyData.featuredImages || [],
          customCss: fetchedCompanyData.customCss || '',
          // Load ATS configuration fields if they exist
          atsProvider: atsProvider || fetchedCompanyData.atsProvider || '',
          atsApiKey: atsApiKey || fetchedCompanyData.atsApiKey || '',
          atsSubdomain: atsSubdomain || fetchedCompanyData.atsSubdomain || '',
          atsCompanyId: atsCompanyId || fetchedCompanyData.atsCompanyId || '',
          atsAuthType: atsAuthType || fetchedCompanyData.atsAuthType || 'Basic',
          atsConfig: fetchedCompanyData.atsConfig || {},
          // Load demo mode setting
          isDemoMode:
            fetchedCompanyData.isDemoMode !== undefined ? fetchedCompanyData.isDemoMode : true,
          footerImage: fetchedCompanyData.footerImage || '',
        });

        // Set preview URL for the company profile
        if (fetchedCompanyData.companyName) {
          setPreviewUrl(`/company-profile/${slugify(fetchedCompanyData.companyName)}`);
        }
      } catch (error) {
        console.error('Error fetching company data:', error);
        showToast({
          message: 'Failed to load company data',
          isSuccess: false,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchCompanyData();
  }, [currentUser?.sub]);

  if (!currentUser) {
    return <ColorfulSmokeyOrbLoader text="Loading user data" />;
  }

  const handleChange = (field: string, value: string | string[] | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    setIsEditing(true);
  };

  // Handler specifically for form inputs that update the ATS fields
  const handleAtsChange = (field: string, value: string) => {
    handleChange(field, value);

    // If we've selected a new ATS provider, clear any existing subdomain and companyId
    if (field === 'atsProvider') {
      const selectedAts = AtsConfigurations.find(ats => ats.id === value);
      if (selectedAts) {
        // Only clear fields if they are not needed for the selected provider
        if (!selectedAts.baseUrl.includes('<subdomain>')) {
          setFormData(prev => ({ ...prev, atsSubdomain: '' }));
        }
        if (!selectedAts.baseUrl.includes('{companyId}')) {
          setFormData(prev => ({ ...prev, atsCompanyId: '' }));
        }
      }
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      // Create a proper structure for atsConfig if we're in the ATS tab
      const dataToSave = { ...formData };

      // If we're saving ATS configuration, create a structured atsConfig object
      if (activeTab === 'ats-config' && formData.atsProvider) {
        // Find the selected ATS configuration
        const selectedAts = AtsConfigurations.find(ats => ats.id === formData.atsProvider);

        if (selectedAts) {
          // Create structured atsConfig data
          const atsConfig = {
            provider: selectedAts.name,
            providerId: selectedAts.id,
            apiKey: formData.atsApiKey,
            baseUrl: selectedAts.baseUrl
              .replace('<subdomain>', formData.atsSubdomain || '')
              .replace('{companyId}', formData.atsCompanyId || ''),
            jobEndpoint: selectedAts.jobEndpoint,
            applicantEndpoint: selectedAts.applicantEndpoint,
            authType: formData.atsAuthType || selectedAts.authType,
            subdomain: formData.atsSubdomain || null,
            companyId: formData.atsCompanyId || null,
          };

          // Update the data to save with the atsConfig
          dataToSave.atsConfig = atsConfig;
        }
      }

      await apiHelper.patch(`/companies`, dataToSave);
      setIsEditing(false);
      showToast({
        message: 'Company settings updated successfully',
        isSuccess: true,
      });
    } catch (error) {
      console.error('Error saving company data:', error);
      showToast({
        message: 'Failed to update company settings',
        isSuccess: false,
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (company) {
      // If there's an atsConfig in the company object, populate the ATS form fields from it
      let atsProvider = '';
      let atsApiKey = '';
      let atsSubdomain = '';
      let atsCompanyId = '';

      if ((company as any).atsConfig && Object.keys((company as any).atsConfig).length > 0) {
        const config = (company as any).atsConfig;
        atsProvider = config.providerId || '';
        atsApiKey = config.apiKey || '';
        atsSubdomain = config.subdomain || '';
        atsCompanyId = config.companyId || '';
      }

      setFormData({
        companyName: company.companyName || '',
        companyWebsite: company.companyWebsite || '',
        size: company.size || '',
        contactName: (company as any).contactName || '',
        contactEmail: company.contactEmail || '',
        phoneNumber: company.phoneNumber || '',
        industry: company.industry || '',
        description: company.description || '',
        companyValues: (company as any).companyValues || [],
        location: company.location || '',
        logo: company.logo || '',
        layoutPreference: (company as any).layoutPreference || 'default',
        primaryColor: (company as any).primaryColor || '#6366f1',
        secondaryColor: (company as any).secondaryColor || '#4f46e5',
        accentColor: (company as any).accentColor || '#4338ca',
        heroImage: (company as any).heroImage || '',
        featuredImages: (company as any).featuredImages || [],
        customCss: (company as any).customCss || '',
        atsProvider: atsProvider || (company as any).atsProvider || '',
        atsApiKey: atsApiKey || (company as any).atsApiKey || '',
        atsSubdomain: atsSubdomain || (company as any).atsSubdomain || '',
        atsCompanyId: atsCompanyId || (company as any).atsCompanyId || '',
        atsAuthType: (company as any).atsAuthType || 'Basic',
        atsConfig: (company as any).atsConfig || {},
        isDemoMode: (company as any).isDemoMode !== undefined ? (company as any).isDemoMode : true,
        footerImage: (company as any).footerImage || '',
      });
    }
    setIsEditing(false);
    showToast({
      message: 'Changes discarded',
      isSuccess: false,
    });
  };

  const handleFetchDescription = async () => {
    try {
      setIsFetchingDescription(true);
      const response = await apiHelper.post<{ summary: string }>(`/jobs/summarize`, {
        url: formData.companyWebsite,
      });
      setFormData(prev => ({
        ...prev,
        description: response.summary,
      }));
      setIsEditing(true); // Set editing state to true when description is generated
      showToast({
        message: 'Company description generated successfully',
        isSuccess: true,
      });
    } catch (error) {
      console.error('Error generating company description:', error);
      showToast({
        message: 'Failed to generate company description',
        isSuccess: false,
      });
    } finally {
      setIsFetchingDescription(false);
    }
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;
    const file = e.target.files[0];
    if (file.size > 5 * 1024 * 1024) {
      showToast({
        message: 'File size exceeds 5MB',
        isSuccess: false,
      });
      return;
    }

    try {
      setIsUploading(true);
      const formDataObj = new FormData();
      formDataObj.append('file', file);
      const { imageUrl } = await apiHelper.post('/companies/upload-logo', formDataObj, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: progressEvent => {
          const progress = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 0));
          setUploadProgress(progress);
        },
      });
      setFormData(prev => ({
        ...prev,
        logo: imageUrl,
      }));
      showToast({
        message: 'Company logo uploaded successfully',
        isSuccess: true,
      });
    } catch (error) {
      console.error('Error uploading company logo:', error);
      showToast({
        message: 'Failed to upload company logo',
        isSuccess: false,
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const companySizeOptions = companySizes.map(size => ({ value: size, label: size }));

  // Get profile style settings
  const getProfileStyleSettings = (): ProfileStyleSettings => {
    return {
      layoutPreference: formData.layoutPreference,
      primaryColor: formData.primaryColor,
      secondaryColor: formData.secondaryColor,
      accentColor: formData.accentColor,
      heroImage: formData.heroImage,
      featuredImages: formData.featuredImages,
      customCss: formData.customCss,
      logo: formData.logo,
      companyName: formData.companyName,
      industry: formData.industry,
      footerImage: formData.footerImage,
    };
  };

  /**
   * Test the ATS connection with the provided credentials
   */
  const testConnection = async (config: {
    provider: string;
    apiKey: string;
    subdomain?: string;
    companyId?: string;
    authType?: string;
  }): Promise<boolean> => {
    try {
      setIsTestingConnection(true);
      const response = await apiHelper.post<{ success: boolean; message: string }>(
        '/ats/test-connection',
        config
      );
      return response.success;
    } catch (error) {
      console.error('Error testing ATS connection:', error);
      return false;
    } finally {
      setIsTestingConnection(false);
    }
  };

  const companyId = company?.clientId || currentUser?.sub || '';

  // Render the tab content based on the active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case 'public-profile':
        return (
          <PublicProfileSettingsV2
            initialSettings={getProfileStyleSettings()}
            onChange={handleChange}
            companyId={companyData?.id || ''}
            clientId={companyData?.clientId || ''}
            handleSave={handleSave}
            handleCancel={handleCancel}
            isEditing={isEditing}
          />
        );

      case 'ats-config':
        return (
          <ATSConfiguration
            formData={{
              atsProvider: formData.atsProvider,
              atsApiKey: formData.atsApiKey,
              atsSubdomain: formData.atsSubdomain,
              atsCompanyId: formData.atsCompanyId,
              atsAuthType: formData.atsAuthType,
              atsConfig: formData.atsConfig,
            }}
            handleChange={handleAtsChange}
            handleSave={handleSave}
            handleCancel={handleCancel}
            isTestingConnection={isTestingConnection}
            atsConfigurations={AtsConfigurations}
            testConnection={testConnection}
            isEditing={isEditing}
          />
        );

      case 'social-media':
        return <SocialMediaConnectors showOnlyLinkedIn={true} />;

      case 'subscription-payments':
        return <SubscriptionPayments clientId={companyId} />;

      case 'team-members':
        // Don't render TeamManagement until companyData is loaded with the actual UUID
        if (!companyData?.id) {
          return (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
            </div>
          );
        }
        return <TeamManagement companyId={companyData.id} />;

      default: // company-profile
        return (
          <CompanyProfile
            formData={{
              companyName: formData.companyName,
              companyWebsite: formData.companyWebsite,
              size: formData.size,
              contactName: formData.contactName,
              contactEmail: formData.contactEmail,
              phoneNumber: formData.phoneNumber,
              industry: formData.industry,
              description: formData.description,
              companyValues: formData.companyValues,
              location: formData.location,
              logo: formData.logo,
              isDemoMode: formData.isDemoMode,
            }}
            handleChange={handleChange}
            handleSave={handleSave}
            handleCancel={handleCancel}
            isEditing={isEditing}
            handleFetchDescription={handleFetchDescription}
            isFetchingDescription={isFetchingDescription}
            handleFileUpload={handleFileUpload}
            isUploading={isUploading}
            uploadProgress={uploadProgress}
            companySizeOptions={companySizeOptions}
          />
        );
    }
  };

  return (
    <AppLayout>
      <div className="h-full flex flex-col relative">
        {/* Full-width Header */}
        <PageHeader
          variant="fullwidth"
          title="Company Settings"
          description="Manage your company profile and settings"
          icon={Settings}
        />

        {/* Spacer for fixed header */}
        <div className="h-[200px] flex-shrink-0"></div>

        {/* Tabs Navigation */}
        <div className="sticky top-0 z-40 flex-none border-b border-gray-300/10 bg-background/95 backdrop-blur h-12 sm:h-14">
          <div className="container mx-auto px-4 sm:px-6 h-full flex items-center">
            {isLoading ? (
              <ColorfulSmokeyOrbLoader text="Loading company profile" />
            ) : (
              <TabNavigation
                tabs={tabs}
                activeTab={activeTab}
                onTabChange={setActiveTab}
                variant="gradient"
                gradientColors={{
                  from: 'from-purple-600',
                  to: 'to-pink-600',
                }}
                updateUrl={true}
                queryParamName="tab"
                showBorder={true}
                showSeparators={true}
                mobileCollapse={true}
                className="w-full h-full flex-1 text-white"
              />
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-auto">
          <div className="container mx-auto px-4 sm:px-6 py-4 sm:py-8">
            {!isLoading && <div>{renderTabContent()}</div>}
          </div>
        </div>

        {/* Global Save Changes Slider */}
        <SaveChangesSlider
          isVisible={
            isEditing &&
            !['subscription-payments', 'team-members', 'social-media'].includes(activeTab)
          }
          onSave={handleSave}
          onCancel={handleCancel}
          isSaving={isSaving}
          saveText="Save Changes"
          cancelText="Discard"
        />
      </div>
    </AppLayout>
  );
};

export default SettingsPage;
