import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle } from 'lucide-react';

interface CollapsedViewProps {
  isProcessing: boolean;
  title: string;
  onClick: () => void;
}

export const CollapsedView: React.FC<CollapsedViewProps> = ({ isProcessing, title, onClick }) => {
  return (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      exit={{ y: 20, opacity: 0 }}
      className="fixed bottom-4 left-4"
      style={{ pointerEvents: 'auto' }}
    >
      <button
        type="button"
        onClick={onClick}
        className={`${
          isProcessing ? 'bg-pink-700 hover:bg-pink-600' : 'bg-green-700 hover:bg-green-600'
        } text-white p-3 rounded-full shadow-lg flex items-center gap-2`}
      >
        {isProcessing ? (
          <div className="relative w-5 h-5">
            <div className="absolute inset-0 rounded-full border-2 border-white border-t-transparent animate-spin"></div>
          </div>
        ) : (
          <CheckCircle className="text-white h-5 w-5" />
        )}
        <span>{isProcessing ? `${title} in Progress` : `${title} Complete`}</span>
      </button>
    </motion.div>
  );
};
