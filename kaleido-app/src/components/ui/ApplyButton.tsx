import { Apply<PERSON>uttonP<PERSON>, JobData, JobWithApplicationStatus } from './ui.types';
import { Check, Rocket } from 'lucide-react';
import React, { useEffect, useState } from 'react';

import ApplicationReasonModal from './ApplicationReasonModal';
import { EventCategory } from '@/services/intercomEvents';
import { JobSeekerSetupSlider } from '../JobSeeker/JobSeekerSetupSlider';
import SmallLoader from '../Layouts/SmallLoader';
import apiClient from '@/lib/apiHelper';
import { cn } from '@/lib/utils';
import { showToast } from '@/components/Toaster';
import useIntercom from '@/hooks/useIntercom';
import { useJobSearchStore } from '@/stores/jobSearchStore';
import { useRouter } from 'next/navigation';
import useUser from '@/hooks/useUser';

// Constants for storing application intent in localStorage
const PENDING_JOB_APPLICATION_KEY = 'headstart_pending_job_application';
const REDIRECT_URL_KEY = 'headstart_redirect_after_auth';

// Color scheme mapping
const colorSchemeMap = {
  emerald: {
    bg: 'bg-gradient-to-r from-emerald-500 via-green-500 to-emerald-600 hover:from-emerald-600 hover:via-green-500 hover:to-emerald-500',
    border: 'border-emerald-400/20 hover:border-emerald-400/40',
    shadow: 'shadow-emerald-500/20 hover:shadow-emerald-500/30',
    pulse: 'bg-gradient-to-r from-emerald-400/0 via-emerald-400/10 to-emerald-400/0',
  },
  indigo: {
    bg: 'bg-gradient-to-r from-indigo-500 via-indigo-600 to-indigo-700 hover:from-indigo-600 hover:via-indigo-700 hover:to-indigo-800',
    border: 'border-indigo-400/20 hover:border-indigo-400/40',
    shadow: 'shadow-indigo-500/20 hover:shadow-indigo-500/30',
    pulse: 'bg-gradient-to-r from-indigo-400/0 via-indigo-400/10 to-indigo-400/0',
  },
  blue: {
    bg: 'bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 hover:from-blue-600 hover:via-blue-700 hover:to-blue-800',
    border: 'border-blue-400/20 hover:border-blue-400/40',
    shadow: 'shadow-blue-500/20 hover:shadow-blue-500/30',
    pulse: 'bg-gradient-to-r from-blue-400/0 via-blue-400/10 to-blue-400/0',
  },
  purple: {
    bg: 'bg-gradient-to-r from-purple-500 via-purple-600 to-purple-700 hover:from-purple-600 hover:via-purple-700 hover:to-purple-800',
    border: 'border-purple-400/20 hover:border-purple-400/40',
    shadow: 'shadow-purple-500/20 hover:shadow-purple-500/30',
    pulse: 'bg-gradient-to-r from-purple-400/0 via-purple-400/10 to-purple-400/0',
  },
  rose: {
    bg: 'bg-gradient-to-r from-rose-500 via-rose-600 to-rose-700 hover:from-rose-600 hover:via-rose-700 hover:to-rose-800',
    border: 'border-rose-400/20 hover:border-rose-400/40',
    shadow: 'shadow-rose-500/20 hover:shadow-rose-500/30',
    pulse: 'bg-gradient-to-r from-rose-400/0 via-rose-400/10 to-rose-400/0',
  },
};

const sizeClasses = {
  sm: 'px-4 py-2 text-sm',
  md: 'px-6 py-3 text-base',
  lg: 'px-8 py-4 text-lg',
};

// Type guard to check if job has matchDetails property - kept for reference
// function hasMatchDetails(job: JobWithApplicationStatus | JobData): job is JobWithApplicationStatus {
//   return 'matchDetails' in job && job.matchDetails !== undefined;
// }

// Simple function to check if application reason modal should be shown based on backend data
const shouldShowApplicationReasonModal = (
  jobData: JobData | JobWithApplicationStatus | null
): boolean => {
  if (!jobData) return false;

  // Use the match percentage provided by the backend
  const matchPercentage = jobData.matchPercentage || jobData.matchDetails?.overallScore || 0;

  // Show modal for low match scores (< 50%)
  const shouldShow = matchPercentage < 50;

  return shouldShow;
};

export const ApplyButton = ({
  className,
  variant = 'default',
  jobId,
  onSuccess,
  onError,
  alreadyApplied = false,
  isPublic = false,
  disabled,
  colorScheme = 'emerald',
  size = 'md',
  icon = <Rocket className="h-6 w-6" />,
  handleLocalApply,
  job,
  ...props
}: ApplyButtonProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [showSetupSlider, setShowSetupSlider] = useState(false);
  const [applicationReason, setApplicationReason] = useState('');
  const [profileValidationData, setProfileValidationData] = useState<any>(null);
  const {
    jobs,
    setSelectedJobId,
    appliedJobIds,
    markJobAsApplied,
    checkUserEligibility,
    isEligibleToApply,
  } = useJobSearchStore();
  const { user, isLoading: isUserLoading } = useUser();
  const router = useRouter();

  // Get Intercom tracking
  const { trackEvent } = useIntercom();

  // Check if job is already applied based on multiple sources
  const isApplied =
    alreadyApplied || appliedJobIds.includes(jobId) || job?.alreadyApplied || job?.hasApplied;

  // Check user eligibility when user changes
  useEffect(() => {
    if (user?.sub) {
      checkUserEligibility(user.sub);
    }
  }, [user, checkUserEligibility]);

  // Update appliedJobIds in store if job is already applied from the job object
  useEffect(() => {
    if (job && (job.alreadyApplied || job.hasApplied) && !appliedJobIds.includes(jobId)) {
      markJobAsApplied(jobId);
    }
  }, [job, jobId, appliedJobIds, markJobAsApplied]);

  // Store application intent and current URL
  const storeApplicationIntent = () => {
    try {
      localStorage.setItem(PENDING_JOB_APPLICATION_KEY, jobId);
      localStorage.setItem(REDIRECT_URL_KEY, window.location.href);
    } catch (error) {
      console.error('Error storing application intent:', error);
    }
  };

  // Check if user is logged in and redirect if not
  const checkUserAndRedirect = () => {
    if (!user) {
      storeApplicationIntent();
      const returnTo = encodeURIComponent(`/dashboard?role=job-seeker`);
      router.push(`/api/auth/login?returnTo=${returnTo}`);
      return true;
    }
    return false;
  };

  // Handle job application
  const handleApply = async (jobId: string, reason?: string) => {
    if (!user?.sub) return;

    // Don't optimistically mark as applied - wait for actual success

    try {
      // Try to apply directly - the backend will validate and may throw an error
      const applicationResult = await apiClient.post(`/job-seekers/apply/${jobId}`, {
        userId: user.sub,
        coverLetter: '', // Optional cover letter
        applicationReason: reason || '', // Include application reason if provided
      });

      // If we get here, application was successful
      // Now mark as applied after successful API call
      markJobAsApplied(jobId);

      showToast({
        message: 'Successfully applied to job!',
        isSuccess: true,
      });

      // Track successful job application
      trackEvent('job_application_submitted', EventCategory.JOB_MANAGEMENT, {
        jobId,
        userId: user.sub,
        hasReason: !!reason && reason.length > 0,
        reasonLength: reason ? reason.length : 0,
        jobTitle: job?.jobType || '',
      });

      return applicationResult;
    } catch (err: any) {
      // Check if this is a validation error - handle both Axios error and ApiError
      const isValidationError =
        (err.response?.status === 400 && err.response?.data?.validationResult) || // Original Axios error
        (err.status === 400 && err.data?.validationResult); // ApiError from apiHelper

      if (isValidationError) {
        const validateResponse = err.response?.data?.validationResult || err.data?.validationResult;

        // Store application intent and validation data
        storeApplicationIntent();
        setProfileValidationData(validateResponse);

        // Show the setup slider instead of redirecting
        setShowSetupSlider(true);
        return undefined; // Signal that a modal was shown
      }

      console.error('Error applying to job:', err);
      showToast({
        message: 'Failed to apply to job. Please try again.',
        isSuccess: false,
      });

      // Track application error
      trackEvent('job_application_error', EventCategory.ERROR, {
        jobId,
        userId: user?.sub || 'unknown',
        errorMessage: err instanceof Error ? err.message : 'Unknown error',
        hasReason: !!reason && reason.length > 0,
      });

      throw err;
    }
  };

  // Handle application reason submission
  const handleReasonSubmit = async (reason: string) => {
    setApplicationReason(reason);
    setShowReasonModal(false);
    setIsLoading(true);

    // Proceed with the application including the reason
    try {
      // Set the selected job ID in the store
      setSelectedJobId(jobId);

      // Find the job in the store if not provided directly
      const jobData = job || jobs.filter(j => j.id === jobId)[0];
      if (!jobData && !jobs.some(j => j.id === jobId)) {
        throw new Error('Job not found');
      }

      // Apply for the job with the reason
      await handleApply(jobId, reason);
      onSuccess?.();
    } catch (error) {
      console.error('Error applying for job:', error);
      onError?.(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle setup slider completion
  const handleSetupComplete = () => {
    setShowSetupSlider(false);
    setProfileValidationData(null);

    // Try to apply for the job again after profile completion
    if (jobId) {
      handleClick();
    }
  };

  // Handle setup slider close
  const handleSetupSliderClose = () => {
    setShowSetupSlider(false);
    setProfileValidationData(null);
  };

  const handleClick = async () => {
    if (disabled || isLoading || isApplied) return;

    // Wait for user loading to complete before proceeding
    if (isUserLoading) {
      return;
    }

    // If user is logged in but not eligible, show a message
    if (user?.sub && isEligibleToApply === false) {
      showToast({
        message: 'Only job seekers and graduates can apply for jobs.',
        isSuccess: false,
      });
      return;
    }

    // If we have a local apply handler, use that
    if (handleLocalApply) {
      handleLocalApply();
      return;
    }

    // Handle authentication check
    if (checkUserAndRedirect()) return;

    // Set loading state when starting application process
    setIsLoading(true);

    // Find the job in the store if not provided directly
    const jobData = job || jobs.filter(j => j.id === jobId)[0];

    // Check if reason modal should be shown based on match percentage
    const shouldShowModal = shouldShowApplicationReasonModal(jobData);

    if (shouldShowModal) {
      // Reset loading state since we're showing modal instead
      setIsLoading(false);

      // Show the reason modal for low match percentage
      setShowReasonModal(true);

      // Track reason modal shown
      trackEvent('job_application_reason_modal_shown', EventCategory.JOB_MANAGEMENT, {
        jobId,
        userId: user?.sub || 'unknown',
        matchPercentage: jobData?.matchPercentage || 0,
        jobTitle: jobData?.jobType || '',
      });

      return;
    }

    // If match score is good, proceed with normal application
    try {
      // Set the selected job ID in the store
      setSelectedJobId(jobId);

      if (!jobData && !jobs.some(j => j.id === jobId)) {
        throw new Error('Job not found');
      }

      // Apply for the job
      await handleApply(jobId);
      onSuccess?.();
    } catch (error) {
      console.error('Error applying for job:', error);
      onError?.(error);
    } finally {
      // Reset loading state
      setIsLoading(false);
    }
  };

  // Get color scheme styles
  const colors =
    colorScheme && colorSchemeMap[colorScheme]
      ? colorSchemeMap[colorScheme]
      : colorSchemeMap.emerald;

  const buttonClasses = cn(
    'relative overflow-hidden rounded-full border transition-all duration-300',
    'inline-flex items-center justify-center gap-2',
    'text-white font-medium shadow-lg',
    colors.bg,
    colors.border,
    colors.shadow,
    sizeClasses[size],
    {
      'opacity-70 cursor-not-allowed':
        disabled || isLoading || isApplied || isEligibleToApply === false,
      'hover:scale-105 active:scale-95':
        !disabled && !isLoading && !isApplied && isEligibleToApply !== false,
    }
  );

  // Don't render the button at all if user is logged in and not eligible
  if (user?.sub && isEligibleToApply === false) {
    return null;
  }

  // Find the job data for the modal
  const jobData = jobs.filter(j => j.id === jobId)[0];

  return (
    <>
      <button
        onClick={handleClick}
        disabled={disabled || isLoading || isApplied || isEligibleToApply === false}
        className={buttonClasses}
        {...props}
      >
        {isLoading ? (
          <>
            <SmallLoader size="xs" />
            Applying...
          </>
        ) : isApplied ? (
          <>
            <Check className="h-5 w-5" />
            Applied
          </>
        ) : (
          <>
            {React.cloneElement(icon as React.ReactElement, {
              className: cn(
                (icon as React.ReactElement).props.className || '',
                'h-6 w-6 mr-2 animate-pulse-slow'
              ),
            })}
            Apply Now
          </>
        )}
        {!disabled && !isLoading && !isApplied && (
          <div
            className={cn(
              'absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] ',
              colors.pulse
            )}
          />
        )}
      </button>

      {/* Application Reason Modal */}
      <ApplicationReasonModal
        isOpen={showReasonModal}
        onClose={() => setShowReasonModal(false)}
        onSubmit={handleReasonSubmit}
        job={jobData as JobData}
      />

      {/* Job Seeker Setup Slider */}
      {showSetupSlider && (
        <JobSeekerSetupSlider
          onComplete={handleSetupComplete}
          onClose={handleSetupSliderClose}
          initialData={profileValidationData?.record}
          validationResponse={profileValidationData}
        />
      )}
    </>
  );
};
