import { useEffect, useState } from 'react';

import {
  Award,
  Briefcase,
  Calendar,
  Camera,
  ChevronLeft,
  ChevronRight,
  FileText,
  GraduationCap,
  Heart,
  Languages,
  Link,
  Mail,
  MapPin,
  Pencil,
  Phone,
  User,
  X,
} from 'lucide-react';
// Import JobSeekerSetupSlider dynamically to avoid circular dependencies
import dynamic from 'next/dynamic';
import Image from 'next/image';

import { apiClient } from '@/lib/apiHelper';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { JobSeekerProfile } from '@/types/jobSeeker';

import { showToast } from '../Toaster';
import { Button } from '../ui/button';
import SectionHeader from '../ui/SectionHeader';
import { Separator } from '../ui/separator';
import { IJobSeekerProfile, JobSeekerSetupSliderProps } from './types';

interface PersonalInfoTabProps {
  profile: JobSeekerProfile;
}

// Import JobSeekerSetupSlider dynamically to avoid circular dependencies
const JobSeekerSetupSlider = dynamic<JobSeekerSetupSliderProps>(
  () => import('./JobSeekerSetupSlider').then(mod => mod.JobSeekerSetupSlider),
  {
    ssr: false,
    loading: () => <div className="p-4 text-center">Loading editor...</div>,
  }
);

// SectionEditButton component for individual section editing
interface SectionEditButtonProps {
  onClick: () => void;
  className?: string;
}

function SectionEditButton({ onClick, className = '' }: SectionEditButtonProps) {
  return (
    <Button
      variant="ghost"
      size="sm"
      className={`absolute top-0 right-0 p-1.5 rounded-full bg-white/10 text-white/70 hover:bg-white/20 hover:text-white ${className}`}
      onClick={onClick}
    >
      <Pencil className="w-3.5 h-3.5" />
    </Button>
  );
}

// Section types for tracking which section is being edited
type EditableSection =
  | 'personal'
  | 'summary'
  | 'skills'
  | 'experience'
  | 'education'
  | 'certifications'
  | 'languages'
  | 'values'
  | null;

export function PersonalInfoTab({ profile }: PersonalInfoTabProps) {
  // State for profile data
  const [profileData, setProfileData] = useState<JobSeekerProfile>(profile);
  const [profileImage, setProfileImage] = useState<string | undefined>(profile.myProfileImage);
  const [isEditSliderOpen, setIsEditSliderOpen] = useState(false);
  const { refreshJobs, invalidateJobsCache } = useJobsStore();
  const [currentExperiencePage, setCurrentExperiencePage] = useState(1);

  // State for section editing
  const [editingSection, setEditingSection] = useState<EditableSection>(null);
  const [editFormData, setEditFormData] = useState<Partial<JobSeekerProfile>>({});

  // Update profile data when prop changes
  useEffect(() => {
    if (profile) {
      setProfileData(profile);
      setProfileImage(profile.myProfileImage);
    }
  }, [profile]);

  // Format date function
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Present';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
  };

  const handleImageUpload = async (imageUrl: string) => {
    setProfileImage(imageUrl);

    // Update the profile in the backend
    try {
      await apiClient.patch(`/job-seekers/profile`, {
        myProfileImage: imageUrl,
      });
      setProfileData(prev => ({ ...prev, myProfileImage: imageUrl }));
      showToast({
        message: 'Profile image updated successfully',
        isSuccess: true,
      });
    } catch (error) {
      console.error('Error updating profile image:', error);
      showToast({
        message: 'Failed to update profile image. Please try again.',
        isSuccess: false,
      });
    }
  };

  const fetchLatestProfile = async () => {
    try {
      const latestProfile = await apiClient.get<JobSeekerProfile>('/job-seekers/client');
      if (latestProfile) {
        setProfileData(latestProfile);
        setProfileImage(latestProfile.myProfileImage);
      }
    } catch (error) {
      console.error('Error fetching latest profile:', error);
    }
  };

  const closeEditSlider = async () => {
    setIsEditSliderOpen(false);
    // Refresh data to show updated profile
    invalidateJobsCache();
    refreshJobs(true);

    // Clear any cached data
    if (typeof localStorage !== 'undefined') {
      const keys = Object.keys(localStorage);
      const cacheKeys = keys.filter(
        key => key.startsWith('api_cache_') || key.startsWith('recent_fetch_')
      );
      cacheKeys.forEach(key => localStorage.removeItem(key));
    }

    // Fetch the latest profile data
    await fetchLatestProfile();
  };

  // Convert StandardizedProfile (JobSeekerProfile) to Partial<IJobSeekerProfile> for the slider
  const convertToSliderFormat = (p: JobSeekerProfile): Partial<IJobSeekerProfile> => {
    return {
      id: p.id,
      userId: p.userId,
      clientId: p.clientId || p.userId,
      firstName: p.firstName || '',
      lastName: p.lastName || '',
      email: p.email || '',
      phone: p.phone || '',
      location: p.location || '',
      myProfileImage: p.myProfileImage,
      summary: p.summary,
      skills: p.skills || [],
      experience: (p.experience || []).map(exp => ({
        title: exp.title,
        company: exp.company,
        startDate: exp.startDate
          ? typeof exp.startDate === 'string'
            ? new Date(exp.startDate)
            : (exp.startDate as any)
          : undefined,
        endDate: exp.endDate
          ? typeof exp.endDate === 'string'
            ? new Date(exp.endDate)
            : (exp.endDate as any)
          : undefined,
        current: false,
        location: (exp as any).location,
        description: exp.description,
        achievements: exp.achievements,
      })) as any,
      resumeUrl: p.resumeUrl,
      linkedinUrl: p.linkedinUrl,
      githubUrl: p.githubUrl,
      education: (p.education || []).map(edu => ({
        institution: edu.institution,
        degree: edu.degree,
        field: (edu as any).field,
        startDate: edu.startDate ? new Date(edu.startDate) : undefined,
        endDate: edu.endDate ? new Date(edu.endDate) : undefined,
        description: edu.description,
      })) as any,
      languages: p.languages || [],
      myValues: p.myValues || [],
      portfolioUrl: p.portfolioUrl,
      videoIntroUrl: p.videoIntroUrl,
      achievements: (p as any).achievements || [],
      recommendations: (p as any).recommendations || [],
      workAvailability: p.workAvailability
        ? ({
            immediatelyAvailable: p.workAvailability.immediatelyAvailable,
            willingToRelocate: p.workAvailability.willingToRelocate,
            noticePeriod: p.workAvailability.noticePeriod
              ? parseInt(p.workAvailability.noticePeriod as unknown as string) || 0
              : undefined,
          } as any)
        : undefined,
      hasCompletedOnboarding: p.hasCompletedOnboarding,
    };
  };

  // Convert IJobSeekerProfile back to StandardizedProfile for local state
  const toStandardProfile = (data: IJobSeekerProfile, prev: JobSeekerProfile): JobSeekerProfile => {
    const fullName = `${data.firstName || ''} ${data.lastName || ''}`.trim();
    return {
      ...prev,
      id: data.id || prev.id,
      userId: data.userId || prev.userId,
      clientId: data.clientId || prev.clientId,
      firstName: data.firstName || prev.firstName,
      lastName: data.lastName || prev.lastName,
      fullName: fullName || prev.fullName,
      email: data.email || prev.email,
      phone: data.phone ?? prev.phone,
      location: data.location ?? prev.location,
      myProfileImage: data.myProfileImage ?? prev.myProfileImage,
      summary: data.summary ?? prev.summary,
      skills: data.skills ?? prev.skills,
      experience: (data.experience as any)?.map((exp: any) => ({
        title: exp.title,
        company: exp.company,
        startDate: exp.startDate
          ? exp.startDate instanceof Date
            ? exp.startDate.toISOString()
            : String(exp.startDate)
          : undefined,
        endDate: exp.endDate
          ? exp.endDate instanceof Date
            ? exp.endDate.toISOString()
            : String(exp.endDate)
          : undefined,
        description: exp.description,
        achievements: exp.achievements,
        location: exp.location,
      })),
      education: (data.education as any)?.map((edu: any) => ({
        institution: edu.institution,
        degree: edu.degree,
        field: edu.field,
        startDate: edu.startDate
          ? edu.startDate instanceof Date
            ? edu.startDate.toISOString()
            : String(edu.startDate)
          : undefined,
        endDate: edu.endDate
          ? edu.endDate instanceof Date
            ? edu.endDate.toISOString()
            : String(edu.endDate)
          : undefined,
        description: edu.description,
        fieldOfStudy: (edu as any).fieldOfStudy,
      })),
      linkedinUrl: data.linkedinUrl ?? prev.linkedinUrl,
      githubUrl: data.githubUrl ?? prev.githubUrl,
      portfolioUrl: data.portfolioUrl ?? prev.portfolioUrl,
      videoIntroUrl: data.videoIntroUrl ?? prev.videoIntroUrl,
      languages: data.languages ?? prev.languages,
      myValues: data.myValues ?? prev.myValues,
      preferences: data.preferences ?? prev.preferences,
      workAvailability: data.workAvailability
        ? ({
            immediatelyAvailable: data.workAvailability.immediatelyAvailable,
            willingToRelocate: data.workAvailability.willingToRelocate,
            noticePeriod:
              (data.workAvailability as any).noticePeriod !== undefined
                ? String((data.workAvailability as any).noticePeriod)
                : prev.workAvailability?.noticePeriod,
          } as any)
        : prev.workAvailability,
      hasCompletedOnboarding: data.hasCompletedOnboarding ?? prev.hasCompletedOnboarding,
    } as JobSeekerProfile;
  };

  // Function to start editing a section
  const startEditingSection = (section: EditableSection) => {
    setEditingSection(section);
    // Initialize edit form data with current section data
    switch (section) {
      case 'summary':
        setEditFormData({ summary: profileData.summary });
        break;
      case 'skills':
        setEditFormData({ skills: profileData.skills });
        break;
      case 'experience':
        setEditFormData({ experience: profileData.experience });
        break;
      case 'education':
        setEditFormData({ education: profileData.education });
        break;
      case 'certifications':
        setEditFormData({ certifications: profileData.certifications });
        break;
      case 'languages':
        setEditFormData({ languages: profileData.languages });
        break;
      case 'values':
        setEditFormData({ myValues: profileData.myValues });
        break;
      case 'personal':
        setIsEditSliderOpen(true);
        break;
      default:
        setEditFormData({});
    }
  };

  // Function to cancel editing
  const cancelEditing = () => {
    setEditingSection(null);
    setEditFormData({});
  };

  // Function to update form data while editing
  const updateEditFormData = (data: Partial<JobSeekerProfile>) => {
    setEditFormData(prev => ({ ...prev, ...data }));
  };

  // Function to save section changes
  const saveSection = async () => {
    if (!editingSection) return;

    try {
      // Update the profile in the backend
      await apiClient.patch('/job-seekers/profile', editFormData);

      // Update local state
      setProfileData(prev => ({ ...prev, ...editFormData }));

      // Show success message
      showToast({
        message: `${editingSection.charAt(0).toUpperCase() + editingSection.slice(1)} updated successfully`,
        isSuccess: true,
      });

      // Reset editing state
      setEditingSection(null);
      setEditFormData({});
    } catch (error) {
      console.error(`Error updating ${editingSection}:`, error);
      showToast({
        message: `Failed to update ${editingSection}. Please try again.`,
        isSuccess: false,
      });
    }
  };

  // Experience pagination
  const itemsPerPage = 3;
  const experienceItems = profileData.experience || [];
  const totalExperiencePages = Math.ceil(experienceItems.length / itemsPerPage);
  const currentExperienceItems = experienceItems.slice(
    (currentExperiencePage - 1) * itemsPerPage,
    currentExperiencePage * itemsPerPage
  );

  const goToNextExperiencePage = () => {
    if (currentExperiencePage < totalExperiencePages) {
      setCurrentExperiencePage(currentExperiencePage + 1);
    }
  };

  const goToPrevExperiencePage = () => {
    if (currentExperiencePage > 1) {
      setCurrentExperiencePage(currentExperiencePage - 1);
    }
  };

  return (
    <div className="space-y-6 text-white relative">
      {/* JobSeekerSetupSlider for full profile editing */}
      {isEditSliderOpen && (
        <div className="fixed inset-0 z-50 bg-black/50">
          <JobSeekerSetupSlider
            onComplete={async updatedProfile => {
              // Update local state with the new profile data
              const converted = toStandardProfile(updatedProfile, profileData);
              setProfileData(converted);
              setProfileImage(converted.myProfileImage);
              // Close and refresh
              await closeEditSlider();
            }}
            onClose={async () => {
              setIsEditSliderOpen(false);
              // Refresh profile data when closing
              await fetchLatestProfile();
            }}
            initialData={convertToSliderFormat(profileData)}
            initialStep="essentials"
          />
        </div>
      )}

      {/* Profile Header with Gradient Background */}
      <div className="relative overflow-hidden mb-6 rounded-xl">
        {/* Gradient Background */}
        <div className="absolute inset-0 bg-gradient-to-r from-pink-700/30 via-purple-600/20 to-blue-500/30 backdrop-blur-sm"></div>

        {/* Content Container */}
        <div className="relative p-6 z-10">
          <div className="flex flex-col sm:flex-row items-center gap-6">
            {/* Profile Image */}
            <div className="w-24 h-24 sm:w-28 sm:h-28 flex-shrink-0 relative group">
              {profileImage ? (
                <Image
                  src={profileImage}
                  alt={`${profile.firstName} ${profile.lastName}`}
                  width={112}
                  height={112}
                  className="w-full h-full rounded-full object-cover border-2 border-white/20 shadow-lg"
                />
              ) : (
                <div className="w-full h-full rounded-full bg-gradient-to-br from-pink-700/30 to-purple-600/30 flex items-center justify-center border-2 border-white/20 shadow-lg">
                  <User className="h-12 w-12 text-white/70" />
                </div>
              )}

              {/* Upload overlay */}
              <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <label htmlFor="profile-image-upload" className="cursor-pointer">
                  <span className="sr-only">Upload profile image</span>
                  <Camera className="h-8 w-8 text-white opacity-80 hover:opacity-100 transition-opacity" />
                  <input
                    id="profile-image-upload"
                    type="file"
                    accept="image/jpeg,image/png,image/webp"
                    aria-label="Upload profile image"
                    title="Upload profile image"
                    onChange={e => {
                      const file = e.target.files?.[0];
                      if (file) {
                        // Create a simple preview
                        const reader = new FileReader();
                        reader.onload = async event => {
                          if (event.target?.result) {
                            // This would normally be handled by the ProfileImageCard component
                            // For now, we'll just show a temporary preview
                            const formData = new FormData();
                            formData.append('image', file);

                            try {
                              const endpoint = '/job-seekers/profile-image';
                              const response = await apiClient.post(endpoint, formData, {
                                headers: {
                                  'Content-Type': 'multipart/form-data',
                                },
                              });

                              // Handle the response based on API structure
                              const responseData = response as any;
                              if (responseData && responseData.imageUrl) {
                                handleImageUpload(responseData.imageUrl);
                              }
                            } catch (error) {
                              console.error('Error uploading image:', error);
                              showToast({
                                message: 'Failed to upload image. Please try again.',
                                isSuccess: false,
                              });
                            }
                          }
                        };
                        reader.readAsDataURL(file);
                      }
                    }}
                    className="sr-only"
                  />
                </label>
              </div>
            </div>

            {/* Profile Info */}
            <div className="flex-grow text-center sm:text-left">
              <h2 className="text-xl font-semibold text-white">
                {profile.firstName} {profile.lastName}
              </h2>
              <p className="text-white/80 text-sm mt-1">
                {(profile.experience && profile.experience[0]?.title) || 'Professional'}
              </p>

              <div className="flex flex-wrap gap-2 mt-3 justify-center sm:justify-start">
                {profile.email && (
                  <div className="flex items-center gap-1 text-xs text-white/80 bg-white/10 px-2 py-1 rounded-full">
                    <Mail className="w-3 h-3 text-pink-400" />
                    <span>{profile.email}</span>
                  </div>
                )}
                {profile.phone && (
                  <div className="flex items-center gap-1 text-xs text-white/80 bg-white/10 px-2 py-1 rounded-full">
                    <Phone className="w-3 h-3 text-pink-400" />
                    <span>{profile.phone}</span>
                  </div>
                )}
                {profile.location && (
                  <div className="flex items-center gap-1 text-xs text-white/80 bg-white/10 px-2 py-1 rounded-full">
                    <MapPin className="w-3 h-3 text-pink-400" />
                    <span>{profile.location}</span>
                  </div>
                )}
              </div>

              {/* Social Links */}
              {(profile.linkedinUrl || profile.githubUrl || profile.portfolioUrl) && (
                <div className="flex gap-3 mt-4 justify-center sm:justify-start">
                  {profile.linkedinUrl && (
                    <a
                      href={profile.linkedinUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-2 bg-white/10 rounded-full hover:bg-white/20 transition-colors shadow-sm"
                      aria-label="LinkedIn Profile"
                    >
                      <Link className="w-4 h-4 text-blue-400" />
                    </a>
                  )}
                  {profile.githubUrl && (
                    <a
                      href={profile.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-2 bg-white/10 rounded-full hover:bg-white/20 transition-colors shadow-sm"
                      aria-label="GitHub Profile"
                    >
                      <Link className="w-4 h-4 text-gray-300" />
                    </a>
                  )}
                  {profile.portfolioUrl && (
                    <a
                      href={profile.portfolioUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-2 bg-white/10 rounded-full hover:bg-white/20 transition-colors shadow-sm"
                      aria-label="Portfolio"
                    >
                      <Link className="w-4 h-4 text-green-400" />
                    </a>
                  )}
                </div>
              )}
            </div>

            {/* Edit Profile Button */}
            <Button
              variant="outline"
              size="sm"
              className="absolute top-4 right-4 rounded-full px-6 b py-6 bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30"
              onClick={() => startEditingSection('personal')}
            >
              <Pencil className="w-3.5 h-3.5 mr-1.5" />
              <span className="text-xs">Edit Profile</span>
            </Button>
          </div>

          {/* Availability Section */}
          {profileData.workAvailability && (
            <div className="mt-4 pt-4 border-t border-white/10 relative group">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-pink-400" />
                <span className="text-sm font-medium text-white">Availability</span>
              </div>
              <div className="flex flex-wrap gap-2 mt-2">
                {profileData.workAvailability.immediatelyAvailable && (
                  <span className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-xs flex items-center gap-1">
                    <span className="w-1.5 h-1.5 bg-green-400 rounded-full"></span>
                    Immediately Available
                  </span>
                )}
                {profileData.workAvailability.willingToRelocate && (
                  <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-xs">
                    Willing to Relocate
                  </span>
                )}
                {profileData.workAvailability.noticePeriod && (
                  <span className="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full text-xs">
                    Notice: {profileData.workAvailability.noticePeriod}
                  </span>
                )}
              </div>

              <SectionEditButton
                onClick={() => startEditingSection('personal')}
                className="opacity-0 group-hover:opacity-100 transition-opacity"
              />
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="space-y-6 px-1">
        {/* Summary Section */}
        {profileData.summary && (
          <div className="relative">
            <SectionHeader title="Summary" icon={FileText} iconClassName="text-pink-400" />
            <div className="relative group">
              <p className="text-white/80 text-sm leading-relaxed">{profileData.summary}</p>
            </div>
          </div>
        )}

        {/* Skills Section */}
        {profileData.skills && profileData.skills.length > 0 && (
          <div className="relative">
            <SectionHeader title="Skills" icon={Award} iconClassName="text-pink-400" />
            <div className="relative group">
              <div className="flex flex-wrap gap-2">
                {profileData.skills.map((skill, index) => (
                  <span
                    key={index}
                    className="bg-white/10 text-white/80 px-3 py-1 rounded-full text-xs"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          </div>
        )}
        {/* Experience Section */}
        <div className="relative">
          <SectionHeader title="Experience" icon={Briefcase} iconClassName="text-pink-400" />

          {editingSection === 'experience' ? (
            <div className="bg-white/5 p-4 rounded-lg">
              <p className="text-sm text-white/80 mb-3">
                Edit your experience in the form below. You can add, edit, or remove work
                experiences.
              </p>

              {/* This would typically be a more complex form with add/edit/remove functionality */}
              {/* For simplicity, we'll just show a message directing to the full editor */}
              <div className="bg-white/10 p-4 rounded-md text-center">
                <p className="text-white/70 text-sm">
                  Experience editing requires the full editor. Click "Edit All" to modify your work
                  history.
                </p>
              </div>

              <div className="flex justify-end gap-2 mt-3">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white/70 hover:text-white"
                  onClick={cancelEditing}
                >
                  <X className="w-3.5 h-3.5 mr-1" />
                  Cancel
                </Button>
                <Button
                  size="sm"
                  className="bg-pink-700/80 hover:bg-pink-700 text-white"
                  onClick={() => setIsEditSliderOpen(true)}
                >
                  Edit All
                </Button>
              </div>
            </div>
          ) : (
            <>
              {profileData.experience && profileData.experience.length > 0 ? (
                <div className="relative group">
                  <ul className="space-y-4">
                    {currentExperienceItems.map((exp, index) => (
                      <li key={index} className="pb-4">
                        {index !== currentExperienceItems.length - 1 && (
                          <Separator className="bg-white/10 mt-4" />
                        )}
                        <div className="flex justify-between items-start">
                          <h4 className="text-sm font-medium text-white">{exp.title}</h4>
                          <span className="text-xs text-white/60">
                            {formatDate(exp.startDate)} - {formatDate(exp.endDate)}
                          </span>
                        </div>
                        <p className="text-sm text-white/80 mt-1">{exp.company}</p>
                        {exp.description && (
                          <p className="text-xs text-white/70 mt-2 leading-relaxed">
                            {exp.description}
                          </p>
                        )}
                      </li>
                    ))}
                  </ul>

                  {/* Pagination controls */}
                  {totalExperiencePages > 1 && (
                    <div className="flex justify-between items-center mt-4">
                      <button
                        onClick={goToPrevExperiencePage}
                        disabled={currentExperiencePage === 1}
                        className={`p-1 rounded-full ${
                          currentExperiencePage === 1
                            ? 'text-white/30'
                            : 'text-white/70 hover:text-white hover:bg-white/10'
                        }`}
                      >
                        <ChevronLeft className="w-5 h-5" />
                      </button>
                      <span className="text-xs text-white/70">
                        Page {currentExperiencePage} of {totalExperiencePages}
                      </span>
                      <button
                        onClick={goToNextExperiencePage}
                        disabled={currentExperiencePage === totalExperiencePages}
                        className={`p-1 rounded-full ${
                          currentExperiencePage === totalExperiencePages
                            ? 'text-white/30'
                            : 'text-white/70 hover:text-white hover:bg-white/10'
                        }`}
                      >
                        <ChevronRight className="w-5 h-5" />
                      </button>
                    </div>
                  )}

                  <SectionEditButton
                    onClick={() => startEditingSection('experience')}
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                  />
                </div>
              ) : (
                <div className="flex items-center justify-center p-4 bg-white/5 rounded-lg">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-white/50 hover:text-white"
                    onClick={() => startEditingSection('experience')}
                  >
                    <Pencil className="w-3.5 h-3.5 mr-1.5" />
                    Add Experience
                  </Button>
                </div>
              )}
            </>
          )}
        </div>

        {/* Education Section */}
        <div className="relative">
          <SectionHeader title="Education" icon={GraduationCap} iconClassName="text-pink-400" />

          {editingSection === 'education' ? (
            <div className="bg-white/5 p-4 rounded-lg">
              <p className="text-sm text-white/80 mb-3">
                Edit your education in the form below. You can add, edit, or remove educational
                qualifications.
              </p>

              {/* This would typically be a more complex form with add/edit/remove functionality */}
              {/* For simplicity, we'll just show a message directing to the full editor */}
              <div className="bg-white/10 p-4 rounded-md text-center">
                <p className="text-white/70 text-sm">
                  Education editing requires the full editor. Click "Edit All" to modify your
                  education history.
                </p>
              </div>

              <div className="flex justify-end gap-2 mt-3">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white/70 hover:text-white"
                  onClick={cancelEditing}
                >
                  <X className="w-3.5 h-3.5 mr-1" />
                  Cancel
                </Button>
                <Button
                  size="sm"
                  className="bg-pink-700/80 hover:bg-pink-700 text-white"
                  onClick={() => setIsEditSliderOpen(true)}
                >
                  Edit All
                </Button>
              </div>
            </div>
          ) : (
            <>
              {profileData.education && profileData.education.length > 0 ? (
                <div className="relative group">
                  <ul className="space-y-4">
                    {profileData.education.map((edu, index) => (
                      <li key={index} className="pb-4">
                        {index !== profileData.education.length - 1 && (
                          <Separator className="bg-white/10 mt-4" />
                        )}
                        <div className="flex justify-between items-start">
                          <h4 className="text-sm font-medium text-white">{edu.degree}</h4>
                          <span className="text-xs text-white/60">
                            {formatDate(edu.startDate)} - {formatDate(edu.endDate)}
                          </span>
                        </div>
                        <p className="text-sm text-white/80 mt-1">{edu.institution}</p>
                        {edu.field && <p className="text-xs text-white/70 mt-1">{edu.field}</p>}
                        {edu.description && (
                          <p className="text-xs text-white/70 mt-2 leading-relaxed">
                            {edu.description}
                          </p>
                        )}
                      </li>
                    ))}
                  </ul>

                  <SectionEditButton
                    onClick={() => startEditingSection('education')}
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                  />
                </div>
              ) : (
                <div className="flex items-center justify-center p-4 bg-white/5 rounded-lg">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-white/50 hover:text-white"
                    onClick={() => startEditingSection('education')}
                  >
                    <Pencil className="w-3.5 h-3.5 mr-1.5" />
                    Add Education
                  </Button>
                </div>
              )}
            </>
          )}
        </div>

        {/* Certifications Section */}
        <div className="relative">
          <SectionHeader title="Certifications" icon={Award} iconClassName="text-pink-400" />

          {editingSection === 'certifications' ? (
            <div className="bg-white/5 p-4 rounded-lg">
              <p className="text-sm text-white/80 mb-3">
                Edit your certifications in the form below. You can add, edit, or remove
                certifications.
              </p>

              {/* This would typically be a more complex form with add/edit/remove functionality */}
              {/* For simplicity, we'll just show a message directing to the full editor */}
              <div className="bg-white/10 p-4 rounded-md text-center">
                <p className="text-white/70 text-sm">
                  Certification editing requires the full editor. Click "Edit All" to modify your
                  certifications.
                </p>
              </div>

              <div className="flex justify-end gap-2 mt-3">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white/70 hover:text-white"
                  onClick={cancelEditing}
                >
                  <X className="w-3.5 h-3.5 mr-1" />
                  Cancel
                </Button>
                <Button
                  size="sm"
                  className="bg-pink-700/80 hover:bg-pink-700 text-white"
                  onClick={() => setIsEditSliderOpen(true)}
                >
                  Edit All
                </Button>
              </div>
            </div>
          ) : (
            <>
              {profileData.certifications && profileData.certifications.length > 0 ? (
                <div className="relative group">
                  <ul className="space-y-3">
                    {profileData.certifications.map((cert, index) => (
                      <li key={index} className="pb-3">
                        {index !== profileData.certifications.length - 1 && (
                          <Separator className="bg-white/10 mt-3" />
                        )}
                        <h4 className="text-sm font-medium text-white">{cert.name}</h4>
                        <p className="text-xs text-white/80 mt-1">
                          {cert.issuer} - {formatDate(cert.issueDate)}
                        </p>
                      </li>
                    ))}
                  </ul>

                  <SectionEditButton
                    onClick={() => startEditingSection('certifications')}
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                  />
                </div>
              ) : (
                <div className="flex items-center justify-center p-4 bg-white/5 rounded-lg">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-white/50 hover:text-white"
                    onClick={() => startEditingSection('certifications')}
                  >
                    <Pencil className="w-3.5 h-3.5 mr-1.5" />
                    Add Certifications
                  </Button>
                </div>
              )}
            </>
          )}
        </div>

        {/* Languages Section */}
        <div className="relative">
          <SectionHeader title="Languages" icon={Languages} iconClassName="text-pink-400" />

          {editingSection === 'languages' ? (
            <div className="bg-white/5 p-4 rounded-lg">
              <textarea
                className="w-full bg-white/10 text-white border-0 rounded-md p-3 text-sm"
                rows={3}
                value={(editFormData.languages || []).join(', ')}
                onChange={e => {
                  const languagesArray = e.target.value
                    .split(',')
                    .map(language => language.trim())
                    .filter(language => language !== '');
                  updateEditFormData({ languages: languagesArray });
                }}
                placeholder="Enter languages separated by commas (e.g. English, Spanish, French)"
              />
              <p className="text-xs text-white/50 mt-1 ml-1">Separate languages with commas</p>
              <div className="flex justify-end gap-2 mt-3">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white/70 hover:text-white"
                  onClick={cancelEditing}
                >
                  <X className="w-3.5 h-3.5 mr-1" />
                  Cancel
                </Button>
                <Button
                  size="sm"
                  className="bg-pink-700/80 hover:bg-pink-700 text-white"
                  onClick={saveSection}
                >
                  Save
                </Button>
              </div>
            </div>
          ) : (
            <>
              {profileData.languages && profileData.languages.length > 0 ? (
                <div className="relative group">
                  <div className="flex flex-wrap gap-2">
                    {profileData.languages.map((language, index) => (
                      <span
                        key={index}
                        className="bg-white/10 text-white/80 px-3 py-1 rounded-full text-xs"
                      >
                        {language}
                      </span>
                    ))}
                  </div>
                  <SectionEditButton
                    onClick={() => startEditingSection('languages')}
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                  />
                </div>
              ) : (
                <div className="flex items-center justify-center p-4 bg-white/5 rounded-lg">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-white/50 hover:text-white"
                    onClick={() => startEditingSection('languages')}
                  >
                    <Pencil className="w-3.5 h-3.5 mr-1.5" />
                    Add Languages
                  </Button>
                </div>
              )}
            </>
          )}
        </div>

        {/* Values Section */}
        <div className="relative">
          <SectionHeader title="Values" icon={Heart} iconClassName="text-pink-400" />

          {editingSection === 'values' ? (
            <div className="bg-white/5 p-4 rounded-lg">
              <textarea
                className="w-full bg-white/10 text-white border-0 rounded-md p-3 text-sm"
                rows={3}
                value={(editFormData.myValues || []).join(', ')}
                onChange={e => {
                  const valuesArray = e.target.value
                    .split(',')
                    .map(value => value.trim())
                    .filter(value => value !== '');
                  updateEditFormData({ myValues: valuesArray });
                }}
                placeholder="Enter values separated by commas (e.g. Teamwork, Innovation, Integrity)"
              />
              <p className="text-xs text-white/50 mt-1 ml-1">Separate values with commas</p>
              <div className="flex justify-end gap-2 mt-3">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white/70 hover:text-white"
                  onClick={cancelEditing}
                >
                  <X className="w-3.5 h-3.5 mr-1" />
                  Cancel
                </Button>
                <Button
                  size="sm"
                  className="bg-pink-700/80 hover:bg-pink-700 text-white"
                  onClick={saveSection}
                >
                  Save
                </Button>
              </div>
            </div>
          ) : (
            <>
              {profileData.myValues && profileData.myValues.length > 0 ? (
                <div className="relative group">
                  <div className="flex flex-wrap gap-2">
                    {profileData.myValues.map((value, index) => (
                      <span
                        key={index}
                        className="bg-white/10 text-white/80 px-3 py-1 rounded-full text-xs"
                      >
                        {value}
                      </span>
                    ))}
                  </div>
                  <SectionEditButton
                    onClick={() => startEditingSection('values')}
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                  />
                </div>
              ) : (
                <div className="flex items-center justify-center p-4 bg-white/5 rounded-lg">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-white/50 hover:text-white"
                    onClick={() => startEditingSection('values')}
                  >
                    <Pencil className="w-3.5 h-3.5 mr-1.5" />
                    Add Values
                  </Button>
                </div>
              )}
            </>
          )}
        </div>

        {/* Job Preferences Section */}
        {profileData.preferences && (
          <div className="relative">
            <SectionHeader title="Job Preferences" icon={Briefcase} iconClassName="text-pink-400" />

            <div className="space-y-4">
              {/* Job Types */}
              {profileData.preferences.jobTypes && profileData.preferences.jobTypes.length > 0 && (
                <div>
                  <p className="text-xs text-white/60 uppercase tracking-wide mb-2">
                    Desired Roles
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {profileData.preferences.jobTypes.map((type, index) => (
                      <span
                        key={index}
                        className="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full text-xs"
                      >
                        {type}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Preferred Locations */}
              {profileData.preferences.locations &&
                profileData.preferences.locations.length > 0 && (
                  <div>
                    <p className="text-xs text-white/60 uppercase tracking-wide mb-2">
                      Preferred Locations
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {profileData.preferences.locations.map((location, index) => (
                        <span
                          key={index}
                          className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-xs flex items-center gap-1"
                        >
                          <MapPin className="w-3 h-3" />
                          {location}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

              {/* Industries */}
              {profileData.preferences.industries &&
                profileData.preferences.industries.length > 0 && (
                  <div>
                    <p className="text-xs text-white/60 uppercase tracking-wide mb-2">Industries</p>
                    <div className="flex flex-wrap gap-2">
                      {profileData.preferences.industries.map((industry, index) => (
                        <span
                          key={index}
                          className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-xs"
                        >
                          {industry}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

              {/* Salary Expectations */}
              {profileData.preferences.desiredSalary && (
                <div>
                  <p className="text-xs text-white/60 uppercase tracking-wide mb-2">
                    Salary Expectations
                  </p>
                  <div className="flex items-center gap-2">
                    <span className="bg-yellow-500/20 text-yellow-300 px-3 py-1 rounded-full text-xs">
                      {profileData.preferences.desiredSalary.currency}{' '}
                      {profileData.preferences.desiredSalary.min} -{' '}
                      {profileData.preferences.desiredSalary.max} /{' '}
                      {profileData.preferences.desiredSalary.period}
                    </span>
                  </div>
                </div>
              )}

              {/* Remote Preference */}
              {profileData.preferences.remotePreference && (
                <div>
                  <p className="text-xs text-white/60 uppercase tracking-wide mb-2">
                    Work Preference
                  </p>
                  <span className="bg-indigo-500/20 text-indigo-300 px-3 py-1 rounded-full text-xs capitalize">
                    {profileData.preferences.remotePreference}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
