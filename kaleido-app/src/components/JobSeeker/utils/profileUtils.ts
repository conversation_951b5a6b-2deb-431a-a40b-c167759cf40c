import { showToast } from '../../Toaster';
import { IJobSeekerProfile } from '../types';

import apiHelper from '@/lib/apiHelper';
import { UserRole } from '@/shared/types';
import { isPlaceholderEmail } from '@/utils/emailUtils';

/**
 * Fetches the job seeker profile for the current user
 * @param user The current user object
 * @param updateFormData Function to update form data with the fetched profile
 * @returns Promise that resolves when the profile is fetched or created
 */
export const fetchJobSeekerProfile = async (
  user: any,
  updateFormData: (data: Partial<IJobSeekerProfile>) => void,
  skipAutoCreate: boolean = false
): Promise<void> => {
  if (!user?.sub) return;

  const isMounted = true;
  let hasAttemptedProfileCreation = false;

  // Check session storage for cached profile to avoid unnecessary API calls
  const cacheKey = `jobseeker_profile_${user.sub}`;
  const cachedData = sessionStorage.getItem(cacheKey);

  if (cachedData) {
    try {
      const cached = JSON.parse(cachedData);
      // Check if cache is less than 5 minutes old
      if (cached.timestamp && Date.now() - cached.timestamp < 5 * 60 * 1000) {
        updateFormData(cached.data);
        return;
      }
    } catch (e) {
      // Invalid cache, continue with API call
      sessionStorage.removeItem(cacheKey);
    }
  }

  try {
    const response = await apiHelper.get('/job-seekers');
    if (response && isMounted) {
      // Check if the email is a placeholder and we have a valid email from Auth0
      if (isPlaceholderEmail(response.email) && user.email) {
        // Update the profile with the Auth0 email
        try {
          await apiHelper.patch(`/job-seekers/${response.id}`, {
            email: user.email,
          });

          // Update the local form data with the new email
          response.email = user.email;
        } catch (emailUpdateError) {
          console.error('Failed to update placeholder email:', emailUpdateError);
          // Continue with the existing profile even if email update fails
        }
      }

      // Transform backend data to match store format
      const transformedData = {
        ...response,
        // Fix location field if it comes as an object
        location:
          typeof response.location === 'object' && response.location !== null
            ? (response.location as any).country || ''
            : response.location || '',
        // Ensure education dates are properly formatted
        education:
          response.education?.map(edu => ({
            ...edu,
            startDate: edu.startDate ? new Date(edu.startDate) : null,
            endDate: edu.endDate ? new Date(edu.endDate) : null,
          })) || [],
        // Ensure experience dates are properly formatted
        experience:
          response.experience?.map(exp => ({
            ...exp,
            startDate: exp.startDate ? new Date(exp.startDate) : null,
            endDate: exp.endDate ? new Date(exp.endDate) : null,
          })) || [],
        // Ensure all required fields have default values
        skills: response.skills || [],
        languages: response.languages || [],
        myValues: response.myValues || [],
        certifications: response.certifications || [],
        projects: response.projects || [],
        socialProfiles: response.socialProfiles || [],
        achievements: response.achievements || [],
        recommendations: response.recommendations || [],
        // Ensure preferences has proper structure
        preferences: {
          jobTypes: response.preferences?.jobTypes || [],
          locations: response.preferences?.locations || [],
          industries: response.preferences?.industries || [],
          remotePreference: response.preferences?.remotePreference || 'onsite',
          desiredSalary: response.preferences?.desiredSalary || {
            min: 0,
            max: 0,
            currency: 'USD',
            period: 'yearly',
          },
        },
        // Ensure work availability has proper structure
        workAvailability: {
          immediatelyAvailable: response.workAvailability?.immediatelyAvailable || false,
          noticePeriod: response.workAvailability?.noticePeriod || 0,
        },
        // Ensure privacy settings has proper structure
        privacySettings: {
          profileVisibility: response.privacySettings?.profileVisibility || 'PRIVATE',
          allowMessages: response.privacySettings?.allowMessages || true,
          showSalary: response.privacySettings?.showSalary || false,
          showContact: response.privacySettings?.showContact || false,
        },
        // Ensure verifications has proper structure
        verifications: {
          email: response.verifications?.email || false,
          phone: response.verifications?.phone || false,
          education: response.verifications?.education || false,
          employment: response.verifications?.employment || false,
        },
        // Ensure metadata has proper structure
        metadata: {
          lastVerified: response.metadata?.lastVerified || null,
          lastUpdated: response.metadata?.lastUpdated || null,
        },
      };

      // Update form data with transformed profile data
      updateFormData(transformedData);

      // Cache the profile data in session storage
      try {
        sessionStorage.setItem(
          cacheKey,
          JSON.stringify({
            data: transformedData,
            timestamp: Date.now(),
          })
        );
      } catch (e) {
        // Ignore storage errors
      }
    }
  } catch (error: any) {
    console.error('Error fetching job seeker profile:', error);
    // Log the error status for debugging

    // If we get a 404 (profile not found) or 403 (permission denied), optionally create a new profile
    if (
      (error.response?.status === 404 || error.response?.status === 403) &&
      !hasAttemptedProfileCreation &&
      !skipAutoCreate &&
      isMounted
    ) {
      hasAttemptedProfileCreation = true;

      // Only attempt to create a profile if we have valid user data
      try {
        // Use data from user object or from error response
        // Make sure we have the user data even if the error response doesn't include it
        const userData = error.response?.data?.userData || user;

        if (userData && userData.email && userData.email.includes('@')) {
          // Ensure we have at least minimal VALID data for creating a profile
          const createData = {
            clientId: userData.sub || userData.userId,
            userId: userData.sub || userData.userId, // Ensure userId is set
            firstName:
              userData.given_name || userData.firstName || userData.name?.split(' ')[0] || '',
            lastName:
              userData.family_name ||
              userData.lastName ||
              (userData.name?.split(' ').length > 1
                ? userData.name.split(' ').slice(1).join(' ')
                : '') ||
              '',
            email: userData.email || '',
            role: UserRole.JOB_SEEKER,
            skills: [],
            hasCompletedOnboarding: false,
            myProfileImage: userData.picture || undefined,
          };

          // Try to create the profile using the public endpoint first
          let newProfile;
          try {
            newProfile = await apiHelper.post('/job-seekers/public/create', createData);
          } catch (publicEndpointError) {
            console.error('Error creating profile with public endpoint:', publicEndpointError);

            // Fall back to the standard endpoint if the public one fails
            try {
              newProfile = await apiHelper.post('/job-seekers', createData);
            } catch (standardEndpointError) {
              console.error(
                'Error creating profile with standard endpoint:',
                standardEndpointError
              );
              throw standardEndpointError; // Re-throw to be caught by the outer catch block
            }
          }

          if (newProfile && isMounted) {
            updateFormData(newProfile);

            // Check if the user already has a role before updating
            try {
              // First check if the user already has a role
              const existingRole = await apiHelper.get(`/roles/${userData.sub}`);

              if (!existingRole) {
                // Only create a new role if one doesn't exist
                await apiHelper.post('/roles', {
                  clientId: userData.sub,
                  role: UserRole.JOB_SEEKER,
                });
              } else {
              }
            } catch (roleError) {
              console.error('Error handling user role:', roleError);
            }

            showToast({
              message: 'Created new profile. Please complete your information.',
              isSuccess: true,
            });
          }
        } else {
          // Don't show error toast for expected cases
        }
      } catch (createError: any) {
        console.error('Error creating new job seeker profile:', createError);

        // If we get a 409 (conflict), try to fetch the profile again
        if (createError.response?.status === 409 && isMounted) {
          try {
            const existingProfile = await apiHelper.get('/job-seekers');
            if (existingProfile && isMounted) {
              updateFormData(existingProfile);
              showToast({
                message: 'Found your existing profile.',
                isSuccess: true,
              });
            }
          } catch (fetchError) {
            console.error('Error fetching existing profile:', fetchError);
            if (isMounted) {
              showToast({
                message: 'Failed to retrieve your profile. Please try again or contact support.',
                isSuccess: false,
              });
            }
          }
        } else if (isMounted) {
          showToast({
            message: 'Failed to create profile. Please try again or contact support.',
            isSuccess: false,
          });
        }
      }
    }
  }
};
