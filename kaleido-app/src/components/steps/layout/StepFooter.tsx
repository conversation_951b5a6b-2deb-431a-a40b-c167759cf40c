import { useEffect, useMemo, useState } from 'react';

import { ChevronLeft, ChevronRight, <PERSON><PERSON>heck, SkipForward } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';

import { SIDEBAR } from '@/constants/layout';
import { steps, stepValidations } from '@/contexts/jobs/constants';
import { useJobsStore } from '@/stores/unifiedJobStore';

import StepIndicator from './StepIndicator';

// Import StepValidation from types instead of defining it here
// This interface is already defined in the jobs context

const StepFooter = () => {
  const router = useRouter();
  const searchParams = useSearchParams() ?? new URLSearchParams();
  const currentStepParam = searchParams.get('step') || 'basic-information';
  const isFinalDraft = currentStepParam === 'final-draft';
  const isPublished = currentStepParam === 'published';

  // Track sidebar expansion state
  const [sidebarExpanded, setSidebarExpanded] = useState<boolean>(false);
  // Track if we're on a large screen
  const [isLargeScreen, setIsLargeScreen] = useState<boolean>(false);
  // Track navigation state to prevent double-clicking
  const [isNavigating, setIsNavigating] = useState<boolean>(false);
  // Track job save status for final draft
  const [jobSaved, setJobSaved] = useState<boolean>(false);
  // Track JD generation status for final draft
  const [jdGenerated, setJdGenerated] = useState<boolean>(false);

  // Get all the necessary functions and state from Zustand store
  const {
    activeStep,
    totalSteps,
    isLoading,
    isFinalStep,
    isStepValid,
    setActiveStep,
    updateURLAndStorage,
  } = useJobsStore();

  // Load sidebar expanded state from localStorage and handle resize
  useEffect(() => {
    // Check screen size
    const checkScreenSize = () => {
      setIsLargeScreen(window.innerWidth >= 1024);
    };

    // Check sidebar state
    const checkSidebarState = () => {
      const savedSidebarState = localStorage.getItem('sidebarExpanded');
      if (savedSidebarState !== null) {
        setSidebarExpanded(JSON.parse(savedSidebarState));
      }
    };

    // Check if job is saved (has an ID)
    const checkJobSaveStatus = () => {
      if (isFinalDraft) {
        const storedJob = localStorage.getItem('job');
        if (storedJob) {
          try {
            const jobData = JSON.parse(storedJob);
            setJobSaved(!!jobData.id);
          } catch (error) {
            setJobSaved(false);
          }
        } else {
          setJobSaved(false);
        }
      } else {
        setJobSaved(true); // For non-final-draft steps, always allow navigation
      }
    };

    // Check if JD has been generated
    const checkJDGenerationStatus = () => {
      if (isFinalDraft) {
        const storedJob = localStorage.getItem('job');
        if (storedJob) {
          try {
            const jobData = JSON.parse(storedJob);
            setJdGenerated(Boolean(jobData.generatedJD && jobData.generatedJD.trim()));
          } catch (error) {
            setJdGenerated(false);
          }
        } else {
          setJdGenerated(false);
        }
      } else {
        setJdGenerated(true); // For non-final-draft steps, always allow navigation
      }
    };

    // Initial checks
    checkScreenSize();
    checkSidebarState();
    checkJobSaveStatus();
    checkJDGenerationStatus();

    // Set up event listeners
    window.addEventListener('resize', checkScreenSize);

    // Poll for sidebar state changes, job save status, and JD generation status
    const interval = setInterval(() => {
      checkSidebarState();
      checkJobSaveStatus();
      checkJDGenerationStatus();
    }, 300);

    // Update the CSS variable when needed
    const updateCSSVariable = () => {
      if (typeof document !== 'undefined') {
        document.documentElement.style.setProperty(
          '--sidebar-width',
          sidebarExpanded ? SIDEBAR.EXPANDED_WIDTH : SIDEBAR.COLLAPSED_WIDTH
        );
      }
    };

    updateCSSVariable();

    return () => {
      window.removeEventListener('resize', checkScreenSize);
      clearInterval(interval);
    };
  }, [sidebarExpanded, isFinalDraft]);

  // Restore original implementation with proper navigation logic
  const handleNext = () => {
    // Prevent double-clicking
    if (isLoading || isNavigating) return;

    // Do not proceed if current step is invalid (especially step 0)
    if (!isFinalDraft && !isStepValid(activeStep)) {
      return;
    }

    // If we're on final-draft, we need to navigate to published
    if (isFinalDraft) {
      setIsNavigating(true);

      // Set completion flag to indicate legitimate job completion
      sessionStorage.setItem('jobCompleted', 'true');

      // Navigate to success page immediately without additional logic
      router.push('/job-description-creation?step=published');

      // Reset navigation state after a delay
      setTimeout(() => setIsNavigating(false), 1000);
      return;
    }

    // Normal next behavior for other steps
    const { handleNext } = useJobsStore.getState();
    handleNext();

    if (activeStep < totalSteps - 1) {
      const nextStep = activeStep + 1;
      updateURLAndStorage(nextStep);

      // Use proper client-side navigation with URL parameters
      const params = new URLSearchParams(searchParams?.toString());
      const newStepName = steps[nextStep]?.name?.toLowerCase().replace(/\s+/g, '-');
      if (newStepName) {
        params.set('step', newStepName);
      }
      const newURL = `/job-description-creation?${params.toString()}`;
      router.replace(newURL);
    }
  };

  const handleBack = () => {
    const { handleBack } = useJobsStore.getState();

    handleBack();

    if (activeStep > 0) {
      const prevStep = activeStep - 1;
      updateURLAndStorage(prevStep);

      // Use proper client-side navigation with URL parameters
      const params = new URLSearchParams(searchParams?.toString());
      const newStepName = steps[prevStep]?.name?.toLowerCase().replace(/\s+/g, '-');
      if (newStepName) {
        params.set('step', newStepName);
      }
      const newURL = `/job-description-creation?${params.toString()}`;
      router.replace(newURL);
    }
  };

  const handleSkip = () => {
    const { handleSkip } = useJobsStore.getState();
    handleSkip();

    if (activeStep < totalSteps - 1) {
      const nextStep = activeStep + 1;
      updateURLAndStorage(nextStep);

      // Use proper client-side navigation with URL parameters
      const params = new URLSearchParams(searchParams?.toString());
      const newStepName = steps[nextStep]?.name?.toLowerCase().replace(/\s+/g, '-');
      if (newStepName) {
        params.set('step', newStepName);
      }
      const newURL = `/job-description-creation?${params.toString()}`;
      router.replace(newURL);
    }
  };

  // No need for additional effects here

  const handleStepClick = (index: number) => {
    // Don't do anything if clicking the same step
    if (index === activeStep) {
      return;
    }

    // Always allow navigation to any step regardless of validation status
    setActiveStep(index);

    // Navigate to the new step using Next.js router
    updateURLAndStorage(index);

    // Use proper client-side navigation with URL parameters
    const params = new URLSearchParams(searchParams?.toString());
    const newStepName = steps[index]?.name?.toLowerCase().replace(/\s+/g, '-');
    if (newStepName) {
      params.set('step', newStepName);
    }
    const newURL = `/job-description-creation?${params.toString()}`;
    router.push(newURL);
  };

  const isCurrentStepValid = isStepValid(activeStep);

  // Check if current step is mandatory
  const isCurrentStepMandatory = useMemo(() => {
    const stepName = steps[activeStep].name.toLowerCase().replace(/\s+/g, '-');
    const validation = stepValidations?.[stepName];
    return validation ? validation.requiredFields.length > 0 : false;
  }, [activeStep]);

  // Step colors are now defined in the StepIndicator component

  // Check if we're in fullscreen mode
  const isFullScreen = searchParams.get('fullscreen') !== 'false';

  // Dynamic styles based on sidebar and screen size
  const footerClasses = `
    fixed bottom-0 left-0 right-0 z-50
    border-t border-gray-800/30
    shadow-lg
    transition-all duration-300
    ${!isFullScreen && isLargeScreen ? 'lg:left-[var(--sidebar-width)]' : ''}
  `;

  if (isPublished) {
    return null;
  }

  return (
    <div className={footerClasses.trim()}>
      {/* Gradient background similar to header overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-gray-900/90 via-gray-900/80 to-transparent backdrop-blur-md" />
      <div className="absolute inset-0 bg-gradient-to-t from-purple-900/30 via-transparent to-transparent" />

      {/* Content wrapper */}
      <div className="relative z-10 flex items-center justify-between px-2 sm:px-3 md:px-4 py-2 sm:py-3">
        {/* Enhanced Back button */}
        <div className="w-[120px]">
          <button
            type="button"
            onClick={handleBack}
            disabled={activeStep === 0 || isLoading}
            className={`group relative px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 flex items-center gap-2 min-w-[100px] justify-center
              ${
                activeStep === 0
                  ? 'text-white/30 cursor-not-allowed bg-gray-800/30'
                  : 'text-white bg-gray-800/40 hover:bg-gray-700/50 border border-gray-600/30 hover:border-gray-500/50 backdrop-blur-sm shadow-lg hover:shadow-xl hover:shadow-gray-900/20'
              }`}
          >
            <ChevronLeft
              className={`w-4 h-4 transition-transform duration-200 ${activeStep === 0 ? '' : 'group-hover:-translate-x-0.5'}`}
            />
            <span>Back</span>
          </button>
        </div>

        {/* Step indicator component */}
        <StepIndicator
          activeStep={activeStep}
          totalSteps={totalSteps}
          isStepValid={isStepValid}
          handleStepClick={handleStepClick}
          isInteractive={false}
        />

        {/* Enhanced Next/Skip buttons */}
        <div className="w-[200px] flex justify-end">
          <div className="flex items-center gap-3">
            {/* Enhanced skip button */}
            {!isFinalStep && !isCurrentStepMandatory && !isFinalDraft && (
              <button
                type="button"
                onClick={handleSkip}
                disabled={isLoading}
                className="group px-4 py-2.5 rounded-xl text-sm font-medium text-white/70 hover:text-white bg-gray-800/30 hover:bg-gray-700/40 border border-gray-600/20 hover:border-gray-500/40 backdrop-blur-sm transition-all duration-300 flex items-center gap-2 shadow-md hover:shadow-lg"
              >
                <SkipForward className="w-4 h-4 transition-transform duration-200 group-hover:translate-x-0.5" />
                <span>Skip</span>
              </button>
            )}

            {/* Enhanced Next/Finish button */}
            <button
              type="button"
              onClick={handleNext}
              disabled={
                isLoading ||
                isNavigating ||
                (!isCurrentStepValid && !isFinalDraft) ||
                (isFinalDraft && (!jobSaved || !jdGenerated))
              }
              title={
                isFinalDraft && (!jobSaved || !jdGenerated)
                  ? !jobSaved
                    ? 'Please save the job before finishing'
                    : 'Please generate the job description before finishing'
                  : ''
              }
              className={`group relative px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 flex items-center gap-2 min-w-[100px] justify-center overflow-hidden
              ${
                isLoading ||
                isNavigating ||
                (!isCurrentStepValid && !isFinalDraft) ||
                (isFinalDraft && (!jobSaved || !jdGenerated))
                  ? 'bg-gray-800/30 text-white/30 cursor-not-allowed border border-gray-700/30'
                  : 'bg-gradient-to-r from-purple-600 via-pink-600 to-purple-700 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25 border border-purple-400/30 hover:border-purple-300/50'
              }`}
            >
              {/* Animated background overlay */}
              {!(
                isLoading ||
                isNavigating ||
                (!isCurrentStepValid && !isFinalDraft) ||
                (isFinalDraft && (!jobSaved || !jdGenerated))
              ) && (
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-out" />
              )}

              <span className="relative z-10">{isFinalDraft ? 'Finish' : 'Next'}</span>
              {isFinalDraft ? (
                <FileCheck className="w-4 h-4 transition-transform duration-200 group-hover:scale-110" />
              ) : (
                <ChevronRight className="w-4 h-4 transition-transform duration-200 group-hover:translate-x-0.5" />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StepFooter;
