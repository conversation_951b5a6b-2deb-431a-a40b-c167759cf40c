import React, { useEffect, useRef } from 'react';

import { Check, FileCheck } from 'lucide-react';

import { steps } from '@/contexts/jobs/constants';

interface StepIndicatorProps {
  activeStep: number;
  totalSteps: number;
  isStepValid: (step: number) => boolean;
  handleStepClick: (index: number) => void;
  // When false, steps are purely visual and not clickable
  isInteractive?: boolean;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({
  activeStep,
  totalSteps,
  isStepValid,
  handleStepClick,
  isInteractive = false,
}) => {
  const previousStepRef = useRef(activeStep);
  const progressLineRef = useRef<HTMLDivElement>(null);

  // Define colors for different step states with improved design
  const stepColors = {
    active:
      'bg-gradient-to-br from-purple-500 via-pink-500 to-purple-600 shadow-[0_0_25px_rgba(168,85,247,0.4)] border-2 border-purple-400/30',
    completed: 'bg-gradient-to-br from-purple-600 to-pink-600 border-2 border-purple-400/50',
    upcoming: 'bg-gray-800/60 backdrop-blur-sm border-2 border-gray-600/40',
  };

  // Update progress line when active step changes
  useEffect(() => {
    if (progressLineRef.current) {
      const progressPercentage = (activeStep / (totalSteps - 1)) * 100;
      progressLineRef.current.style.width = `${progressPercentage}%`;
    }
    previousStepRef.current = activeStep;
  }, [activeStep, totalSteps]);

  return (
    <div className="flex-1 flex justify-center items-center relative bottom-[1.5rem]">
      <div className="relative pt-8">
        {/* Enhanced subtle gradient background effect */}
        <div className="absolute top-1/2 left-0 w-full h-16 bg-gradient-to-r from-transparent via-pink-700/10 to-transparent -z-20 transform -translate-y-1/2 mt-4 rounded-full blur-xl"></div>

        {/* Connection lines between steps - background line (positioned in the middle) */}
        <div className="absolute bottom-[1.25rem] left-0 w-full h-0.5 bg-gray-700 -z-10 transform -translate-y-1/2 rounded-full"></div>

        {/* Animated progress line (solid color, positioned in the middle) */}
        <div
          ref={progressLineRef}
          className="absolute bottom-[1.25rem] left-0 h-1 bg-pink-700 -z-5 transform -translate-y-1/2 rounded-full transition-all duration-500 ease-out shadow-[0_0_10px_rgba(219,39,119,0.3)]"
          style={{ width: `${(activeStep / (totalSteps - 1)) * 100}%` }}
        ></div>

        {/* Step icons with connected line */}
        <div className="relative flex items-center justify-center gap-4 sm:gap-6 md:gap-8">
          {Array.from({ length: totalSteps }, (_, i) => {
            const StepIcon = steps[i]?.icon || FileCheck;
            const isActive = i === activeStep;
            const isCompleted = i < activeStep;
            const stepName = steps[i]?.name || '';
            const isClickable = isInteractive && (isCompleted || isStepValid(i));

            // Calculate styles based on step state with improved design
            const iconSize = isActive ? 'w-12 h-12' : isCompleted ? 'w-10 h-10' : 'w-10 h-10';
            const iconContainerStyles = isActive
              ? 'ring-4 ring-purple-400/30 shadow-2xl shadow-purple-500/20'
              : isCompleted
                ? 'ring-2 ring-purple-400/40 shadow-lg shadow-purple-500/10'
                : 'ring-1 ring-gray-500/30 shadow-md shadow-gray-900/20';

            return (
              <button
                type="button"
                key={i}
                onClick={() => isClickable && handleStepClick(i)}
                disabled={!isClickable}
                aria-label={`Go to step ${steps[i]?.name}`}
                className={`
                  relative group focus:outline-none 
                  ${!isClickable ? 'cursor-not-allowed opacity-80' : 'cursor-pointer'}
                `}
              >
                {/* Step text above active circle - removed border and background */}
                {isActive && (
                  <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 w-auto min-w-max z-10">
                    <div className="px-6 py-1 shadow-md">
                      <span className="text-white text-sm font-medium whitespace-nowrap">
                        {stepName}
                      </span>
                    </div>
                  </div>
                )}

                {/* Enhanced completion indicator */}
                {isCompleted && (
                  <div className="absolute -top-1 -right-1 z-10">
                    <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gradient-to-br from-green-400 to-green-600 text-white text-xs shadow-lg border-2 border-white/20 backdrop-blur-sm">
                      <Check className="w-3.5 h-3.5" strokeWidth={2.5} />
                    </div>
                  </div>
                )}

                {/* Icon container with enhanced animation */}
                <div
                  className={`
                    flex items-center justify-center rounded-full
                    transition-all duration-300 ease-out
                    ${iconSize}
                    ${iconContainerStyles}
                    ${
                      isActive
                        ? stepColors.active
                        : isCompleted
                          ? stepColors.completed
                          : stepColors.upcoming
                    }
                    ${isActive ? 'scale-110' : isClickable ? 'hover:scale-105 hover:shadow-xl hover:shadow-purple-500/20' : ''}
                    ${isActive ? 'animate-pulse' : ''}
                  `}
                >
                  <StepIcon
                    className={`
                    text-white
                    ${isActive ? 'w-6 h-6' : isCompleted ? 'w-4 h-4' : 'w-5 h-5'}
                    transition-all duration-200
                  `}
                  />
                </div>

                {/* Hover tooltip with step name - only for non-active steps */}
                {!isActive && (
                  <div className="absolute -bottom-7 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none">
                    <div className="bg-gradient-to-r from-gray-900 to-gray-800 text-white text-xs rounded-md px-3 py-1.5 whitespace-nowrap border border-gray-700/50 shadow-lg">
                      {stepName}
                    </div>
                  </div>
                )}
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default StepIndicator;
