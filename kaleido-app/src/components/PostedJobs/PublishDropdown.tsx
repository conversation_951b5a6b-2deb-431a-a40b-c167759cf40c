import React, { useState } from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import { Check, ChevronDown, ExternalLink, EyeOff, Rocket, Settings } from 'lucide-react';
import { useRouter } from 'next/navigation';

export interface PublishPlatform {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  disabled?: boolean;
}

interface PublishDropdownProps {
  isOpen: boolean;
  platforms: PublishPlatform[];
  selectedPlatforms: string[];
  togglePlatformSelection: (platformId: string) => void;
  handlePublish: () => void;
  handleUnpublish: () => void;
  isPublishing: boolean;
  isUnpublishing: boolean;
  isPublished: boolean;
}

const PublishDropdown: React.FC<PublishDropdownProps> = ({
  isOpen,
  platforms,
  selectedPlatforms,
  togglePlatformSelection,
  handlePublish,
  handleUnpublish,
  isPublishing,
  isUnpublishing,
  isPublished,
}) => {
  const [showComingSoon, setShowComingSoon] = useState(false);
  const router = useRouter();

  if (!isOpen) return null;

  return (
    <div
      style={{
        backgroundColor: 'var(--card-bg)',
        borderColor: 'var(--card-border)',
        zIndex: 50,
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
      }}
      className="absolute top-full right-0 mt-2 w-80 rounded-lg shadow-lg border overflow-hidden backdrop-blur-2xl z-50"
    >
      <div className="p-3 backdrop-blur-2xl">
        <h3 className="text-md mb-2" style={{ color: 'var(--foreground-color)' }}>
          Select platforms:
        </h3>
        <div className="space-y-0">
          {/* Our Job Board platform */}
          {platforms
            .filter(platform => !platform.disabled)
            .map((platform, index, filteredPlatforms) => (
              <div
                key={platform.id}
                className="flex items-center justify-between gap-2 py-3 px-2 rounded cursor-pointer transition-colors"
                style={{
                  color: 'var(--foreground-color)',
                  borderBottom:
                    index !== filteredPlatforms.length - 1
                      ? `1px solid var(--card-border)`
                      : 'none',
                }}
                onClick={() => togglePlatformSelection(platform.id)}
              >
                <div className="flex items-center gap-2">
                  <div
                    className="flex items-center justify-center w-6 h-6 rounded-full"
                    style={{ backgroundColor: platform.color }}
                  >
                    <div style={{ color: 'var(--foreground-color)' }}>{platform.icon}</div>
                  </div>
                  <span className="text-sm" style={{ color: 'var(--foreground-color)' }}>
                    {platform.name}
                  </span>
                </div>

                <div
                  className="flex items-center justify-center w-5 h-5 border-2 rounded-md relative"
                  style={{ borderColor: 'var(--card-border)' }}
                >
                  {(selectedPlatforms.includes(platform.id) ||
                    (platform.id === 'jobboard' && isPublished)) && (
                    <Check className="w-4 h-4 text-white absolute" />
                  )}
                </div>
              </div>
            ))}

          {/* Toggle button for external platforms */}
          {platforms.some(platform => platform.disabled) && (
            <button
              onClick={() => setShowComingSoon(!showComingSoon)}
              className="w-full mt-2 text-sm py-2 flex items-center justify-start px-2 gap-1"
              style={{ color: 'var(--foreground-color)', opacity: 0.7 }}
            >
              {showComingSoon ? (
                <>
                  <ChevronDown className="w-4 h-4 transform rotate-180" />
                  <span>Hide External Platforms</span>
                </>
              ) : (
                <>
                  <ChevronDown className="w-4 h-4" />
                  <span>External Platforms</span>
                </>
              )}
            </button>
          )}

          {/* External platforms with animation */}
          <AnimatePresence>
            {showComingSoon && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="overflow-hidden"
              >
                {platforms
                  .filter(platform => platform.disabled)
                  .map((platform, index, filteredPlatforms) => (
                    <motion.div
                      key={platform.id}
                      initial={{ x: -20, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center justify-between gap-2 py-3 px-2 rounded cursor-not-allowed"
                      style={{
                        borderBottom:
                          index !== filteredPlatforms.length - 1
                            ? `1px solid var(--card-border)`
                            : 'none',
                      }}
                    >
                      <div className="flex items-center gap-2">
                        <div
                          className="flex items-center justify-center w-6 h-6 rounded-full"
                          style={{ backgroundColor: platform.color }}
                        >
                          <div className="text-white/50">{platform.icon}</div>
                        </div>
                        <div>
                          <span className="text-sm text-white/50">{platform.name}</span>
                        </div>
                      </div>

                      <div className="flex items-center justify-center w-5 h-5 border-2 rounded-md relative border-white/30"></div>
                    </motion.div>
                  ))}
                {/* Show guidance for unconnected platforms */}
                {platforms.some(platform => platform.disabled && platform.id !== 'jobboard') && (
                  <div
                    className="mt-4 p-3 rounded-lg border"
                    style={{
                      backgroundColor: 'var(--card-bg)',
                      borderColor: 'var(--card-border)',
                      opacity: 0.8,
                    }}
                  >
                    <div className="flex items-start gap-2">
                      <Settings
                        className="w-4 h-4 mt-0.5"
                        style={{ color: 'var(--foreground-color)' }}
                      />
                      <div className="flex-1">
                        <p
                          className="text-sm font-medium mb-1"
                          style={{ color: 'var(--foreground-color)' }}
                        >
                          Connect Social Media Platforms
                        </p>
                        <p
                          className="text-xs mb-2"
                          style={{ color: 'var(--foreground-color)', opacity: 0.7 }}
                        >
                          Connect your social media accounts to publish jobs across multiple
                          platforms.
                        </p>
                        <button
                          onClick={() => router.push('/company-settings?tab=social-media')}
                          className="flex items-center gap-1 text-xs px-2 py-1 rounded transition-colors"
                          style={{
                            backgroundColor: 'var(--button-secondary-bg)',
                            color: 'var(--button-secondary-text)',
                          }}
                        >
                          <ExternalLink className="w-3 h-3" />
                          <span>Go to Settings</span>
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        <div className="mt-3 flex justify-end gap-2">
          {isPublished && (
            <button
              onClick={handleUnpublish}
              disabled={
                isUnpublishing || isPublishing || (selectedPlatforms.length === 0 && !isPublished)
              }
              className="px-4 py-2 rounded-full transition-colors text-white text-sm disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 w-full justify-center"
              style={{
                backgroundColor: '#f97316',
                color: 'var(--button-primary-text)',
              }}
            >
              {isUnpublishing ? (
                <>
                  <span className="animate-spin">⏳</span>
                  <span>Unpublishing...</span>
                </>
              ) : (
                <>
                  <EyeOff className="w-4 h-4" />
                  <span>Unpublish</span>
                </>
              )}
            </button>
          )}
          {!isPublished && selectedPlatforms.length > 0 && (
            <button
              onClick={handlePublish}
              disabled={isPublishing || isUnpublishing || selectedPlatforms.length === 0}
              className="px-4 py-2 rounded-full transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 w-full justify-center"
              style={{
                background: 'var(--button-primary-bg)',
                color: 'var(--button-primary-text)',
              }}
            >
              {isPublishing ? (
                <>
                  <span className="animate-spin">⏳</span>
                  <span>Publishing...</span>
                </>
              ) : (
                <>
                  <Rocket className="w-4 h-4" />
                  <span>Publish Now</span>
                </>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default PublishDropdown;
