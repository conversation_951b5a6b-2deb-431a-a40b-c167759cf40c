'use client';

import React, { Suspense, useEffect, useState } from 'react';

import { motion } from 'framer-motion';
import { CreditCard, Menu, Moon, Sun, X, Grid3x3, Sparkles } from 'lucide-react';
import Image from 'next/image';
import { usePathname } from 'next/navigation';

import useEnhancedUserData from '@/hooks/useEnhancedUserData';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { useUser } from '@/hooks/useUser';
import { NavItem } from '@/lib/navigation';
import { getUserDisplayName } from '@/utils/getUserDisplayName';

import { MobileMenu } from './MobileMenu';
import SubscriptionStatusIndicator from './SubscriptionStatusIndicator';
import UserProfileSection from './UserProfileSection';

interface HeaderProps {
  pathname: string;
  navItems: NavItem[];
  expanded?: boolean;
  setExpanded?: (expanded: boolean) => void;
}

export const Header: React.FC<HeaderProps> = ({ pathname, navItems, expanded, setExpanded }) => {
  const { user } = useUser();
  const { userData } = useEnhancedUserData();
  const currentPathname = usePathname();
  const [isDarkTheme, setIsDarkTheme] = useState(true);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [useClassicView, setUseClassicView] = useState(false);
  const isMobile = useMediaQuery('(max-width: 768px)');
  const isTablet = useMediaQuery('(max-width: 1024px)');

  // Get authoritative user role from enhanced user data
  const userRole = userData?.userRole || '';

  // Check if we're on the dashboard page and if user is employer
  // Support both pathname prop and usePathname hook
  const actualPathname = currentPathname || pathname;
  const isDashboard = actualPathname === '/dashboard';
  const isEmployer = userRole === 'employer';
  const showDashboardToggle = isDashboard && isEmployer;

  // Load classic view preference
  useEffect(() => {
    if (typeof window !== 'undefined' && showDashboardToggle) {
      setUseClassicView(localStorage.getItem('classicViewEnabled') === 'true');
    }
  }, [showDashboardToggle]);

  // Handle dashboard view toggle
  const handleToggleDashboardView = () => {
    const newValue = !useClassicView;
    setUseClassicView(newValue);
    if (typeof window !== 'undefined') {
      localStorage.setItem('classicViewEnabled', String(newValue));
      // Dispatch event to notify dashboard
      window.dispatchEvent(
        new CustomEvent('dashboardViewToggle', { detail: { classic: newValue } })
      );
    }
  };

  // Initialize theme from localStorage and update theme state on client side
  useEffect(() => {
    // Function to update the theme state based on DOM attribute
    const updateThemeState = () => {
      const currentTheme = document.documentElement.getAttribute('data-theme') || 'dark';
      setIsDarkTheme(currentTheme !== 'light');
    };

    // Check localStorage for saved theme preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      // Apply the saved theme to the DOM
      document.documentElement.setAttribute('data-theme', savedTheme);
      document.documentElement.style.colorScheme = savedTheme;
      // Update state to match
      setIsDarkTheme(savedTheme !== 'light');
    } else {
      // If no saved theme, set initial theme state based on current DOM
      updateThemeState();
    }

    // Listen for theme changes using MutationObserver
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.attributeName === 'data-theme') {
          updateThemeState();
        }
      });
    });

    // Add event listeners
    observer.observe(document.documentElement, { attributes: true });

    // Clean up
    return () => {
      observer.disconnect();
    };
  }, []);

  // Toggle theme function
  const handleToggleTheme = () => {
    // Get the current theme directly from the DOM
    const currentTheme = document.documentElement.getAttribute('data-theme');

    // Determine the new theme (force it to be the opposite of the current one)
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';

    // Apply the theme change directly
    document.documentElement.setAttribute('data-theme', newTheme);
    document.documentElement.style.colorScheme = newTheme;

    // Save to localStorage for persistence
    localStorage.setItem('theme', 'dark');

    // Update local state
    setIsDarkTheme(newTheme === 'dark');

    // Dispatch a custom event that other components can listen for
    const event = new CustomEvent('themechange', { detail: { theme: newTheme } });
    document.dispatchEvent(event);
  };

  const isReferralPartner =
    userRole === 'referral-partner' || pathname.startsWith('/referral-partner');

  return (
    <Suspense>
      <div
        className={`${isReferralPartner ? 'absolute' : 'sticky'} top-0 z-50 h-[70px] flex items-center w-full ${isReferralPartner ? 'bg-transparent' : ''}`}
      >
        <div className="w-full px-4 lg:px-8 py-10">
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-between"
          >
            {/* Left side - Burger menu on mobile */}
            <div className="flex items-center">
              {/* Mobile menu toggle button - Only visible on mobile */}
              {isMobile && (
                <button
                  type="button"
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  className="p-2.5 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
                  aria-label="Toggle mobile menu"
                >
                  <Menu className="h-5 w-5 text-white/90" />
                </button>
              )}

              {/* Left side empty space */}
            </div>

            {/* Center - Logo */}
            <div className="absolute left-1/2 transform -translate-x-1/2 flex items-center justify-center">
              <div className="flex items-center">
                {isMobile && (
                  <Image
                    src="/images/logos/kaleido-logo-only.webp"
                    alt="Kaleido Logo"
                    width={40}
                    height={40}
                    className={isMobile ? '' : 'mr-2'}
                  />
                )}
              </div>
            </div>

            {/* Right side - User controls */}
            <div className="flex items-center">
              {/* Dashboard View Toggle - Only for employer dashboard */}
              {showDashboardToggle && (
                <button
                  type="button"
                  onClick={handleToggleDashboardView}
                  className="mr-3 px-3 py-2 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center gap-2 group"
                  aria-label={useClassicView ? 'Switch to zen mode' : 'Switch to classic view'}
                  title={
                    useClassicView
                      ? 'Switch to Zen Mode (Cmd/Ctrl + Shift + V)'
                      : 'Switch to Classic View (Cmd/Ctrl + Shift + V)'
                  }
                >
                  {useClassicView ? (
                    <Sparkles className="h-4 w-4 text-purple-300 group-hover:text-purple-200" />
                  ) : (
                    <>
                      <Grid3x3 className="h-4 w-4 text-white/70 group-hover:text-white/90" />
                    </>
                  )}
                </button>
              )}

              {/* Theme toggle button - Hidden for now */}
              {false && (
                <button
                  type="button"
                  onClick={handleToggleTheme}
                  className="p-2.5 rounded-full hover:bg-white/10 transition-colors"
                  aria-label={isDarkTheme ? 'Switch to light mode' : 'Switch to dark mode'}
                >
                  {isDarkTheme ? (
                    <Sun className="h-5 w-5 text-yellow-300" />
                  ) : (
                    <Moon className="h-5 w-5 text-blue-700" />
                  )}
                </button>
              )}

              {/* User profile section - Hidden on mobile, full on tablet and desktop */}
              {!isMobile && (
                <UserProfileSection
                  userName={getUserDisplayName(user)}
                  userRole={userRole}
                  simplified={false}
                />
              )}

              {/* Mobile subscription button - Only visible on mobile */}
              {isMobile && (
                <button
                  type="button"
                  className="ml-2 p-2.5 rounded-full hover:bg-white/10 transition-colors"
                  aria-label="Subscription Status"
                  onClick={() => setShowSubscriptionModal(true)}
                >
                  <CreditCard className="h-5 w-5 text-purple-300" />
                </button>
              )}
            </div>
          </motion.div>
        </div>
      </div>

      {/* Mobile Subscription Modal */}
      {showSubscriptionModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div
            className="absolute inset-0 bg-black/60 backdrop-blur-xl"
            onClick={() => setShowSubscriptionModal(false)}
          ></div>
          <div className="relative w-full max-w-lg mx-4 bg-purple-900/30 backdrop-blur-xl rounded-xl border border-white/10 p-5 z-10">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-white">Subscription Status</h2>
              <button
                onClick={() => setShowSubscriptionModal(false)}
                className="p-2 rounded-full hover:bg-white/10"
              >
                <X className="w-5 h-5 text-white/80" />
              </button>
            </div>
            <div className="max-h-[80vh] overflow-y-auto">
              <SubscriptionStatusIndicator userRole={userRole} isMobile={true} />
            </div>
          </div>
        </div>
      )}

      {/* Mobile Menu */}
      <MobileMenu
        isMobileMenuOpen={isMobileMenuOpen}
        setIsMobileMenuOpen={setIsMobileMenuOpen}
        pathname={pathname}
        navItems={navItems}
        expanded={expanded}
        setExpanded={setExpanded}
        userRole={userRole}
        userName={getUserDisplayName(user)}
      />
    </Suspense>
  );
};
