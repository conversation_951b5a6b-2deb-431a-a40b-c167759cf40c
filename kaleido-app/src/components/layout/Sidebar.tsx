'use client';

import React, { useEffect, useMemo } from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import { Briefcase, ChevronLeft, ChevronRight, Flag, LayoutDashboard, Store } from 'lucide-react';
import Image from 'next/image';

import { SIDEBAR } from '@/constants/layout';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { useUser } from '@/hooks/useUser';
import { NavItem } from '@/lib/navigation';
import { UserRole } from '@/types/roles';

import { SidebarIcon } from './SidebarIcon';
import { SidebarProfile } from './SidebarProfile';

// Props for the Sidebar component
interface SidebarProps {
  expanded: boolean;
  setExpanded: (expanded: boolean) => void;
  pathname: string;
  navItems: NavItem[];
}

// Section separator component
const SectionSeparator: React.FC<{ label: string; expanded: boolean }> = ({ label, expanded }) => {
  // Always use dark theme styling

  // Define icons for specific sections
  const getIconForLabel = () => {
    if (label === 'Marketplace') {
      return <Store className="h-4 w-4 text-pink-700/20" />;
    } else if (label === 'Job Board') {
      return <Briefcase className="h-4 w-4 text-purple-500/20" />;
    } else if (label === 'Admin Controls') {
      return <Flag className="h-4 w-4 text-red-500/20" />;
    } else if (label === 'Main Navigation') {
      return <LayoutDashboard className="h-4 w-4 text-blue-500/20" />;
    }
    return null;
  };

  return (
    <div className="px-4 my-3">
      {expanded ? (
        <div className="flex items-center gap-2">
          <div className="h-[1px] bg-white/10 flex-grow" />
          <div className="flex items-center gap-1">
            {getIconForLabel()}
            <span className="text-xs text-white/10 uppercase tracking-wider px-1">{label}</span>
          </div>
          <div className="h-[1px] bg-white/10 flex-grow" />
        </div>
      ) : (
        <div className="h-[1px] bg-white/10 w-full" />
      )}
    </div>
  );
};

export const Sidebar: React.FC<SidebarProps> = ({ expanded, setExpanded, pathname, navItems }) => {
  const { user, isLoading: userLoading } = useUser();
  // Always use dark theme styling

  // Get user role from localStorage
  const userRole = useMemo(() => {
    try {
      if (!user?.sub) return null;
      const roleData = localStorage.getItem(`userRole_${user.sub}`);
      if (!roleData) return null;

      const { role } = JSON.parse(roleData);
      const roleValue = role as string;

      return roleValue.toLowerCase().replace(/\s+/g, '-') as UserRole;
    } catch (error) {
      console.error('Error parsing role data:', error);
      return null;
    }
  }, [user?.sub]);

  // Use our custom hook to handle dynamic navigation items
  const { processedNavItems } = useDynamicNavigation(navItems, userRole);

  // Sort items to ensure hub items come before market items and admin items come last
  const sortedNavItems = useMemo(() => {
    // Group items by their group property
    const groupedItems = processedNavItems.reduce(
      (acc, item) => {
        const group = item.group || 'other';
        if (!acc[group]) {
          acc[group] = [];
        }
        acc[group].push(item);
        return acc;
      },
      {} as Record<string, NavItem[]>
    );

    // Define the order of groups
    const groupOrder = ['hub', 'jobs', 'market', 'partner', 'admin', 'other'];

    // Flatten the grouped items in the desired order
    const sorted = groupOrder.flatMap(group => groupedItems[group] || []);
    return sorted;
  }, [processedNavItems]);

  // Load initial sidebar expansion state
  useEffect(() => {
    const savedSidebarState = localStorage.getItem('sidebarExpanded');
    if (savedSidebarState !== null) {
      setExpanded(JSON.parse(savedSidebarState));
    }
  }, [setExpanded]);

  // Save sidebar expansion state when it changes
  useEffect(() => {
    localStorage.setItem('sidebarExpanded', JSON.stringify(expanded));
  }, [expanded]);

  // Prevent any rendering until everything is loaded
  if (userLoading)
    return (
      <div
        className={`fixed left-0 top-0 h-full bg-black/1 hidden md:block ${expanded ? `w-[${SIDEBAR.EXPANDED_WIDTH}]` : `w-[${SIDEBAR.COLLAPSED_WIDTH}]`}`}
      />
    );

  return (
    <motion.div
      initial={false}
      animate={{ width: expanded ? SIDEBAR.EXPANDED_WIDTH : SIDEBAR.COLLAPSED_WIDTH }}
      transition={{ duration: 0.2, ease: 'easeOut' }}
      className="fixed left-0 top-0 h-full backdrop-blur-xl z-[9999]
        bg-black/1 border-r border-white/10 before:bg-gradient-to-b before:from-white/5 before:to-transparent
        before:absolute before:inset-0 before:pointer-events-none
        flex-col
        md:flex
        hidden"
    >
      {/* Logo Section */}
      <div className="flex items-center justify-between p-4 bg-white/5 backdrop-blur-md h-[80px]">
        <div className="cursor-pointer h-[55px] flex items-center justify-center">
          <AnimatePresence initial={false} mode="wait">
            {!expanded ? (
              <motion.div
                key="small-logo"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="h-[40px] w-[40px] flex items-center justify-center"
              >
                <Image
                  src="/images/logos/kaleido-logo-only.webp"
                  alt="Kaleido Logo"
                  width={40}
                  height={40}
                  style={{ width: 'auto', height: 'auto', maxWidth: '40px', maxHeight: '40px' }}
                />
              </motion.div>
            ) : (
              <motion.div
                key="full-logo"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="h-[55px] w-[90px] flex items-center justify-start"
              >
                <Image
                  src="/images/logos/kaleido-logo-full-white.webp"
                  alt="Kaleido Talent"
                  width={100}
                  height={40}
                  style={{ width: 'auto', height: 'auto', maxWidth: '100px', maxHeight: '40px' }}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Toggle Button */}
        <button
          type="button"
          onClick={() => {
            const newExpanded = !expanded;
            setExpanded(newExpanded);
            // Dispatch custom event for same-tab communication
            window.dispatchEvent(
              new CustomEvent('sidebar-toggle', { detail: { expanded: newExpanded } })
            );
          }}
          className="p-1.5 rounded-full bg-white/10 hover:bg-purple-900/70 transition-colors"
          aria-label={expanded ? 'Collapse sidebar' : 'Expand sidebar'}
        >
          {expanded ? (
            <ChevronLeft className="h-4 w-4 text-white/80" />
          ) : (
            <ChevronRight className="h-4 w-4 text-white/80" />
          )}
        </button>
      </div>

      {/* Navigation Items */}
      <div className="flex-1 py-2 space-y-1">
        {sortedNavItems
          .filter(item => !item.hidden)
          .map((item, index, array) => {
            const previousItem = array[index - 1];
            const showSeparator = previousItem && previousItem.group !== item.group;

            return (
              <React.Fragment key={String(item.href)}>
                {showSeparator && item.group && (
                  <SectionSeparator
                    label={
                      typeof item.group === 'string' && item.group === 'market'
                        ? 'Marketplace'
                        : typeof item.group === 'string' && item.group === 'admin'
                          ? 'Admin Controls'
                          : typeof item.group === 'string' && item.group === 'hub'
                            ? 'Main Navigation'
                            : typeof item.group === 'string' && item.group === 'jobs'
                              ? 'Job Board'
                              : 'Other'
                    }
                    expanded={expanded}
                  />
                )}
                <div className="relative group">
                  <SidebarIcon
                    icon={item.icon}
                    label={item.label}
                    active={
                      item.href === '/referral-partner'
                        ? pathname.startsWith('/referral-partner')
                        : pathname === item.href
                    }
                    expanded={expanded}
                    href={String(item.href)}
                    description={item.description}
                  />
                  {/* Item Hover Label */}
                  {!expanded && (
                    <div
                      className="absolute left-full top-1/2 -translate-y-1/2 ml-3 px-3 py-2
                      bg-[#280933de] border-white/10 text-white rounded-md text-sm whitespace-nowrap
                      opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none z-50
                      border shadow-lg"
                    >
                      {/* Connecting line */}
                      <div className="absolute right-full top-1/2 -translate-y-1/2 w-3 h-[2px] bg-gradient-to-r from-transparent to-purple-500"></div>

                      {/* Tooltip content */}
                      <div className="flex items-center gap-2 backdrop-blur-xl">
                        <span className="text-purple-300">
                          {React.createElement(item.icon, { className: 'h-4 w-4' })}
                        </span>
                        <div className="flex flex-col">
                          <span className="font-medium">{item.label}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </React.Fragment>
            );
          })}
      </div>

      {/* Add Profile Section */}
      {user && <SidebarProfile expanded={expanded} userData={user} />}
    </motion.div>
  );
};
