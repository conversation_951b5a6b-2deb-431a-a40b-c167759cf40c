'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { AnimatedBackground } from '../AnimatedBackground';
import { CongratulatoryModal, HiredJobInfo } from './CongratulatoryModal';
import { ReferralProgramBanner } from '@/components/shared/ReferralProgramBanner';
import apiHelper from '@/lib/apiHelper';
import useUser from '@/hooks/useUser';
import { 
  Briefcase, 
  FileText, 
  Star, 
  Bell,
  ArrowRight,
  TrendingUp,
  Clock,
  CheckCircle
} from 'lucide-react';

interface ZenJobSeekerDashboardProps {
  stats: {
    applications: any;
    matchedJobs: any;
    notifications: any;
  };
  loading: boolean;
  userName?: string;
}

const ZenJobSeekerDashboard: React.FC<ZenJobSeekerDashboardProps> = ({ stats, loading, userName }) => {
  const [hiredJobInfo, setHiredJobInfo] = useState<HiredJobInfo | null>(null);
  const [showHiredModal, setShowHiredModal] = useState(false);
  const router = useRouter();
  const { user } = useUser();
  const displayName = userName || user?.given_name || user?.name?.split(' ')[0] || 'there';

  // Check for hired status on load
  useEffect(() => {
    const checkHiredStatus = async () => {
      try {
        const response = await apiHelper.get('/dashboard/hired-status');
        if (response?.isHired) {
          setHiredJobInfo(response.jobInfo);
          setShowHiredModal(true);
        }
      } catch (error) {
        console.error('Error checking hired status:', error);
      }
    };

    checkHiredStatus();
  }, []);

  if (loading) {
    return (
      <div className="relative flex h-full w-full items-center justify-center p-6">
        <div className="animate-pulse text-white/40">Loading your dashboard...</div>
      </div>
    );
  }

  const totalApplications = stats.applications?.total || 0;
  const interviewingCount = stats.applications?.statusBreakdown?.interviewing || 0;
  const matchedJobsCount = stats.matchedJobs?.total || 0;
  const unreadNotifications = stats.notifications?.unread || 0;

  // Get greeting based on time of day
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  return (
    <>
      <AnimatedBackground />
      <div 
        data-testid="zen-jobseeker-dashboard"
        className="relative flex h-full w-full flex-col items-center justify-center p-6"
      >
        <div className="w-full max-w-5xl mx-auto space-y-8">
          {/* Welcome Message */}
          <div className="text-center space-y-2">
            <h1 className="text-3xl font-light text-white/90">
              {getGreeting()}, {displayName}
            </h1>
            <p className="text-white/60">
              Your career journey continues to unfold
            </p>
          </div>

          {/* Referral Program Banner */}
          <ReferralProgramBanner userType="jobseeker" />

          {/* Main Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {/* Applications */}
            <div 
              onClick={() => router.push('/applications')}
              className="group relative bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-300 cursor-pointer"
            >
              <div className="flex flex-col items-center text-center space-y-3">
                <FileText className="w-8 h-8 text-pink-400/80 group-hover:text-pink-400 transition-colors" />
                <div>
                  <div className="text-2xl font-light text-white">{totalApplications}</div>
                  <div className="text-xs text-white/60 mt-1">Applications</div>
                </div>
              </div>
            </div>

            {/* Interviewing */}
            <div 
              onClick={() => router.push('/applications')}
              className="group relative bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-300 cursor-pointer"
            >
              <div className="flex flex-col items-center text-center space-y-3">
                <Briefcase className="w-8 h-8 text-blue-400/80 group-hover:text-blue-400 transition-colors" />
                <div>
                  <div className="text-2xl font-light text-white">{interviewingCount}</div>
                  <div className="text-xs text-white/60 mt-1">Interviewing</div>
                </div>
              </div>
            </div>

            {/* Matched Jobs */}
            <div 
              onClick={() => router.push('/jobs/marketplace')}
              className="group relative bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-300 cursor-pointer"
            >
              <div className="flex flex-col items-center text-center space-y-3">
                <Star className="w-8 h-8 text-yellow-400/80 group-hover:text-yellow-400 transition-colors" />
                <div>
                  <div className="text-2xl font-light text-white">{matchedJobsCount}</div>
                  <div className="text-xs text-white/60 mt-1">Matched Jobs</div>
                </div>
              </div>
            </div>

            {/* Notifications */}
            <div 
              onClick={() => router.push('/notifications')}
              className="group relative bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-300 cursor-pointer"
            >
              <div className="flex flex-col items-center text-center space-y-3">
                <Bell className="w-8 h-8 text-indigo-400/80 group-hover:text-indigo-400 transition-colors" />
                <div>
                  <div className="text-2xl font-light text-white">{unreadNotifications}</div>
                  <div className="text-xs text-white/60 mt-1">Unread</div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-4">
            <h2 className="text-center text-sm font-medium text-white/60 uppercase tracking-wider">
              Quick Actions
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={() => router.push('/jobs/marketplace')}
                className="group flex items-center justify-between bg-gradient-to-r from-purple-500/10 to-pink-500/10 backdrop-blur-sm rounded-xl p-4 border border-white/10 hover:from-purple-500/20 hover:to-pink-500/20 transition-all duration-300"
              >
                <span className="text-white/90 font-light">Browse Jobs</span>
                <ArrowRight className="w-4 h-4 text-white/60 group-hover:text-white/90 group-hover:translate-x-1 transition-all" />
              </button>

              <button
                onClick={() => router.push('/profile')}
                className="group flex items-center justify-between bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm rounded-xl p-4 border border-white/10 hover:from-blue-500/20 hover:to-purple-500/20 transition-all duration-300"
              >
                <span className="text-white/90 font-light">Update Profile</span>
                <ArrowRight className="w-4 h-4 text-white/60 group-hover:text-white/90 group-hover:translate-x-1 transition-all" />
              </button>

              <button
                onClick={() => router.push('/applications')}
                className="group flex items-center justify-between bg-gradient-to-r from-pink-500/10 to-indigo-500/10 backdrop-blur-sm rounded-xl p-4 border border-white/10 hover:from-pink-500/20 hover:to-indigo-500/20 transition-all duration-300"
              >
                <span className="text-white/90 font-light">View Applications</span>
                <ArrowRight className="w-4 h-4 text-white/60 group-hover:text-white/90 group-hover:translate-x-1 transition-all" />
              </button>
            </div>
          </div>

          {/* Recent Activity */}
          {stats.applications?.recentApplications && stats.applications.recentApplications.length > 0 && (
            <div className="space-y-4">
              <h2 className="text-center text-sm font-medium text-white/60 uppercase tracking-wider">
                Recent Activity
              </h2>
              <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
                <div className="space-y-3">
                  {stats.applications.recentApplications.slice(0, 3).map((app: any, index: number) => (
                    <div
                      key={index}
                      onClick={() => router.push(`/applications?id=${app.id}`)}
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-white/5 transition-colors cursor-pointer"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center">
                          <Briefcase className="w-5 h-5 text-white/70" />
                        </div>
                        <div>
                          <div className="text-white/90 text-sm">{app.jobTitle}</div>
                          <div className="text-white/50 text-xs">{app.companyName}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="w-3 h-3 text-white/40" />
                        <span className="text-white/50 text-xs">
                          {new Date(app.appliedAt).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                          })}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Motivational Quote */}
          <div className="text-center space-y-2 py-4">
            <p className="text-white/40 text-sm italic">
              "The future belongs to those who believe in the beauty of their dreams"
            </p>
            <p className="text-white/30 text-xs">- Eleanor Roosevelt</p>
          </div>
        </div>
      </div>

      {/* Congratulatory Modal for Hired Status */}
      <CongratulatoryModal
        isOpen={showHiredModal}
        onClose={() => setShowHiredModal(false)}
        jobInfo={hiredJobInfo}
      />
    </>
  );
};

export default ZenJobSeekerDashboard;