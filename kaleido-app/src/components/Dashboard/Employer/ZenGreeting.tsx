'use client';

import React from 'react';
import { useZenGreetings } from './hooks/useZenGreetings';

interface ZenGreetingProps {
  userName?: string;
}

export const ZenGreeting: React.FC<ZenGreetingProps> = ({ userName = 'there' }) => {
  const { primary, secondary, fadeClass } = useZenGreetings(userName);

  return (
    <div className={`transition-opacity duration-500 ${fadeClass} max-w-lg`}>
      {/* Primary Greeting - Large, elegant typography with wrapping */}
      <h1 className="text-4xl md:text-5xl lg:text-6xl font-extralight text-white mb-3 tracking-tight leading-[1.1] break-words">
        {primary}
      </h1>

      {/* Secondary Message - Subtle and supportive */}
      <p className="text-lg md:text-xl lg:text-2xl font-light text-white/70 tracking-wide max-w-md">
        {secondary}
      </p>
    </div>
  );
};
