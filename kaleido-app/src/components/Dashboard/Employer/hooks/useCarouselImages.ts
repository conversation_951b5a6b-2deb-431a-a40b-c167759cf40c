import { useState, useEffect, useCallback } from 'react';
import imageMetadata from '@/types/landing-dashboard.json';

export interface CarouselImage {
  path: string;
  location: string;
  mood: string;
  description: string;
}

const TRANSITION_DURATION = 120000; // 2 minutes
const PRELOAD_COUNT = 2; // Number of images to preload

export const useCarouselImages = () => {
  // Start with a random index on each page refresh
  const [currentIndex, setCurrentIndex] = useState(() =>
    Math.floor(Math.random() * imageMetadata.length)
  );
  const [images] = useState<CarouselImage[]>(imageMetadata);
  const [isPaused, setIsPaused] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [preloadedImages, setPreloadedImages] = useState<Set<string>>(new Set());

  // Get time-appropriate images based on mood
  const getTimeAppropriateImages = useCallback(() => {
    const hour = new Date().getHours();

    // Filter images by time-appropriate moods
    let moodFilter: string[] = [];

    if (hour >= 5 && hour < 9) {
      // Morning: hopeful, peaceful, serene
      moodFilter = ['hopeful', 'peaceful', 'serene', 'tranquil'];
    } else if (hour >= 9 && hour < 12) {
      // Late morning: expansive, flowing
      moodFilter = ['expansive', 'flowing', 'dynamic'];
    } else if (hour >= 12 && hour < 17) {
      // Afternoon: dynamic, vibrant, expansive
      moodFilter = ['dynamic', 'vibrant', 'expansive', 'urban'];
    } else if (hour >= 17 && hour < 20) {
      // Evening: calming, romantic, nostalgic
      moodFilter = ['calming', 'romantic', 'nostalgic', 'peaceful'];
    } else {
      // Night: mysterious, tranquil, relaxing
      moodFilter = ['mysterious', 'tranquil', 'relaxing', 'serene'];
    }

    // Filter images by mood, fallback to all if no matches
    const filteredImages = images.filter(img => moodFilter.includes(img.mood));
    return filteredImages.length > 0 ? filteredImages : images;
  }, [images]);

  // Preload images for smooth transitions
  const preloadImage = useCallback(
    (src: string) => {
      if (!preloadedImages.has(src)) {
        const img = new Image();
        img.src = src;
        img.onload = () => {
          setPreloadedImages(prev => new Set(prev).add(src));
        };
      }
    },
    [preloadedImages]
  );

  // Preload next images
  useEffect(() => {
    const appropriateImages = getTimeAppropriateImages();
    for (let i = 1; i <= PRELOAD_COUNT; i++) {
      const nextIndex = (currentIndex + i) % appropriateImages.length;
      preloadImage(appropriateImages[nextIndex].path);
    }
  }, [currentIndex, getTimeAppropriateImages, preloadImage]);

  // Auto-advance carousel
  useEffect(() => {
    if (isPaused) return;

    const timer = setInterval(() => {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentIndex(prev => {
          const appropriateImages = getTimeAppropriateImages();
          return (prev + 1) % appropriateImages.length;
        });
        setIsTransitioning(false);
      }, 2000); // 2 second transition
    }, TRANSITION_DURATION);

    return () => clearInterval(timer);
  }, [isPaused, getTimeAppropriateImages]);

  // Navigation functions with consistent 2-second transition like auto-advance
  const goToNext = useCallback(() => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setTimeout(() => {
      const appropriateImages = getTimeAppropriateImages();
      setCurrentIndex(prev => (prev + 1) % appropriateImages.length);
      setIsTransitioning(false);
    }, 2000); // Match auto-advance transition duration
  }, [isTransitioning, getTimeAppropriateImages]);

  const goToPrevious = useCallback(() => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setTimeout(() => {
      const appropriateImages = getTimeAppropriateImages();
      setCurrentIndex(prev => (prev - 1 + appropriateImages.length) % appropriateImages.length);
      setIsTransitioning(false);
    }, 2000); // Match auto-advance transition duration
  }, [isTransitioning, getTimeAppropriateImages]);

  const goToIndex = useCallback(
    (index: number) => {
      if (isTransitioning) return;
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentIndex(index);
        setIsTransitioning(false);
      }, 2000); // Match auto-advance transition duration
    },
    [isTransitioning]
  );

  // Keyboard controls
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowLeft':
          goToPrevious();
          break;
        case 'ArrowRight':
          goToNext();
          break;
        case ' ':
          e.preventDefault();
          setIsPaused(prev => !prev);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [goToNext, goToPrevious]);

  const appropriateImages = getTimeAppropriateImages();
  const currentImage = appropriateImages[currentIndex % appropriateImages.length];

  return {
    currentImage,
    currentIndex,
    totalImages: appropriateImages.length,
    isPaused,
    isTransitioning,
    goToNext,
    goToPrevious,
    goToIndex,
    togglePause: () => setIsPaused(prev => !prev),
    images: appropriateImages,
  };
};
