import { useState, useEffect } from 'react';

interface GreetingData {
  primary: string;
  secondary: string;
}

const getTimeBasedGreeting = (name: string): string => {
  const hour = new Date().getHours();
  const firstName = name.split(' ')[0]; // Use first name for more casual greetings

  const greetings: Record<string, string[]> = {
    earlyMorning: [
      // 5am-7am
      `Good morning, ${name} ☀️`,
      `Early bird gets the worm, ${firstName}`,
      `Peaceful morning to you, ${name}`,
      `Welcome to a new day, ${firstName}`,
      `The day is yours, ${name}`,
      `Rise and shine, ${firstName}`,
      `Morning meditation complete, ${name}?`,
      `${firstName}, the sunrise awaits`,
      `Namaste, ${name}`,
    ],
    morning: [
      // 7am-10am
      `Good morning, ${name}`,
      `Ready to make today count, ${firstName}?`,
      `Coffee first, ${firstName}?`,
      `Beautiful morning, isn't it, ${name}?`,
      `How are you feeling today, ${firstName}?`,
      `Let's ease into the day, ${name}`,
      `Morning momentum building nicely, ${firstName}`,
      `${firstName}, your team awaits`,
      `Welcome back, ${name}`,
    ],
    lateMorning: [
      // 10am-12pm
      `Having a productive morning, ${name}?`,
      `Hope your morning is going well, ${firstName}`,
      `Time for a quick stretch, ${name}?`,
      `Flowing through the morning, ${firstName}`,
      `You're doing great, ${name}`,
      `${firstName}, take a moment to breathe`,
      `Keep going, ${name}`,
    ],
    afternoon: [
      // 12pm-2pm
      `Good afternoon, ${name}`,
      `Lunch break time, ${firstName}?`,
      `Halfway through the day, ${name}`,
      `Time to recharge, ${firstName}?`,
      `Afternoon energy boost incoming, ${name}`,
      `Taking care of yourself today, ${firstName}?`,
      `${name}, remember to eat`,
      `Post-lunch clarity, ${firstName}?`,
    ],
    midAfternoon: [
      // 2pm-5pm
      `Afternoon momentum, ${name}`,
      `Stay hydrated, ${firstName}`,
      `The afternoon is yours, ${name}`,
      `Finding your flow, ${firstName}?`,
      `Gentle reminder to breathe, ${name}`,
      `You've got this, ${firstName}`,
      `${name}, almost there`,
      `Keep pushing, ${firstName}`,
    ],
    evening: [
      // 5pm-7pm
      `Good evening, ${name}`,
      `Wrapping up for the day, ${firstName}?`,
      `Time to transition, ${name}`,
      `Evening calm settling in, ${firstName}`,
      `How was your day, ${name}?`,
      `Sunset approaches, ${firstName}`,
      `${name}, well done today`,
      `Time flies, doesn't it, ${firstName}?`,
    ],
    lateEvening: [
      // 7pm-9pm
      `Winding down, ${name}?`,
      `Evening tranquility, ${firstName}`,
      `Time for yourself, ${name}`,
      `Reflecting on today's wins, ${firstName}?`,
      `Peaceful evening to you, ${name}`,
      `Family time, ${firstName}?`,
      `${name}, you've earned this rest`,
      `Relax, ${firstName}`,
    ],
    night: [
      // 9pm-12am
      `Still here, ${name}?`,
      `Night owl mode activated, ${firstName}`,
      `Remember to rest, ${name}`,
      `Tomorrow can wait, ${firstName}`,
      `Time to disconnect, ${name}?`,
      `The stars are out, ${firstName}`,
      `${name}, don't forget to sleep`,
      `Late night inspiration, ${firstName}?`,
    ],
    lateNight: [
      // 12am-5am
      `Burning the midnight oil, ${name}?`,
      `Can't sleep, ${firstName}?`,
      `Night thoughts, ${name}?`,
      `The world is quiet now, ${firstName}`,
      `Just checking in quickly, ${name}?`,
      `Early morning or late night, ${firstName}?`,
      `${name}, the moon watches over`,
      `Insomnia or dedication, ${firstName}?`,
    ],
  };

  let timeSlot: string;
  if (hour >= 5 && hour < 7) timeSlot = 'earlyMorning';
  else if (hour >= 7 && hour < 10) timeSlot = 'morning';
  else if (hour >= 10 && hour < 12) timeSlot = 'lateMorning';
  else if (hour >= 12 && hour < 14) timeSlot = 'afternoon';
  else if (hour >= 14 && hour < 17) timeSlot = 'midAfternoon';
  else if (hour >= 17 && hour < 19) timeSlot = 'evening';
  else if (hour >= 19 && hour < 21) timeSlot = 'lateEvening';
  else if (hour >= 21 && hour < 24) timeSlot = 'night';
  else timeSlot = 'lateNight';

  const greetingOptions = greetings[timeSlot];
  return greetingOptions[Math.floor(Math.random() * greetingOptions.length)];
};

const getSecondaryMessage = (name?: string): string => {
  const day = new Date().getDay();
  const hour = new Date().getHours();
  const firstName = name ? name.split(' ')[0] : '';

  const dayMessages: Record<number, string> = {
    1: 'Fresh week, fresh possibilities',
    2: 'Building momentum',
    3: 'Midweek balance',
    4: 'Almost there',
    5: 'Weekend is calling',
    6: 'Weekend vibes',
    0: 'Rest and recharge',
  };

  const motivational = [
    'What would you like to accomplish today?',
    'Your journey continues',
    'Progress, not perfection',
    'One step at a time',
    'Every moment matters',
    "You're exactly where you need to be",
    'Your potential is limitless',
    'Today is full of opportunities',
    'Trust the process',
  ];

  const statusUpdates = [
    'Your team is thriving',
    "3 new opportunities await when you're ready",
    'Everything is running smoothly',
    'All systems peaceful',
    'Your workspace is ready',
    'Updates can wait',
    'Your projects are on track',
    'The pipeline looks healthy',
    'Good news awaits your review',
  ];

  const mindfulPrompts = [
    'Take your time',
    'No rush today',
    'Breathe deeply',
    'Present moment awareness',
    'What brings you joy today?',
    'Remember why you started',
    'Listen to your intuition',
    'Embrace the calm',
    'Find your center',
  ];

  // Mix of messages based on time and randomness
  const allMessages = [...motivational, ...statusUpdates, ...mindfulPrompts];

  // Sometimes use day-specific message
  if (Math.random() < 0.3) {
    return dayMessages[day];
  }

  // Morning: more motivational
  if (hour >= 5 && hour < 12) {
    return motivational[Math.floor(Math.random() * motivational.length)];
  }

  // Afternoon: status updates
  if (hour >= 12 && hour < 17) {
    return statusUpdates[Math.floor(Math.random() * statusUpdates.length)];
  }

  // Evening: mindful prompts
  if (hour >= 17) {
    return mindfulPrompts[Math.floor(Math.random() * mindfulPrompts.length)];
  }

  return allMessages[Math.floor(Math.random() * allMessages.length)];
};

const getSpecialOccasionGreeting = (name: string): string | null => {
  const today = new Date();
  const month = today.getMonth() + 1;
  const day = today.getDate();
  const firstName = name.split(' ')[0];

  // Check for holidays
  if (month === 1 && day === 1) return `Happy New Year, ${name}! 🎊`;
  if (month === 12 && day === 25) return `Merry Christmas, ${name}! 🎄`;
  if (month === 12 && day === 31) return `Ready for the new year, ${firstName}?`;
  if (month === 2 && day === 14) return `Happy Valentine's Day, ${name}! 💝`;
  if (month === 10 && day === 31) return `Happy Halloween, ${firstName}! 🎃`;

  // Check for day-specific greetings
  const dayOfWeek = today.getDay();
  if (dayOfWeek === 1 && Math.random() < 0.2) return `Mindful Monday, ${name}`;
  if (dayOfWeek === 5 && Math.random() < 0.2) return `Feel-good Friday, ${firstName}!`;
  if (dayOfWeek === 3 && Math.random() < 0.15) return `Wonderful Wednesday, ${name}`;

  return null;
};

export const useZenGreetings = (userName: string = 'there') => {
  const [greeting, setGreeting] = useState<GreetingData>({
    primary: '',
    secondary: '',
  });
  const [fadeClass, setFadeClass] = useState('opacity-0');

  useEffect(() => {
    const updateGreeting = () => {
      // Check for special occasions first
      const specialGreeting = getSpecialOccasionGreeting(userName);

      const newGreeting: GreetingData = {
        primary: specialGreeting || getTimeBasedGreeting(userName),
        secondary: getSecondaryMessage(userName),
      };

      // Fade out
      setFadeClass('opacity-0');

      setTimeout(() => {
        setGreeting(newGreeting);
        // Fade in
        setFadeClass('opacity-100');
      }, 500);
    };

    // Initial greeting
    updateGreeting();

    // Update greeting every 5 minutes
    const interval = setInterval(updateGreeting, 5 * 60 * 1000);

    // Also update when user returns to tab
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        updateGreeting();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [userName]);

  return {
    primary: greeting.primary,
    secondary: greeting.secondary,
    fadeClass,
  };
};
