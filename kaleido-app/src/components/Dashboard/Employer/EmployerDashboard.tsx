'use client';

import React, { useState, useEffect, useCallback } from 'react';
import useUser from '@/hooks/useUser';
import ZenEmployerDashboard from './ZenEmployerDashboard';
import ClassicEmployerDashboard from './ClassicEmployerDashboard';
import { Grid3x3 } from 'lucide-react';

interface EmployerDashboardProps {
  stats: any;
  loading: boolean;
}

const EmployerDashboard: React.FC<EmployerDashboardProps> = ({ stats, loading }) => {
  const { user } = useUser();
  const userName = user?.given_name || user?.name?.split(' ')[0] || 'there';

  const [useClassicView, setUseClassicView] = useState(() => {
    // Check localStorage for classic view preference
    if (typeof window !== 'undefined') {
      return localStorage.getItem('classicViewEnabled') === 'true';
    }
    return false;
  });

  // Toggle classic view and save preference
  const toggleClassicView = useCallback(() => {
    setUseClassicView(prev => {
      const newValue = !prev;
      if (typeof window !== 'undefined') {
        localStorage.setItem('classicViewEnabled', String(newValue));
      }
      return newValue;
    });
  }, []);

  // Listen for keyboard shortcut and header toggle
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'v') {
        e.preventDefault();
        toggleClassicView();
      }
    };

    const handleDashboardToggle = (e: CustomEvent) => {
      setUseClassicView(e.detail.classic);
    };

    window.addEventListener('keydown', handleKeyPress);
    window.addEventListener('dashboardViewToggle' as any, handleDashboardToggle as any);

    return () => {
      window.removeEventListener('keydown', handleKeyPress);
      window.removeEventListener('dashboardViewToggle' as any, handleDashboardToggle as any);
    };
  }, [toggleClassicView]);

  if (useClassicView) {
    return <ClassicEmployerDashboard stats={stats} loading={loading} />;
  }

  return <ZenEmployerDashboard stats={stats} loading={loading} userName={userName} />;
};

export default EmployerDashboard;
