'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { ChevronLeft, ChevronRight, Pause, Play } from 'lucide-react';
import { useCarouselImages } from './hooks/useCarouselImages';
import { SIDEBAR } from '@/constants/layout';

export const BackgroundCarousel: React.FC = () => {
  const [sidebarExpanded, setSidebarExpanded] = useState(false);

  // Get sidebar state from localStorage and listen for changes
  useEffect(() => {
    const checkSidebarState = () => {
      const savedState = localStorage.getItem('sidebarExpanded');
      if (savedState !== null) {
        setSidebarExpanded(JSON.parse(savedState));
      }
    };

    // Initial check
    checkSidebarState();

    // Listen for storage changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'sidebarExpanded') {
        checkSidebarState();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Listen for custom event for same-tab changes
    const handleSidebarToggle = (e: CustomEvent) => {
      setSidebarExpanded(e.detail.expanded);
    };

    window.addEventListener('sidebar-toggle' as any, handleSidebarToggle as any);

    // Also check periodically for same-tab changes
    const interval = setInterval(checkSidebarState, 100);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('sidebar-toggle' as any, handleSidebarToggle as any);
      clearInterval(interval);
    };
  }, []);

  const {
    currentImage,
    currentIndex,
    totalImages,
    isPaused,
    isTransitioning,
    goToNext,
    goToPrevious,
    goToIndex,
    togglePause,
    images,
  } = useCarouselImages();

  return (
    <div className="absolute inset-0 w-full h-full">
      {/* Main Image with Ken Burns Effect */}
      <div
        className={`absolute inset-0 transition-opacity duration-[2000ms] ${isTransitioning ? 'opacity-0' : 'opacity-100'}`}
      >
        <div className={`absolute inset-0 ${!isPaused ? 'animate-ken-burns' : ''}`}>
          <Image
            src={currentImage.path}
            alt={currentImage.description}
            fill
            priority
            quality={90}
            className="object-cover scale-110"
            sizes="100vw"
          />
        </div>
      </div>

      {/* Navigation Controls - Positioned near bottom, above metrics */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex items-center gap-3 z-20">
        {/* Previous Button */}
        <button
          onClick={goToPrevious}
          className="p-1.5 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-200"
          aria-label="Previous image"
        >
          <ChevronLeft className="w-3 h-3 text-white/60" />
        </button>

        {/* Dots Indicator */}
        <div className="flex items-center gap-1.5">
          {images.slice(0, 10).map((_, index) => (
            <button
              key={index}
              onClick={() => goToIndex(index)}
              className={`transition-all duration-300 ${
                index === currentIndex
                  ? 'w-6 h-1.5 bg-white/60'
                  : 'w-1.5 h-1.5 bg-white/30 hover:bg-white/40'
              } rounded-full`}
              aria-label={`Go to image ${index + 1}`}
            />
          ))}
          {totalImages > 10 && (
            <span className="text-white/30 text-[10px] ml-1">+{totalImages - 10}</span>
          )}
        </div>

        {/* Next Button */}
        <button
          onClick={goToNext}
          className="p-1.5 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-200"
          aria-label="Next image"
        >
          <ChevronRight className="w-3 h-3 text-white/60" />
        </button>

        {/* Pause/Play Button */}
        <button
          onClick={togglePause}
          className="p-1.5 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-200 ml-2"
          aria-label={isPaused ? 'Play carousel' : 'Pause carousel'}
        >
          {isPaused ? (
            <Play className="w-3 h-3 text-white/60" />
          ) : (
            <Pause className="w-3 h-3 text-white/60" />
          )}
        </button>
      </div>

      {/* Location Display - Bottom right corner */}
      {currentImage.location !== 'Unknown' && (
        <div className="absolute bottom-8 right-8 z-20">
          <div className="flex items-center gap-2 text-white/40">
            <span className="text-sm font-extralight">|</span>
            <span className="text-sm font-light tracking-wide">{currentImage.location}</span>
          </div>
        </div>
      )}

      {/* Keyboard Shortcuts Hint - Bottom left, very subtle */}
      <div
        className="absolute bottom-8 z-20"
        style={{
          left:
            typeof window !== 'undefined' && window.innerWidth >= 1024
              ? `calc(${sidebarExpanded ? SIDEBAR.EXPANDED_WIDTH : SIDEBAR.COLLAPSED_WIDTH} + 2rem)`
              : '2rem',
        }}
      >
        <div className="flex items-center gap-3 text-white/20 text-[11px] font-light">
          <span>← → Navigate</span>
          <span className="text-white/10">·</span>
          <span>Space to pause</span>
        </div>
      </div>
    </div>
  );
};
