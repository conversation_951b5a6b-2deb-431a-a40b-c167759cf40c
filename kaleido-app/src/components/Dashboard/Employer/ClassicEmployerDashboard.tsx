'use client';

import React from 'react';
import useUser from '@/hooks/useUser';

import {
  BarChart3,
  Briefcase,
  CreditCard,
  FileText,
  PlusCircle,
  Search,
  Star,
  Users,
  Sparkles,
} from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

import { AnimatedBackground } from '../AnimatedBackground';
import { ReferralProgramBanner } from '@/components/shared/ReferralProgramBanner';
import { ClassicDashboardHeader } from './ClassicDashboardHeader';

interface Job {
  id: string;
  companyName: string;
  jobType: string;
  status: string;
  candidateCount: number;
  createdAt: string | Date;
}

interface ClassicEmployerDashboardProps {
  stats: any;
  loading: boolean;
}

const ClassicEmployerDashboard: React.FC<ClassicEmployerDashboardProps> = ({ stats, loading }) => {
  const router = useRouter();
  const { user } = useUser();
  const userName = user?.given_name || user?.name?.split(' ')[0] || 'there';

  if (loading) {
    return (
      <div className="relative flex h-full w-full items-center p-3">
        <div className="w-full max-w-7xl">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div
                key={i}
                className="col-span-1 animate-pulse bg-white/5 rounded-xl min-h-[180px]"
              />
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Navigate to job creation page
  const handleCreateJob = () => {
    router.push('/job-description-creation');
  };

  // Navigate to candidate search page
  const handleSearchCandidates = () => {
    router.push('/talent-hub');
  };

  // Navigate to manage jobs page
  const handleManageJobs = () => {
    router.push('/jobs');
  };

  // Navigate to subscription page
  const handleViewSubscription = () => {
    router.push('/company-settings?tab=subscription-payments');
  };

  return (
    <div className="relative min-h-screen">
      <AnimatedBackground />
      
      {/* Inspirational Header Section - Fixed position */}
      <ClassicDashboardHeader userName={userName} stats={stats} />
      
      {/* Main Content - With proper spacing for fixed header */}
      <div className="relative pt-[280px]">
        <div className="p-3 sm:p-4">
          <div className="w-full max-w-7xl mx-auto">
          {/* Referral Program Banner */}
          <ReferralProgramBanner userType="employer" />

          {/* Quick Actions */}
          <div className="mb-8">
            <h2 className="text-xs font-medium text-white/60 mb-4 uppercase tracking-wider">
              Quick Actions
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              {/* Create Job Description Card */}
              <div
                className="group relative overflow-hidden border border-white/5 bg-white/5 hover:bg-white/10 backdrop-blur-sm rounded-xl transition-all duration-300 cursor-pointer p-6"
                onClick={handleCreateJob}
              >
                <div className="flex flex-col gap-3">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-pink-500/10">
                      <FileText className="w-5 h-5 text-pink-400" />
                    </div>
                    <h3 className="text-sm font-medium text-white">Create Job Description</h3>
                  </div>
                  <p className="text-xs text-white/60 leading-relaxed">
                    Create a new job description to find the perfect candidates
                  </p>
                </div>
                <div className="absolute inset-x-0 bottom-0 h-[1px] bg-gradient-to-r from-transparent via-pink-500/40 to-transparent scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></div>
              </div>

              {/* Talent Hub Card */}
              <div
                className="group relative overflow-hidden border border-white/5 bg-white/5 hover:bg-white/10 backdrop-blur-sm rounded-xl transition-all duration-300 cursor-pointer p-6"
                onClick={handleSearchCandidates}
              >
                <div className="flex flex-col gap-3">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-blue-500/10">
                      <Search className="w-5 h-5 text-blue-400" />
                    </div>
                    <h3 className="text-sm font-medium text-white">Talent Hub</h3>
                  </div>
                  <p className="text-xs text-white/60 leading-relaxed">
                    Browse and search through our database of qualified candidates
                  </p>
                </div>
                <div className="absolute inset-x-0 bottom-0 h-[1px] bg-gradient-to-r from-transparent via-blue-500/40 to-transparent scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></div>
              </div>

              {/* Match & Rank Card */}
              <div
                className="group relative overflow-hidden border border-white/5 bg-white/5 hover:bg-white/10 backdrop-blur-sm rounded-xl transition-all duration-300 cursor-pointer p-6"
                onClick={handleManageJobs}
              >
                <div className="flex flex-col gap-3">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-purple-500/10">
                      <Star className="w-5 h-5 text-purple-400" />
                    </div>
                    <h3 className="text-sm font-medium text-white">Match & Rank</h3>
                  </div>
                  <p className="text-xs text-white/60 leading-relaxed">
                    View and manage your jobs with AI-powered candidate matching
                  </p>
                </div>
                <div className="absolute inset-x-0 bottom-0 h-[1px] bg-gradient-to-r from-transparent via-purple-500/40 to-transparent scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></div>
              </div>
            </div>
          </div>


          {/* Recent Jobs and Subscription in two columns */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {/* Recent Jobs */}
            <div>
              <h2 className="text-xs font-medium text-white/60 mb-4 uppercase tracking-wider">
                Recent Jobs
              </h2>
              <Card className="border-white/5 bg-white/5 backdrop-blur-sm hover:bg-white/10 transition-all duration-200">
                <CardContent className="p-4">
                  {stats.jobs?.recentJobs && stats.jobs.recentJobs.length > 0 ? (
                    <div className="overflow-hidden">
                      <table className="w-full">
                        <tbody>
                          {stats.jobs.recentJobs.slice(0, 3).map((job: Job, index: number) => (
                            <tr
                              key={index}
                              className="border-b border-white/5 hover:bg-purple-500/5 cursor-pointer transition-all duration-200"
                              onClick={() => router.push(`/jobs/${job.id}/manage`)}
                            >
                              <td className="py-2.5">
                                <div className="flex items-center gap-2">
                                  <Briefcase className="w-3.5 h-3.5 text-pink-400 shrink-0" />
                                  <span className="text-white/80 text-xs font-medium truncate">
                                    {job.jobType}
                                  </span>
                                </div>
                                <div className="flex items-center gap-3 mt-1.5">
                                  <div className="flex items-center gap-1">
                                    <Users className="w-3 h-3 text-pink-400/60 shrink-0" />
                                    <span className="text-white/60 text-xs">
                                      {job.candidateCount}
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <FileText className="w-3 h-3 text-pink-400/60 shrink-0" />
                                    <span className="text-white/60 text-xs">
                                      {new Date(
                                        typeof job.createdAt === 'string'
                                          ? job.createdAt
                                          : job.createdAt.toISOString()
                                      ).toLocaleDateString('en-US', {
                                        month: 'short',
                                        day: 'numeric',
                                      })}
                                    </span>
                                  </div>
                                </div>
                              </td>
                              <td className="py-2.5 text-right align-top">
                                {job.status === 'NEW' && (
                                  <PlusCircle className="w-4 h-4 text-purple-300/70 ml-auto" />
                                )}
                                {job.status === 'MATCHED' && (
                                  <Star className="w-4 h-4 text-blue-300/70 ml-auto" />
                                )}
                                {job.status === 'OPEN' && (
                                  <Briefcase className="w-4 h-4 text-green-300/70 ml-auto" />
                                )}
                                {!['NEW', 'MATCHED', 'OPEN'].includes(job.status) && (
                                  <FileText className="w-4 h-4 text-gray-300/70 ml-auto" />
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-white/60 text-xs">No recent jobs found</p>
                      <Button
                        onClick={handleCreateJob}
                        variant="link"
                        className="text-pink-400 text-xs mt-2"
                      >
                        Create your first job
                      </Button>
                    </div>
                  )}
                  {stats.jobs?.recentJobs && stats.jobs.recentJobs.length > 0 && (
                    <div className="mt-2 text-center">
                      <Button
                        onClick={() => router.push('/jobs')}
                        variant="link"
                        className="text-pink-400 text-xs py-1"
                      >
                        View all jobs
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Credit Usage */}
            <div>
              <h2 className="text-xs font-medium text-white/60 mb-4 uppercase tracking-wider">
                Credit Usage
              </h2>
              <Card className="border-white/5 bg-white/5 backdrop-blur-sm hover:bg-white/10 transition-all duration-200">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm flex items-center text-white">
                      <CreditCard className="w-4 h-4 text-pink-400 mr-2" />
                      {stats.subscription?.plan || 'Free'} Plan
                    </CardTitle>
                    <Badge className="bg-purple-500/20 text-purple-300">
                      {stats.subscription?.daysRemaining || 30} days remaining
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="pb-4">
                  {stats.subscription?.credits ? (
                    <div className="space-y-4">
                      {/* Credit Usage Overview */}
                      <div className="bg-purple-300/5 rounded-lg p-3 border border-white/5">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-xs text-white/80">Credits</span>
                          <span className="text-xs font-medium text-white/90">
                            {stats.subscription.credits.usedCredits} /{' '}
                            {stats.subscription.credits.totalCredits} used
                          </span>
                        </div>
                        <Progress
                          value={Math.min(
                            (stats.subscription.credits.usedCredits /
                              stats.subscription.credits.totalCredits) *
                              100,
                            100
                          )}
                          className="h-2 bg-purple-500/10"
                          indicatorClassName="bg-gradient-to-r from-pink-500 to-purple-500"
                        />
                        <div className="text-center mt-2">
                          <span className="text-lg font-bold text-white">
                            {stats.subscription.credits.remainingCredits}
                          </span>
                          <span className="text-xs text-white/70 ml-1">credits remaining</span>
                        </div>
                      </div>

                      {/* Credit Costs */}
                      <div className="space-y-2">
                        <h4 className="text-xs font-medium text-white/80">Credit Costs</h4>
                        <div className="space-y-1.5">
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-white/70">Job Description</span>
                            <Badge className="bg-green-500/20 text-green-300 text-xs">FREE</Badge>
                          </div>
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-white/70">Match & Rank</span>
                            <Badge className="bg-purple-500/20 text-purple-300 text-xs">
                              1 credit
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-white/70">LinkedIn Scout</span>
                            <Badge className="bg-purple-500/20 text-purple-300 text-xs">
                              1 credit
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-white/70">Video JD</span>
                            <Badge className="bg-purple-500/20 text-purple-300 text-xs">
                              25 credits
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-white/60 text-xs">No credit data available</p>
                    </div>
                  )}
                  <CardFooter className="px-0 pt-2 pb-0">
                    <Button
                      variant="link"
                      className="text-pink-400 text-xs p-0"
                      onClick={handleViewSubscription}
                    >
                      View subscription details
                    </Button>
                  </CardFooter>
                </CardContent>
              </Card>
            </div>
            </div>
          </div>
        </div>
      </div>

      {/* Fallback Toggle Button - Top right corner */}
      <button
        onClick={() => {
          const newValue = false; // Switch to zen view
          if (typeof window !== 'undefined') {
            localStorage.setItem('classicViewEnabled', String(newValue));
            window.dispatchEvent(
              new CustomEvent('dashboardViewToggle', { detail: { classic: newValue } })
            );
            // Force refresh to apply changes
            window.location.reload();
          }
        }}
        className="fixed top-24 right-8 z-40 px-3 py-2 rounded-full bg-purple-600/20 backdrop-blur-sm hover:bg-purple-600/30 transition-all duration-200 flex items-center gap-2 group"
        aria-label="Switch to zen mode"
        title="Switch to Zen Mode (Cmd/Ctrl + Shift + V)"
      >
        <Sparkles className="h-4 w-4 text-purple-300 group-hover:text-purple-200" />
      </button>
    </div>
  );
};

export default ClassicEmployerDashboard;
