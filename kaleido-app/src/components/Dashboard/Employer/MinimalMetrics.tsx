'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import {
  Briefcase,
  Users,
  Star,
  CreditCard,
  PlusCircle,
  Search,
  MessageSquare,
} from 'lucide-react';

interface MinimalMetricsProps {
  stats: {
    jobs?: {
      statusBreakdown?: {
        open?: number;
      };
    };
    candidates?: {
      total?: number;
      matched?: number;
    };
    subscription?: {
      credits?: {
        usedCredits?: number;
        totalCredits?: number;
        remainingCredits?: number;
      };
    };
    messages?: {
      unread?: number;
    };
  };
}

export const MinimalMetrics: React.FC<MinimalMetricsProps> = ({ stats }) => {
  const router = useRouter();

  const handleCreateJob = () => router.push('/job-description-creation');
  const handleSearchCandidates = () => router.push('/talent-hub');
  const handleViewMessages = () => router.push('/messages');

  return (
    <div className="relative w-full">
      {/* Metrics Container - Full width with proper spacing */}
      <div className="flex flex-wrap items-end justify-between gap-4 lg:gap-8">
        {/* Left Section - Key Metrics */}
        <div className="flex flex-wrap items-center gap-4 lg:gap-6">
          {/* Active Jobs */}
          <div
            className="flex items-center gap-3 group cursor-pointer backdrop-blur-sm bg-white/5 rounded-lg px-4 py-3 hover:bg-white/10 transition-all duration-200"
            onClick={() => router.push('/jobs')}
          >
            <Briefcase className="w-5 h-5 text-white/70" />
            <div>
              <p className="text-[10px] uppercase tracking-wider text-white/50">Active Jobs</p>
              <p className="text-2xl font-extralight text-white">
                {stats.jobs?.statusBreakdown?.open || 0}
              </p>
            </div>
          </div>

          {/* Total Candidates */}
          <div
            className="flex items-center gap-3 group cursor-pointer backdrop-blur-sm bg-white/5 rounded-lg px-4 py-3 hover:bg-white/10 transition-all duration-200"
            onClick={handleSearchCandidates}
          >
            <Users className="w-5 h-5 text-white/70" />
            <div>
              <p className="text-[10px] uppercase tracking-wider text-white/50">Candidates</p>
              <p className="text-2xl font-extralight text-white">{stats.candidates?.total || 0}</p>
            </div>
          </div>

          {/* New Matches */}
          <div
            className="flex items-center gap-3 group cursor-pointer backdrop-blur-sm bg-white/5 rounded-lg px-4 py-3 hover:bg-white/10 transition-all duration-200"
            onClick={() => router.push('/jobs')}
          >
            <Star className="w-5 h-5 text-white/70" />
            <div>
              <p className="text-[10px] uppercase tracking-wider text-white/50">New Matches</p>
              <p className="text-2xl font-extralight text-white">
                {stats.candidates?.matched || 0}
              </p>
            </div>
          </div>

          {/* Credits */}
          <div
            className="flex items-center gap-3 group cursor-pointer backdrop-blur-sm bg-white/5 rounded-lg px-4 py-3 hover:bg-white/10 transition-all duration-200"
            onClick={() => router.push('/company-settings?tab=subscription-payments')}
          >
            <CreditCard className="w-5 h-5 text-white/70" />
            <div>
              <p className="text-[10px] uppercase tracking-wider text-white/50">Credits</p>
              <p className="text-lg font-extralight text-white">
                <span className="text-2xl">
                  {stats.subscription?.credits?.remainingCredits || 0}
                </span>
                <span className="text-sm text-white/40 ml-1">
                  /{stats.subscription?.credits?.totalCredits || 0}
                </span>
              </p>
            </div>
          </div>
        </div>

        {/* Right Section - Quick Actions - Moved left to avoid Intercom */}
        <div className="flex items-center gap-4 mb-8 mr-20">
          <div className="flex flex-col items-center gap-1">
            <button
              onClick={handleCreateJob}
              className="p-3 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-200 group"
              aria-label="Create new job"
            >
              <PlusCircle className="w-5 h-5 text-white/70 group-hover:text-white/90" />
            </button>
            <span className="text-[10px] text-white/50 font-light">Create JD</span>
          </div>
          <div className="flex flex-col items-center gap-1">
            <button
              onClick={handleSearchCandidates}
              className="p-3 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-200 group"
              aria-label="Search talent"
            >
              <Search className="w-5 h-5 text-white/70 group-hover:text-white/90" />
            </button>
            <span className="text-[10px] text-white/50 font-light">Talent Hub</span>
          </div>
          <div className="flex flex-col items-center gap-1">
            <button
              onClick={() => router.push('/jobs')}
              className="p-3 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-200 group"
              aria-label="Match and rank"
            >
              <Star className="w-5 h-5 text-white/70 group-hover:text-white/90" />
            </button>
            <span className="text-[10px] text-white/50 font-light">Match & Rank</span>
          </div>
        </div>
      </div>
    </div>
  );
};
