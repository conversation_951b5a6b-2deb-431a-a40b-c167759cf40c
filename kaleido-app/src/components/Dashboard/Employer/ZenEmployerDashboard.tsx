'use client';

import React, { useState, useEffect } from 'react';
import { BackgroundCarousel } from './BackgroundCarousel';
import { ZenGreeting } from './ZenGreeting';
import { MinimalMetrics } from './MinimalMetrics';
import { Grid3x3 } from 'lucide-react';
import { SIDEBAR } from '@/constants/layout';

interface ZenEmployerDashboardProps {
  stats: any;
  loading: boolean;
  userName?: string;
}

const ZenEmployerDashboard: React.FC<ZenEmployerDashboardProps> = ({
  stats,
  loading,
  userName = 'Michael',
}) => {
  const [showMetrics, setShowMetrics] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);

  // Title case the username
  const formattedUserName = userName
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');

  // Get sidebar state from localStorage and listen for changes
  useEffect(() => {
    const checkSidebarState = () => {
      const savedState = localStorage.getItem('sidebarExpanded');
      if (savedState !== null) {
        setSidebarExpanded(JSON.parse(savedState));
      }
    };

    // Initial check
    checkSidebarState();

    // Listen for storage changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'sidebarExpanded') {
        checkSidebarState();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Also check periodically for same-tab changes
    const interval = setInterval(checkSidebarState, 100);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, []);

  // Show metrics after a delay for smoother entrance
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowMetrics(true);
    }, 2000);
    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return (
      <div className="fixed inset-0 bg-gradient-to-br from-purple-950 via-purple-900 to-pink-900 flex items-center justify-center z-0">
        <div className="animate-pulse">
          <div className="w-32 h-32 rounded-full bg-white/10 backdrop-blur-sm" />
        </div>
      </div>
    );
  }

  // Calculate padding for sidebar
  const sidebarPadding =
    typeof window !== 'undefined' && window.innerWidth >= 1024
      ? sidebarExpanded
        ? SIDEBAR.EXPANDED_WIDTH
        : SIDEBAR.COLLAPSED_WIDTH
      : '0px';

  return (
    <div className="fixed inset-0 z-0">
      {/* Background Carousel - Full viewport */}
      <BackgroundCarousel />

      {/* Gradient Overlays - Multiple layers for smooth transition */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Base gradient - subtle darkening from bottom */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent" />

        {/* Purple-pink gradient overlay */}
        <div className="absolute bottom-0 left-0 right-0 h-2/3 bg-gradient-to-t from-purple-950/90 via-purple-900/50 to-transparent" />

        {/* Pink accent gradient */}
        <div className="absolute bottom-0 left-0 right-0 h-1/2 bg-gradient-to-t from-pink-950/40 via-transparent to-transparent" />

        {/* Strong bottom gradient for text readability */}
        <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
      </div>

      {/* Content Container - Greeting and Metrics */}
      <div className="absolute inset-0 z-10 flex flex-col justify-end">
        {/* Content with sidebar padding */}
        <div
          className="relative pb-8"
          style={{
            paddingLeft:
              typeof window !== 'undefined' && window.innerWidth >= 1024
                ? `calc(2rem + ${sidebarPadding})`
                : '2rem',
            paddingRight: '2rem',
          }}
        >
          {/* Greeting - Positioned much higher up with more space from navigation */}
          <div
            className={`transition-all duration-1000 mb-24 ${showMetrics ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}
          >
            <ZenGreeting userName={formattedUserName} />
          </div>

          {/* Minimal Metrics - Higher up from navigation controls */}
          <div
            className={`transition-all duration-1000 mb-20 ${showMetrics ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}
          >
            <MinimalMetrics stats={stats} />
          </div>
        </div>
      </div>

      {/* Fallback Toggle Button - Top right corner */}
      <button
        onClick={() => {
          const newValue = true; // Switch to classic view
          if (typeof window !== 'undefined') {
            localStorage.setItem('classicViewEnabled', String(newValue));
            window.dispatchEvent(
              new CustomEvent('dashboardViewToggle', { detail: { classic: newValue } })
            );
            // Force refresh to apply changes
            window.location.reload();
          }
        }}
        className="absolute top-24 right-8 z-40 px-3 py-2 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-200 flex items-center gap-2 group"
        aria-label="Switch to classic view"
        title="Switch to Classic View (Cmd/Ctrl + Shift + V)"
      >
        <Grid3x3 className="h-4 w-4 text-white/70 group-hover:text-white/90" />
      </button>
    </div>
  );
};

export default ZenEmployerDashboard;
