'use client';

import { SIDEBAR } from '@/constants/layout';
import imageMetadata from '@/types/landing-dashboard.json';
import { AnimatePresence, motion } from 'framer-motion';
import {
  Briefcase,
  ChevronLeft,
  ChevronRight,
  CreditCard,
  FileText,
  Pause,
  Play,
  Sparkles,
  Star,
  Users,
} from 'lucide-react';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';

interface ClassicDashboardHeaderProps {
  userName?: string;
  stats?: {
    jobs?: { statusBreakdown?: { open?: number } };
    candidates?: { total?: number; matched?: number };
    subscription?: { credits?: { remainingCredits?: number; totalCredits?: number } };
    metrics?: { totalUploads?: number };
  };
}

// Professional inspirational quotes for employers
const getInspirationalQuotes = () => {
  const quotes = [
    { text: 'Great vision without great people is irrelevant.', author: '<PERSON>' },
    { text: 'The strength of the team is each individual member.', author: '<PERSON>' },
    { text: 'Talent wins games, but teamwork wins championships.', author: '<PERSON>' },
    { text: 'Culture eats strategy for breakfast.', author: '<PERSON>' },
    { text: 'Hire character. Train skill.', author: '<PERSON>' },
    { text: 'The best way to predict the future is to create it.', author: '<PERSON> Drucker' },
    { text: "Success is best when it's shared.", author: '<PERSON> <PERSON>' },
    { text: 'Innovation distinguishes between a leader and a follower.', author: '<PERSON> Jobs' },
    { text: "Quality is everyone's responsibility.", author: 'W. <PERSON> Deming' },
    { text: "Excellence is not a skill, it's an attitude.", author: 'Ralph Marston' },
  ];

  return quotes[Math.floor(Math.random() * quotes.length)];
};

export const ClassicDashboardHeader: React.FC<ClassicDashboardHeaderProps> = ({
  userName = 'there',
  stats,
}) => {
  const [currentQuote, setCurrentQuote] = useState(getInspirationalQuotes());
  const [currentImageIndex, setCurrentImageIndex] = useState(() =>
    Math.floor(Math.random() * imageMetadata.length)
  );
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [fadeClass, setFadeClass] = useState('opacity-100');
  const [isPaused, setIsPaused] = useState(false);

  // Get greeting based on time
  const getGreeting = () => {
    const hour = new Date().getHours();
    const name = userName.split(' ')[0];

    if (hour >= 5 && hour < 12) return `Good morning, ${name}`;
    if (hour >= 12 && hour < 17) return `Good afternoon, ${name}`;
    if (hour >= 17 && hour < 21) return `Good evening, ${name}`;
    return `Welcome back, ${name}`;
  };

  // Carousel navigation functions
  const goToNext = () => {
    setCurrentImageIndex(prev => (prev + 1) % imageMetadata.length);
  };

  const goToPrevious = () => {
    setCurrentImageIndex(prev => (prev - 1 + imageMetadata.length) % imageMetadata.length);
  };

  const goToIndex = (index: number) => {
    setCurrentImageIndex(index);
  };

  const togglePause = () => {
    setIsPaused(prev => !prev);
  };

  // Rotate quotes every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setFadeClass('opacity-0');
      setTimeout(() => {
        setCurrentQuote(getInspirationalQuotes());
        setFadeClass('opacity-100');
      }, 500);
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  // Rotate images every minute (only when not paused)
  useEffect(() => {
    if (isPaused) return;

    const interval = setInterval(() => {
      setCurrentImageIndex(prev => (prev + 1) % imageMetadata.length);
    }, 60000);

    return () => clearInterval(interval);
  }, [isPaused]);

  // Get sidebar state
  useEffect(() => {
    const checkSidebarState = () => {
      const savedState = localStorage.getItem('sidebarExpanded');
      if (savedState !== null) {
        setSidebarExpanded(JSON.parse(savedState));
      }
    };

    checkSidebarState();
    const interval = setInterval(checkSidebarState, 100);

    return () => clearInterval(interval);
  }, []);

  const sidebarPadding =
    typeof window !== 'undefined' && window.innerWidth >= 1024
      ? sidebarExpanded
        ? SIDEBAR.EXPANDED_WIDTH
        : SIDEBAR.COLLAPSED_WIDTH
      : '0px';

  const currentImage = imageMetadata[currentImageIndex];

  return (
    <div className="fixed top-0 left-0 right-0 h-[320px] overflow-hidden z-0">
      {/* Background Image with Animation */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentImageIndex}
          initial={{ opacity: 0, scale: 1.05 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.5, ease: 'easeInOut' }}
          className="absolute inset-0 z-0"
        >
          <Image
            src={currentImage.path}
            alt={currentImage.description}
            fill
            priority
            quality={100}
            className="object-cover object-[center_35%]"
            sizes="100vw"
          />
        </motion.div>
      </AnimatePresence>

      {/* Very faint overlay */}
      <div className="absolute inset-0 z-5 bg-black/10" />

      {/* Additional decorative layers */}
      <div className="absolute inset-0 z-10">
        {/* Darker blackish-purple gradient at bottom */}
        <div className="absolute inset-0 bg-gradient-to-t from-purple-950/90  via-purple-900/40 via-25% via-purple-800/30 via-35% to-transparent" />

        {/* Dynamic corner accents */}
        <motion.div
          className="absolute top-0 right-0 w-64 h-64 bg-gradient-radial from-purple-600/15 via-transparent to-transparent blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />

        <motion.div
          className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-radial from-teal-600/15 via-transparent to-transparent blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2,
          }}
        />
      </div>

      {/* Content overlay */}
      <div className="relative z-20 w-full h-full flex items-end">
        <div
          className="w-full px-4 sm:px-6 lg:px-8 pb-6"
          style={{
            paddingLeft:
              typeof window !== 'undefined' && window.innerWidth >= 1024
                ? `calc(1rem + ${sidebarPadding})`
                : '1rem',
          }}
        >
          <div className="flex items-end justify-between">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="flex-1"
            >
              {/* Greeting */}
              <div className="flex items-center mb-2">
                <h1 className="text-2xl sm:text-3xl font-light text-white">{getGreeting()}</h1>
              </div>
              <p className="text-sm text-white/60 max-w-3xl mb-3">Your recruitment dashboard</p>

              {/* Inspirational Quote */}
              <div
                className={`transition-opacity duration-500 ${fadeClass} flex items-start gap-3 max-w-2xl`}
              >
                <Sparkles className="w-5 h-5 text-yellow-400/70 flex-shrink-0 mt-1" />
                <div>
                  <p className="text-white/80 text-sm font-light italic">"{currentQuote.text}"</p>
                  <p className="text-white/50 text-xs mt-1">— {currentQuote.author}</p>
                </div>
              </div>
            </motion.div>

            {/* Key Metrics - Bottom right */}
            <motion.div
              className="hidden lg:flex items-center gap-3"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              {/* Open Jobs */}
              <div className="flex items-center gap-2 backdrop-blur-sm bg-white/10 rounded-lg px-3 py-2">
                <Briefcase className="w-4 h-4 text-white/70" />
                <div>
                  <p className="text-[10px] uppercase tracking-wider text-white/50">Open Jobs</p>
                  <p className="text-lg font-extralight text-white">
                    {stats?.jobs?.statusBreakdown?.open || 0}
                  </p>
                </div>
              </div>

              {/* Total Candidates */}
              <div className="flex items-center gap-2 backdrop-blur-sm bg-white/10 rounded-lg px-3 py-2">
                <Users className="w-4 h-4 text-white/70" />
                <div>
                  <p className="text-[10px] uppercase tracking-wider text-white/50">
                    Total Candidates
                  </p>
                  <p className="text-lg font-extralight text-white">
                    {stats?.candidates?.total || 0}
                  </p>
                </div>
              </div>

              {/* Matched */}
              <div className="flex items-center gap-2 backdrop-blur-sm bg-white/10 rounded-lg px-3 py-2">
                <Star className="w-4 h-4 text-white/70" />
                <div>
                  <p className="text-[10px] uppercase tracking-wider text-white/50">Matched</p>
                  <p className="text-lg font-extralight text-white">
                    {stats?.candidates?.matched || 0}
                  </p>
                </div>
              </div>

              {/* Uploads */}
              <div className="flex items-center gap-2 backdrop-blur-sm bg-white/10 rounded-lg px-3 py-2">
                <FileText className="w-4 h-4 text-white/70" />
                <div>
                  <p className="text-[10px] uppercase tracking-wider text-white/50">Uploads</p>
                  <p className="text-lg font-extralight text-white">
                    {stats?.metrics?.totalUploads || 0}
                  </p>
                </div>
              </div>

              {/* Credits */}
              <div className="flex items-center gap-2 backdrop-blur-sm bg-white/10 rounded-lg px-3 py-2">
                <CreditCard className="w-4 h-4 text-white/70" />
                <div>
                  <p className="text-[10px] uppercase tracking-wider text-white/50">Credits</p>
                  <p className="text-sm font-extralight text-white">
                    <span className="text-lg">
                      {stats?.subscription?.credits?.remainingCredits || 0}
                    </span>
                    <span className="text-xs text-white/40 ml-1">
                      /{stats?.subscription?.credits?.totalCredits || 0}
                    </span>
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Carousel Navigation Controls - Bottom center */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex items-center gap-3 z-30">
        {/* Previous Button */}
        <button
          type="button"
          onClick={goToPrevious}
          className="p-1.5 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-200"
          aria-label="Previous image"
        >
          <ChevronLeft className="w-3 h-3 text-white/60" />
        </button>

        {/* Dots Indicator */}
        <div className="flex items-center gap-1.5">
          {imageMetadata.slice(0, 10).map((_, index) => (
            <button
              key={index}
              onClick={() => goToIndex(index)}
              className={`transition-all duration-300 ${
                index === currentImageIndex
                  ? 'w-6 h-1.5 bg-white/60'
                  : 'w-1.5 h-1.5 bg-white/30 hover:bg-white/40'
              } rounded-full`}
              aria-label={`Go to image ${index + 1}`}
            />
          ))}
          {imageMetadata.length > 10 && (
            <span className="text-white/30 text-[10px] ml-1">+{imageMetadata.length - 10}</span>
          )}
        </div>

        {/* Next Button */}
        <button
          onClick={goToNext}
          className="p-1.5 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-200"
          aria-label="Next image"
        >
          <ChevronRight className="w-3 h-3 text-white/60" />
        </button>

        {/* Pause/Play Button */}
        <button
          onClick={togglePause}
          className="p-1.5 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-200 ml-2"
          aria-label={isPaused ? 'Play carousel' : 'Pause carousel'}
        >
          {isPaused ? (
            <Play className="w-3 h-3 text-white/60" />
          ) : (
            <Pause className="w-3 h-3 text-white/60" />
          )}
        </button>
      </div>

      {/* Location Display - Bottom right corner */}
      {currentImage.location !== 'Unknown' && (
        <div className="absolute bottom-8 right-8 z-30">
          <div className="flex items-center gap-2 text-white/40">
            <span className="text-sm font-extralight">|</span>
            <span className="text-sm font-light tracking-wide">{currentImage.location}</span>
          </div>
        </div>
      )}
    </div>
  );
};
