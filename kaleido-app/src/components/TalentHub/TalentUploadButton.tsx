'use client';

import { GenericStatusManager } from '@/components/shared/GenericStatusManager/GenericStatusManager';
import { StatusJob, StatusManagerConfig } from '@/components/shared/GenericStatusManager/types';
import { showToast } from '@/components/Toaster';
import { Button } from '@/components/ui/button';
import apiHelper from '@/lib/apiHelper';
import { useTalentHubStore } from '@/stores/talentHubStore';
import { AnimatePresence, motion } from 'framer-motion';
import { AlertCircle, CheckCircle, Download, FileText, Loader2, Upload, X } from 'lucide-react';
import React, { useCallback, useRef, useState } from 'react';

interface UploadResult {
  success: boolean;
  successCount: number;
  errorCount: number;
  errors: Array<{
    filename: string;
    error: string;
  }>;
  processedCandidates: Array<{
    id: string;
    fullName: string;
    email: string;
  }>;
  duplicatesSkipped: number;
  message: string;
  jobId?: string;
  batchId?: string;
}

interface TalentUploadButtonProps {
  jobId?: string;
  onUploadComplete?: () => void;
}

export const TalentUploadButton: React.FC<TalentUploadButtonProps> = ({
  jobId,
  onUploadComplete,
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null);
  const [showResults, setShowResults] = useState(false);
  const [uploadJobs, setUploadJobs] = useState<{ [key: string]: StatusJob }>({});
  const [activeJobIds, setActiveJobIds] = useState<string[]>([]);
  const [showStatusManager, setShowStatusManager] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { fetchCandidates, pagination } = useTalentHubStore();

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    // Check maximum files limit
    if (files.length > 50) {
      showToast({
        message: 'Maximum 50 files allowed',
        type: 'error',
      });
      return;
    }

    // Validate file types
    const validExtensions = ['pdf', 'doc', 'docx', 'csv', 'xlsx', 'xls'];
    const invalidFiles = files.filter(file => {
      const ext = file.name.toLowerCase().split('.').pop();
      return !validExtensions.includes(ext || '');
    });

    if (invalidFiles.length > 0) {
      showToast({
        message: `Invalid file types: ${invalidFiles.map(f => f.name).join(', ')}`,
        type: 'error',
      });
      return;
    }

    // Check file sizes (max 20MB each)
    const oversizedFiles = files.filter(file => file.size > 20 * 1024 * 1024);
    if (oversizedFiles.length > 0) {
      showToast({
        message: `Files exceed 20MB limit: ${oversizedFiles.map(f => f.name).join(', ')}`,
        type: 'error',
      });
      return;
    }

    setSelectedFiles(files);
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return;

    setIsUploading(true);
    setUploadProgress(0);
    setUploadResult(null);

    try {
      const formData = new FormData();
      selectedFiles.forEach(file => {
        formData.append('files', file);
      });

      if (jobId) {
        formData.append('jobId', jobId);
      }

      const response = await apiHelper.post('/talent/bulk-upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const result = response.data;

      // All uploads are now queued
      const queueJobId = result.queueJobId || result.jobId;
      const jobIds = [queueJobId];

      // Create initial job entries
      const newJobs: { [key: string]: StatusJob } = {};
      newJobs[queueJobId] = {
        id: queueJobId,
        status: 'active',
        progress: 0,
        message: result.message || 'Processing files...',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        metadata: {
          filesQueued: result.filesQueued || 0,
        },
      };

      setUploadJobs(newJobs);
      setActiveJobIds(jobIds);
      setShowStatusManager(true);

      // Show initial message
      showToast({
        message:
          result.message ||
          `${result.filesQueued} file${result.filesQueued > 1 ? 's' : ''} queued for processing`,
        type: 'info',
      });
    } catch (error: any) {
      showToast({ message: error.message || 'Upload failed. Please try again.', type: 'error' });
      console.error('Upload error:', error);
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
      setSelectedFiles([]);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      const response = await apiHelper.get('/talent/csv-template', {
        responseType: 'blob',
      });

      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'candidate_upload_template.csv';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showToast({ message: 'Template downloaded successfully', type: 'success' });
    } catch (error) {
      showToast({ message: 'Failed to download template', type: 'error' });
      console.error('Download error:', error);
    }
  };

  const handleExportCandidates = async () => {
    try {
      const params: any = {};
      if (jobId) params.jobId = jobId;

      const response = await apiHelper.get('/talent/export-csv', {
        params,
        responseType: 'blob',
      });

      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `candidates_${jobId || 'all'}_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showToast({ message: 'Candidates exported successfully', type: 'success' });
    } catch (error) {
      showToast({ message: 'Failed to export candidates', type: 'error' });
      console.error('Export error:', error);
    }
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  // Status Manager callbacks
  const handleUpdateJob = useCallback((jobId: string, update: Partial<StatusJob>) => {
    setUploadJobs(prev => ({
      ...prev,
      [jobId]: { ...prev[jobId], ...update },
    }));
  }, []);

  const handleRemoveJob = useCallback((jobId: string) => {
    setUploadJobs(prev => {
      const newJobs = { ...prev };
      delete newJobs[jobId];
      return newJobs;
    });
    setActiveJobIds(prev => prev.filter(id => id !== jobId));
  }, []);

  const handleClearCompleted = useCallback(() => {
    setUploadJobs({});
    setActiveJobIds([]);
    setShowStatusManager(false);
  }, []);

  const handleUploadComplete = useCallback(
    async (jobId: string, result: any) => {
      if (result) {
        setUploadResult(result);

        if (result.successCount > 0) {
          showToast({
            message: `Successfully processed ${result.successCount} candidates`,
            type: 'success',
          });
          await fetchCandidates(pagination.currentPage, pagination.itemsPerPage);
          onUploadComplete?.();
        }

        if (result.duplicatesSkipped > 0) {
          showToast({
            message: `Skipped ${result.duplicatesSkipped} duplicate candidates`,
            type: 'info',
          });
        }

        if (result.errorCount > 0) {
          showToast({ message: `Failed to process ${result.errorCount} files`, type: 'warning' });
        }
      }
    },
    [fetchCandidates, pagination, onUploadComplete]
  );

  // Status Manager configuration
  const statusManagerConfig: StatusManagerConfig = {
    title: 'Talent Upload',
    icon: <Upload className="w-5 h-5 text-blue-500" />,
    statusEndpoint: (jobId: string) => `/talent/upload-status/${jobId}`,
    pollInterval: 2000,
    maxRetries: 3,
    onJobComplete: handleUploadComplete,
    onJobFailed: (jobId: string, error: any) => {
      showToast({ message: `Upload failed: ${error || 'Unknown error'}`, type: 'error' });
    },
    managerId: 'talent-upload',
    autoCloseOnCompletion: false,
    showCloseButton: true,
    useStatusCompletionModal: true,
    action: 'upload',
    actionDisplay: 'Upload',
  };

  return (
    <>
      <div className="flex gap-2">
        {/* Upload Button */}
        <Button
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
        >
          <Upload className="w-4 h-4 mr-2" />
          {isUploading ? `Uploading... ${uploadProgress}%` : 'Upload Talent'}
        </Button>

        {/* Download Template Button */}
        <Button
          onClick={handleDownloadTemplate}
          variant="outline"
          className="border-gray-600 hover:bg-gray-800"
        >
          <FileText className="w-4 h-4 mr-2" />
          CSV Template
        </Button>

        {/* Export Button */}
        <Button
          onClick={handleExportCandidates}
          variant="outline"
          className="border-gray-600 hover:bg-gray-800"
        >
          <Download className="w-4 h-4 mr-2" />
          Export CSV
        </Button>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.csv,.xlsx,.xls"
          className="hidden"
          onChange={handleFileSelect}
        />
      </div>

      {/* Selected Files Preview */}
      <AnimatePresence>
        {selectedFiles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mt-4 p-4 bg-gray-800 rounded-lg border border-gray-700"
          >
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-sm font-semibold text-gray-200">
                Selected Files ({selectedFiles.length})
              </h3>
              <Button
                size="sm"
                onClick={handleUpload}
                disabled={isUploading}
                className="bg-green-600 hover:bg-green-700"
              >
                {isUploading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Upload className="w-4 h-4 mr-2" />
                    Upload All
                  </>
                )}
              </Button>
            </div>

            <div className="space-y-2 max-h-40 overflow-y-auto">
              {selectedFiles.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 bg-gray-900 rounded"
                >
                  <div className="flex items-center gap-2">
                    <FileText className="w-4 h-4 text-blue-400" />
                    <span className="text-sm text-gray-300">{file.name}</span>
                    <span className="text-xs text-gray-500">
                      ({(file.size / 1024).toFixed(1)} KB)
                    </span>
                  </div>
                  <button
                    onClick={() => removeFile(index)}
                    className="text-gray-400 hover:text-red-400 transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>

            {isUploading && (
              <div className="mt-3">
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <motion.div
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${uploadProgress}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Generic Status Manager for Queue Processing */}
      {showStatusManager && (
        <GenericStatusManager
          jobs={uploadJobs}
          activeJobs={activeJobIds}
          config={statusManagerConfig}
          onUpdateJob={handleUpdateJob}
          onRemoveJob={handleRemoveJob}
          onClearCompleted={handleClearCompleted}
          onComplete={(jobId: string) => {
            const job = uploadJobs[jobId];
            if (job && job.result) {
              handleUploadComplete(jobId, job.result);
            }
          }}
        />
      )}

      {/* Upload Results Modal (for non-queued uploads) */}
      <AnimatePresence>
        {showResults && uploadResult && !showStatusManager && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
            onClick={() => setShowResults(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-900 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto"
              onClick={e => e.stopPropagation()}
            >
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-white">Upload Results</h2>
                <button
                  onClick={() => setShowResults(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* Summary Stats */}
              <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="bg-green-900/30 p-3 rounded-lg border border-green-700">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-green-400" />
                    <div>
                      <p className="text-sm text-gray-400">Successful</p>
                      <p className="text-2xl font-bold text-green-400">
                        {uploadResult.successCount}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-yellow-900/30 p-3 rounded-lg border border-yellow-700">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="w-5 h-5 text-yellow-400" />
                    <div>
                      <p className="text-sm text-gray-400">Duplicates</p>
                      <p className="text-2xl font-bold text-yellow-400">
                        {uploadResult.duplicatesSkipped}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-red-900/30 p-3 rounded-lg border border-red-700">
                  <div className="flex items-center gap-2">
                    <X className="w-5 h-5 text-red-400" />
                    <div>
                      <p className="text-sm text-gray-400">Failed</p>
                      <p className="text-2xl font-bold text-red-400">{uploadResult.errorCount}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Error Details */}
              {uploadResult.errors?.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-sm font-semibold text-gray-300 mb-2">Errors</h3>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {uploadResult.errors.map((error, index) => (
                      <div
                        key={index}
                        className="p-2 bg-red-900/20 border border-red-800 rounded text-sm"
                      >
                        <span className="text-red-400 font-medium">{error.filename}:</span>
                        <span className="text-gray-300 ml-2">{error.error}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Processed Candidates */}
              {uploadResult.processedCandidates?.length > 0 && (
                <div>
                  <h3 className="text-sm font-semibold text-gray-300 mb-2">Processed Candidates</h3>
                  <div className="space-y-1 max-h-40 overflow-y-auto">
                    {uploadResult.processedCandidates.map((candidate, index) => (
                      <div
                        key={index}
                        className="p-2 bg-gray-800 rounded text-sm flex justify-between"
                      >
                        <span className="text-gray-300">{candidate.fullName}</span>
                        <span className="text-gray-500">{candidate.email}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <Button onClick={() => setShowResults(false)} className="w-full mt-6">
                Close
              </Button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
