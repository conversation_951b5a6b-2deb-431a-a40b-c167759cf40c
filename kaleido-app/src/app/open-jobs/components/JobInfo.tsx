'use client';

import { useEffect, useRef, useState } from 'react';

import { formatDistanceToNow } from 'date-fns';
import { AnimatePresence, motion } from 'framer-motion';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

import { showToast } from '@/components/Toaster';
import { ApplyButton } from '@/components/ui/ApplyButton';
import { useJobSearch } from '@/contexts/jobSearch/JobSearchContext';
import apiHelper from '@/lib/apiHelper';
import { UserRole } from '@/types/roles';
import { useUser } from '@/hooks/useUser';
import {
  BriefcaseIcon,
  BuildingOfficeIcon,
  ChartBarIcon,
  CheckCircleIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  LightBulbIcon,
  MapPinIcon,
  PlayCircleIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline';

// Import the dedicated styles for open jobs
import styles from '../styles/openJobs.module.css';
import { MatchProgressBar } from './MatchProgressBar';
import MatchRecommendations from './MatchRecommendations';
import { BenefitsTab, CompanyTab, DescriptionTab, SkillsTab, TLDRTab, VideoTab } from './tabs';

// Import a utility to check if a component is within context without throwing

// Key for storing job application context in localStorage
const PENDING_JOB_APPLICATION_KEY = 'headstart_pending_job_application';
const REDIRECT_URL_KEY = 'headstart_redirect_after_auth';

interface JobInfoProps {
  job: any;
  onJobApply?: (jobId: string) => Promise<void>;
  currentUserRole?: string | null;
}

export const JobInfo = ({ job, onJobApply, currentUserRole }: JobInfoProps) => {
  // Set default active tab to 'video' if a video URL exists (either directly or in videoJD), otherwise 'description'
  const [activeTab, setActiveTab] = useState<
    'video' | 'description' | 'tldr' | 'skills' | 'benefits' | 'company'
  >(
    job.videoUrl ||
      (job.videoJD && job.videoJD.videoUrl) ||
      (job.videoJDs && job.videoJDs[0]?.videoUrl)
      ? 'video'
      : 'description'
  );
  // State to track if JobSearchContext is available
  const [hasJobSearchContext, setHasJobSearchContext] = useState(false);
  // State for application status
  const [isApplying, setIsApplying] = useState(false);
  const [hasApplied, setHasApplied] = useState(false);
  // State for match details expansion
  const [showMatchDetails, setShowMatchDetails] = useState(false);
  // Debounce control
  const [lastApplyTime, setLastApplyTime] = useState(0);
  // Add application response data state
  const [applicationData, setApplicationData] = useState<any>(null);
  // Add state to manage modal visibility
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  // Ref to track ongoing API calls
  const applicationInProgressRef = useRef(false);
  // Ref for the job info container
  const jobInfoRef = useRef<HTMLDivElement>(null);
  // User state
  const { user } = useUser();
  const router = useRouter();

  // Conditionally use the job search context
  let handleApply, isPublicView;
  try {
    const context = useJobSearch();
    handleApply = context.handleApply;
    isPublicView = context.isPublicView;
    // If we get here, context is available
    if (!hasJobSearchContext) setHasJobSearchContext(true);
  } catch (error) {
    // Context not available, will use fallback UI
    if (hasJobSearchContext) setHasJobSearchContext(false);
  }

  // Reset active tab when job changes
  useEffect(() => {
    // Check if job has a video URL (either directly or in videoJD)
    const hasVideo =
      job.videoUrl ||
      (job.videoJD && job.videoJD.videoUrl) ||
      (job.videoJDs && job.videoJDs[0]?.videoUrl);
    setActiveTab(hasVideo ? 'video' : 'description');

    // Check if mobile/tablet view based on viewport width
    const isMobileOrTablet = window.innerWidth < 1024;

    // If in mobile/tablet view, scroll the component into view with smooth animation
    if (isMobileOrTablet && jobInfoRef.current) {
      // Use a slight delay to ensure DOM is updated
      setTimeout(() => {
        jobInfoRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }, 100);
    }
  }, [job.id, job.videoUrl, job.videoJD, job.videoJDs]);

  // Check if user has already applied for this job
  useEffect(() => {
    if (user?.sub && job.id) {
      const checkExistingApplication = async () => {
        try {
          // Only make this API call if we haven't confirmed application yet
          if (!hasApplied) {
            const response = await apiHelper.get('/job-seekers/applications');
            const applications = response?.data || [];
            const hasAlreadyApplied = applications.some(app => app.jobId === job.id);

            if (hasAlreadyApplied) {
              setHasApplied(true);
            }
          }
        } catch (error) {
          console.error('Error checking applications:', error);
        }
      };

      checkExistingApplication();
    }
  }, [user, job.id, hasApplied]);

  // Store application intent and current URL for redirect
  const storeApplicationIntent = () => {
    try {
      // Store the job ID to apply to
      localStorage.setItem(PENDING_JOB_APPLICATION_KEY, job.id);

      // Store the FULL URL including origin to ensure reliable matching
      const fullUrl = window.location.href;
      localStorage.setItem(REDIRECT_URL_KEY, fullUrl);

      // Log for debugging
    } catch (error) {
      console.error('Error storing application intent:', error);
    }
  };

  // Check for pending job applications on mount - significantly simplified
  useEffect(() => {
    // We need to avoid running this multiple times for the same job ID
    // Only run if we have a user and no confirmation of application yet
    if (!user?.sub || hasApplied || applicationInProgressRef.current) {
      return;
    }

    const checkPendingApplication = async () => {
      const pendingJobId = localStorage.getItem(PENDING_JOB_APPLICATION_KEY);
      const redirectUrl = localStorage.getItem(REDIRECT_URL_KEY);
      const currentUrl = window.location.href;

      // Check if we're at the right URL and have a pending application for THIS job
      const isReturnedFromLogin =
        redirectUrl && currentUrl.split('?')[0].indexOf(redirectUrl.split('?')[0]) !== -1;

      // Only proceed if we have the right conditions
      if (pendingJobId === job.id && redirectUrl && isReturnedFromLogin) {
        // Prevent duplicate API calls
        applicationInProgressRef.current = true;

        // Clear stored data first to prevent loops
        localStorage.removeItem(PENDING_JOB_APPLICATION_KEY);
        localStorage.removeItem(REDIRECT_URL_KEY);

        try {
          // Validate profile completion - with debounce
          const now = Date.now();
          if (now - lastApplyTime < 5000) {
            applicationInProgressRef.current = false;
            return;
          }
          setLastApplyTime(now);

          const validationResult = await apiHelper.post('/job-seekers/validate-profile', {
            sub: user.sub,
            userId: user.sub,
            email: user.email,
          });

          // Check if profile is now valid for application
          if (validationResult.isValid) {
            setIsApplying(true);

            try {
              // Apply for the job
              const applicationResponse = await apiHelper.post(`/job-seekers/apply/${job.id}`, {
                coverLetter: '',
              });

              // Store application data for display
              setApplicationData(applicationResponse);
              setHasApplied(true);

              // Show success toast
              showToast({
                message: 'Successfully applied to job!',
                isSuccess: true,
              });

              // Show success modal after small delay
              setTimeout(() => {
                setShowSuccessModal(true);
              }, 500);
            } catch (applyError: any) {
              // Only treat 409 as success
              if (applyError.response?.status === 409) {
                showToast({
                  message: 'You have already applied to this job',
                  isSuccess: true,
                });
                setHasApplied(true);
              } else {
                showToast({
                  message: 'Failed to apply to job after login. Please try again.',
                  isSuccess: false,
                });
              }
            }
          } else {
            // Profile incomplete handling
            const missingFieldsCount = validationResult.mandatoryMissingFields?.length || 0;
            const completionPercentage = validationResult.completion?.overall || 0;

            showToast({
              message: `Profile ${completionPercentage}% complete. ${missingFieldsCount} required fields missing.`,
              isSuccess: false,
            });

            const userRoleKey = `userRole_${user.sub}`;
            if (!localStorage.getItem(userRoleKey)) {
              localStorage.setItem(userRoleKey, JSON.stringify({ role: UserRole.JOB_SEEKER }));
            }

            setTimeout(() => {
              router.push(
                `/dashboard?showSetup=true&validationData=${encodeURIComponent(JSON.stringify(validationResult))}`
              );
            }, 1000);
          }
        } catch (error) {
          console.error('Error auto-applying to job:', error);
          showToast({
            message: 'Please try applying again.',
            isSuccess: false,
          });
        } finally {
          setIsApplying(false);
          applicationInProgressRef.current = false;
        }
      }
    };

    // Run once with debounce protection
    checkPendingApplication();
  }, [user, job.id, router, hasApplied, lastApplyTime]);

  // Handle manual apply with improved debounce
  const handleLocalApply = async () => {
    // Prevent multiple rapid clicks/API calls
    if (applicationInProgressRef.current) {
      return;
    }

    // Check for recent clicks (stronger debounce - 3 seconds)
    const now = Date.now();
    if (now - lastApplyTime < 3000) {
      return;
    }
    setLastApplyTime(now);

    // If already applied, just show feedback
    if (hasApplied) {
      showToast({
        message: 'You have already applied for this position',
        isSuccess: true,
      });

      // Show the success modal with the application data if we have it
      if (applicationData) {
        setShowSuccessModal(true);
      }
      return;
    }

    // Set flag to prevent concurrent calls
    applicationInProgressRef.current = true;
    setIsApplying(true);

    try {
      // Use the provided onJobApply prop if available
      if (onJobApply) {
        try {
          await onJobApply(job.id);
          // Only set as applied if onJobApply succeeds
          setHasApplied(true);
        } catch (error) {
          // onJobApply failed (e.g., role validation error)
          console.error('Application failed:', error);
          // Don't set hasApplied to true
        } finally {
          applicationInProgressRef.current = false;
          setIsApplying(false);
        }
        return;
      }

      // Otherwise, check for context handler
      if (handleApply) {
        await handleApply(job);
        setHasApplied(true);
        applicationInProgressRef.current = false;
        setIsApplying(false);
        return;
      }

      // Auth flow if not logged in
      if (!user) {
        storeApplicationIntent();
        const returnTo = encodeURIComponent(window.location.href);
        router.push(`/api/auth/login?returnTo=${returnTo}`);
        applicationInProgressRef.current = false;
        setIsApplying(false);
        return;
      }

      // Main application logic for logged-in users
      if (user?.sub) {
        try {
          // Validate profile
          const validationResult = await apiHelper.post('/job-seekers/validate-profile', {
            sub: user.sub,
            userId: user.sub,
            email: user.email,
          });

          if (!validationResult.isValid) {
            // Redirect to profile setup
            storeApplicationIntent();
            showToast({
              message: 'Please complete your profile before applying.',
              isSuccess: false,
            });
            router.push(
              `/dashboard?showSetup=true&validationData=${encodeURIComponent(JSON.stringify(validationResult))}`
            );
            return;
          }

          // Profile is valid, apply for job
          try {
            const applicationResponse = await apiHelper.post(`/job-seekers/apply/${job.id}`, {
              coverLetter: '',
            });

            // Store application data and update state
            setApplicationData(applicationResponse);
            setHasApplied(true);

            // Show success toast
            showToast({
              message: 'Successfully applied to job!',
              isSuccess: true,
            });

            // Show success modal
            setTimeout(() => {
              setShowSuccessModal(true);
            }, 500);
          } catch (applyError: any) {
            if (applyError.response?.status === 409) {
              showToast({
                message: 'You have already applied to this job',
                isSuccess: true,
              });
              setHasApplied(true);
            } else {
              throw applyError;
            }
          }
        } catch (error: any) {
          if (error.message?.includes('Profile incomplete')) {
            storeApplicationIntent();
            showToast({
              message: 'Please complete your profile before applying.',
              isSuccess: false,
            });
            const userRoleKey = `userRole_${user.sub}`;
            if (!localStorage.getItem(userRoleKey)) {
              localStorage.setItem(userRoleKey, JSON.stringify({ role: UserRole.JOB_SEEKER }));
            }
            router.push(`/dashboard?role=job-seeker&showSetup=true`);
            return;
          }
          throw error;
        }
      }
    } catch (error) {
      console.error('Error applying to job:', error);
      showToast({
        message: 'Failed to apply. Please try again.',
        isSuccess: false,
      });
    } finally {
      setIsApplying(false);
      applicationInProgressRef.current = false;
    }
  };

  // Function to close success modal
  const closeSuccessModal = () => {
    setShowSuccessModal(false);
  };

  return (
    <div
      ref={jobInfoRef}
      className={`${styles.jobCard} rounded-xl shadow-md p-3 sm:p-4 md:p-6 lg:p-8 lg:sticky lg:top-8 relative`}
    >
      <style jsx global>{`
        .compact-list {
          margin-top: 0.25rem !important;
          margin-bottom: 0.25rem !important;
        }
        .compact-list li {
          margin-top: 0 !important;
          margin-bottom: 0 !important;
          padding-top: 0 !important;
          padding-bottom: 0 !important;
          line-height: 1.2 !important;
        }
        /* Hide scrollbar for Chrome, Safari and Opera */
        .no-scrollbar::-webkit-scrollbar {
          display: none;
        }
        /* Hide scrollbar for IE, Edge and Firefox */
        .no-scrollbar {
          -ms-overflow-style: none; /* IE and Edge */
          scrollbar-width: none; /* Firefox */
        }
      `}</style>
      {/* Modern Header with gradient background */}
      <div className="mb-4 sm:mb-6 lg:mb-8 -m-3 sm:-m-4 md:-m-6 lg:-m-8 p-3 sm:p-4 md:p-6 lg:p-8 bg-gradient-to-r from-indigo-600 to-blue-600 rounded-t-xl">
        <div className="flex flex-col gap-4 sm:gap-5 lg:flex-row lg:justify-between lg:items-start lg:gap-8">
          <div className="flex items-start gap-3 sm:gap-4 lg:gap-6 w-full lg:flex-1 lg:min-w-0">
            {job.company?.logo ? (
              <div className="bg-white p-1.5 sm:p-2 rounded-lg lg:rounded-xl shadow-md flex-shrink-0">
                <Image
                  src={job.company.logo}
                  alt={job.company?.companyName || 'Company logo'}
                  width={60}
                  height={60}
                  className="rounded-md lg:rounded-lg object-contain w-[50px] h-[50px] sm:w-[60px] sm:h-[60px] lg:w-[80px] lg:h-[80px]"
                />
              </div>
            ) : (
              <div className="flex-shrink-0">
                <BuildingOfficeIcon className="h-8 w-8 sm:h-10 sm:w-10 lg:h-14 lg:w-14 text-white" />
              </div>
            )}
            <div className="min-w-0 flex-1 text-white">
              <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-white truncate">
                {job.jobType}
              </h2>
              <p className="text-sm sm:text-base lg:text-lg !text-white/80 truncate">
                {job.company?.companyName}
              </p>
              <div className="text-xs sm:text-sm !text-white/80 mt-0.5 sm:mt-1">
                Posted {job.createdAt ? formatDistanceToNow(new Date(job.createdAt)) : 'recently'}{' '}
                ago
              </div>
              {/* Match Score Indicator with Expandable Details */}
              {job.matchPercentage && (
                <div className="mt-2 sm:mt-3">
                  <div
                    className="flex items-center justify-between mb-1 sm:mb-2 cursor-pointer"
                    onClick={() => setShowMatchDetails(prev => !prev)}
                  >
                    <div className="flex items-center">
                      <ChartBarIcon className="h-3 w-3 sm:h-4 sm:w-4 text-indigo-200 mr-1 sm:mr-1.5" />
                      <span className="text-xs sm:text-sm text-indigo-200">Match Score</span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-xs sm:text-sm font-medium text-white mr-1 sm:mr-1.5">
                        {job.matchPercentage}%
                      </span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className={`h-3 w-3 sm:h-4 sm:w-4 text-indigo-200 transition-transform duration-200 ${showMatchDetails ? 'rotate-180' : ''}`}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </div>
                  </div>

                  <div className="h-1.5 sm:h-2 mt-1 mb-1 sm:mt-2 sm:mb-2">
                    <MatchProgressBar percentage={job.matchPercentage} />
                  </div>

                  {job.matchDetails?.matchFeedback && (
                    <div className="text-xs sm:text-sm text-indigo-200 mt-1 sm:mt-2">
                      {job.matchDetails.matchFeedback}
                    </div>
                  )}

                  <AnimatePresence>
                    {showMatchDetails && job.matchDetails && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                        className="overflow-hidden"
                      >
                        <div className="mt-2 sm:mt-3 border-t border-indigo-700 pt-2 sm:pt-3">
                          {job.matchDetails.recommendations &&
                          job.matchDetails.recommendations.length > 0 ? (
                            <MatchRecommendations
                              recommendations={job.matchDetails.recommendations}
                              textColor="text-indigo-200"
                              iconColor="text-indigo-200"
                              bulletColor="text-pink-500"
                            />
                          ) : (
                            <div className="space-y-1 sm:space-y-1.5">
                              {job.matchDetails.skillsMatch && (
                                <div className="flex justify-between text-xs sm:text-sm">
                                  <span className="text-indigo-300">Skills:</span>
                                  <span className="text-white font-medium">
                                    {job.matchDetails.skillsMatch.score}%
                                  </span>
                                </div>
                              )}
                              {job.matchDetails.experienceMatch && (
                                <div className="flex justify-between text-xs sm:text-sm">
                                  <span className="text-indigo-300">Experience:</span>
                                  <span className="text-white font-medium">
                                    {job.matchDetails.experienceMatch.score}%
                                  </span>
                                </div>
                              )}
                              {job.matchDetails.locationMatch && (
                                <div className="flex justify-between text-xs sm:text-sm">
                                  <span className="text-indigo-300">Location:</span>
                                  <span className="text-white font-medium">
                                    {job.matchDetails.locationMatch.score}%
                                  </span>
                                </div>
                              )}
                              {job.matchDetails.educationMatch && (
                                <div className="flex justify-between text-xs sm:text-sm">
                                  <span className="text-indigo-300">Education:</span>
                                  <span className="text-white font-medium">
                                    {job.matchDetails.educationMatch.score}%
                                  </span>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              )}
            </div>
          </div>
          {/* Apply Button Container - Only show on large screens */}
          <div className="hidden lg:block lg:flex-shrink-0">
            {currentUserRole === UserRole.EMPLOYER || currentUserRole === UserRole.ADMIN ? (
              <div className="text-white text-center px-10 py-4 text-sm bg-white/20 rounded-full min-w-[180px]">
                <p className="font-semibold">Employer View</p>
                <p className="text-xs text-white/80">Cannot apply as employer</p>
              </div>
            ) : (
              <ApplyButton
                jobId={job.id}
                job={job}
                isPublic={true}
                variant="default"
                className="text-white whitespace-nowrap px-10 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 min-w-[180px]"
                alreadyApplied={job.alreadyApplied || hasApplied}
                handleLocalApply={handleLocalApply}
              />
            )}
          </div>
        </div>
      </div>

      {/* Key Job Info Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8 mt-6 sm:mt-8">
        {/* Match score card removed */}
        {job.location && job.location.length > 0 && (
          <div className="flex items-center gap-2 sm:gap-3 bg-gray-50 hover:bg-gray-100 transition-colors p-3 sm:p-4 rounded-xl">
            <div className="bg-indigo-100 p-2 sm:p-2.5 rounded-full flex-shrink-0">
              <MapPinIcon className="h-4 w-4 sm:h-5 sm:w-5 text-indigo-600" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs sm:text-sm text-gray-500 font-medium">Location</div>
              <div className="text-sm sm:text-base text-gray-800 font-semibold truncate">
                {Array.isArray(job.location) ? job.location.join(', ') : job.location}
              </div>
            </div>
          </div>
        )}

        {job.experience && (
          <div className="flex items-center gap-2 sm:gap-3 bg-gray-50 hover:bg-gray-100 transition-colors p-3 sm:p-4 rounded-xl">
            <div className="bg-indigo-100 p-2 sm:p-2.5 rounded-full flex-shrink-0">
              <BriefcaseIcon className="h-4 w-4 sm:h-5 sm:w-5 text-indigo-600" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs sm:text-sm text-gray-500 font-medium">Experience</div>
              <div className="text-sm sm:text-base text-gray-800 font-semibold truncate">
                {job.experience}
              </div>
            </div>
          </div>
        )}

        {job.salaryRange && (
          <div className="flex items-center gap-2 sm:gap-3 bg-gray-50 hover:bg-gray-100 transition-colors p-3 sm:p-4 rounded-xl">
            <div className="bg-indigo-100 p-2 sm:p-2.5 rounded-full flex-shrink-0">
              <CurrencyDollarIcon className="h-4 w-4 sm:h-5 sm:w-5 text-indigo-600" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs sm:text-sm text-gray-500 font-medium">Salary</div>
              <div className="text-sm sm:text-base text-gray-800 font-semibold truncate">
                {job.salaryRange}
              </div>
            </div>
          </div>
        )}

        {job.department && (
          <div className="flex items-center gap-2 sm:gap-3 bg-gray-50 hover:bg-gray-100 transition-colors p-3 sm:p-4 rounded-xl">
            <div className="bg-indigo-100 p-2 sm:p-2.5 rounded-full flex-shrink-0">
              <BuildingOfficeIcon className="h-4 w-4 sm:h-5 sm:w-5 text-indigo-600" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs sm:text-sm text-gray-500 font-medium">Department</div>
              <div className="text-sm sm:text-base text-gray-800 font-semibold truncate">
                {job.department}
              </div>
            </div>
          </div>
        )}

        {job.typeOfJob && (
          <div className="flex items-center gap-2 sm:gap-3 bg-gray-50 hover:bg-gray-100 transition-colors p-3 sm:p-4 rounded-xl">
            <div className="bg-indigo-100 p-2 sm:p-2.5 rounded-full flex-shrink-0">
              <BriefcaseIcon className="h-4 w-4 sm:h-5 sm:w-5 text-indigo-600" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs sm:text-sm text-gray-500 font-medium">Job Type</div>
              <div className="text-sm sm:text-base text-gray-800 font-semibold truncate">
                {job.typeOfJob}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Tabs Navigation */}
      <div className="border-b border-gray-200 mb-4 sm:mb-6">
        <nav className="flex overflow-x-auto pb-1 -mb-px space-x-2 sm:space-x-4 lg:space-x-6 no-scrollbar">
          {/* Show video tab if job has a video URL (either directly or in videoJD) */}
          {(job.videoUrl ||
            (job.videoJD && job.videoJD.videoUrl) ||
            (job.videoJDs && job.videoJDs[0]?.videoUrl)) && (
            <button
              type="button"
              onClick={() => setActiveTab('video')}
              className={`py-2 sm:py-3 lg:py-4 px-1 sm:px-2 border-b-2 text-xs sm:text-sm lg:text-base whitespace-nowrap flex items-center min-w-0 ${
                activeTab === 'video'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <PlayCircleIcon
                className={`h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-1 sm:mr-1.5 ${activeTab === 'video' ? 'text-indigo-600' : 'text-gray-400'}`}
              />
              <span>Video</span>
              <span className="ml-1 sm:ml-1.5 flex h-1.5 w-1.5">
                <span className="animate-ping absolute inline-flex h-1.5 w-1.5 rounded-full bg-indigo-400 opacity-75"></span>
                <span className="relative inline-flex rounded-full h-1.5 w-1.5 bg-indigo-500"></span>
              </span>
            </button>
          )}

          <button
            type="button"
            onClick={() => setActiveTab('description')}
            className={`py-2 sm:py-3 lg:py-4 px-1 sm:px-2 border-b-2 text-xs sm:text-sm lg:text-base whitespace-nowrap flex items-center min-w-0 ${
              activeTab === 'description'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <DocumentTextIcon
              className={`h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-1 sm:mr-1.5 ${activeTab === 'description' ? 'text-indigo-600' : 'text-gray-400'}`}
            />
            <span>Description</span>
          </button>

          <button
            type="button"
            onClick={() => setActiveTab('tldr')}
            className={`py-2 sm:py-3 lg:py-4 px-1 sm:px-2 border-b-2 text-xs sm:text-sm lg:text-base whitespace-nowrap flex items-center min-w-0 ${
              activeTab === 'tldr'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <LightBulbIcon
              className={`h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-1 sm:mr-1.5 ${activeTab === 'tldr' ? 'text-indigo-600' : 'text-gray-400'}`}
            />
            <span>TLDR</span>
          </button>

          <button
            type="button"
            onClick={() => setActiveTab('skills')}
            className={`py-2 sm:py-3 lg:py-4 px-1 sm:px-2 border-b-2 text-xs sm:text-sm lg:text-base whitespace-nowrap flex items-center min-w-0 ${
              activeTab === 'skills'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <CheckCircleIcon
              className={`h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-1 sm:mr-1.5 ${activeTab === 'skills' ? 'text-indigo-600' : 'text-gray-400'}`}
            />
            <span>Skills</span>
          </button>

          <button
            type="button"
            onClick={() => setActiveTab('benefits')}
            className={`py-2 sm:py-3 lg:py-4 px-1 sm:px-2 border-b-2 text-xs sm:text-sm lg:text-base whitespace-nowrap flex items-center min-w-0 ${
              activeTab === 'benefits'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <SparklesIcon
              className={`h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-1 sm:mr-1.5 ${activeTab === 'benefits' ? 'text-indigo-600' : 'text-gray-400'}`}
            />
            <span>Benefits</span>
          </button>

          <button
            type="button"
            onClick={() => setActiveTab('company')}
            className={`py-2 sm:py-3 lg:py-4 px-1 sm:px-2 border-b-2 text-xs sm:text-sm lg:text-base whitespace-nowrap flex items-center min-w-0 ${
              activeTab === 'company'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <BuildingOfficeIcon
              className={`h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-1 sm:mr-1.5 ${activeTab === 'company' ? 'text-indigo-600' : 'text-gray-400'}`}
            />
            <span>Company</span>
          </button>
        </nav>
      </div>

      {/* Tab Content with responsive text sizing */}
      <div className="prose max-w-none text-xs sm:text-sm lg:text-base">
        <AnimatePresence mode="wait">
          {/* Video Tab */}
          {activeTab === 'video' &&
            (job.videoUrl ||
              (job.videoJD && job.videoJD.videoUrl) ||
              (job.videoJDs && job.videoJDs[0]?.videoUrl)) && <VideoTab job={job} />}

          {/* Description Tab */}
          {activeTab === 'description' && <DescriptionTab job={job} />}

          {/* TLDR Tab */}
          {activeTab === 'tldr' && <TLDRTab job={job} />}

          {/* Skills Tab */}
          {activeTab === 'skills' && <SkillsTab job={job} />}

          {/* Benefits Tab */}
          {activeTab === 'benefits' && <BenefitsTab job={job} />}

          {/* Company Tab */}
          {activeTab === 'company' && <CompanyTab job={job} />}
        </AnimatePresence>
      </div>

      {/* Job Application Success Modal */}
      {showSuccessModal && applicationData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-3 sm:p-4 lg:p-6">
          <div className="bg-white rounded-lg sm:rounded-xl lg:rounded-2xl shadow-xl max-w-sm sm:max-w-md lg:max-w-lg w-full overflow-hidden mx-2 sm:mx-4 lg:mx-0">
            <div className="p-4 sm:p-6 lg:p-8 bg-gradient-to-r from-green-500 to-emerald-600">
              <div className="flex justify-between items-center">
                <h3 className="text-lg sm:text-xl lg:text-2xl font-semibold text-white">
                  Application Submitted!
                </h3>
                <button
                  type="button"
                  onClick={closeSuccessModal}
                  className="text-white hover:text-gray-200 transition-colors p-1"
                  title="Close dialog"
                  aria-label="Close application success dialog"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 sm:h-6 sm:w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <div className="px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
              <div className="flex items-center justify-center mb-4 sm:mb-6">
                <div className="bg-green-100 rounded-full p-2 sm:p-3 lg:p-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-8 w-8 sm:h-10 sm:w-10 lg:h-12 lg:w-12 text-green-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>

              <p className="text-center text-gray-700 mb-4 sm:mb-6 text-sm sm:text-base lg:text-lg leading-relaxed">
                Your application for{' '}
                <span className="font-semibold text-indigo-600">{job.jobType}</span> at{' '}
                <span className="font-semibold text-indigo-600">{job.company?.companyName}</span>{' '}
                has been successfully submitted!
              </p>

              <div className="bg-gray-50 p-3 sm:p-4 lg:p-6 rounded-lg sm:rounded-xl mb-4 sm:mb-6 space-y-3 sm:space-y-4">
                <div>
                  <div className="text-xs sm:text-sm text-gray-500 mb-1 font-medium">
                    Application ID
                  </div>
                  <div className="text-xs sm:text-sm font-mono bg-gray-100 p-2 rounded border break-all">
                    {applicationData.id}
                  </div>
                </div>

                <div>
                  <div className="text-xs sm:text-sm text-gray-500 mb-1 font-medium">Status</div>
                  <div className="flex items-center">
                    <span className="bg-green-100 text-green-800 text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-1.5 rounded-full font-medium">
                      {applicationData.status}
                    </span>
                  </div>
                </div>

                <div>
                  <div className="text-xs sm:text-sm text-gray-500 mb-1 font-medium">
                    Date Submitted
                  </div>
                  <div className="text-xs sm:text-sm text-gray-800">
                    {new Date(applicationData.createdAt).toLocaleString()}
                  </div>
                </div>
              </div>
            </div>

            <div className="px-4 sm:px-6 lg:px-8 py-3 sm:py-4 lg:py-6 bg-gray-50 flex justify-center">
              <button
                type="button"
                onClick={closeSuccessModal}
                className="px-4 sm:px-6 lg:px-8 py-2 sm:py-2.5 lg:py-3 bg-indigo-600 text-white text-sm sm:text-base rounded-md sm:rounded-lg hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors font-medium"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Mobile Apply Button - Only show on small/medium screens */}
      <div className="lg:hidden sticky bottom-0 bg-white border-t border-gray-200 p-3 sm:p-4 -m-3 sm:-m-4 md:-m-6 mt-6 sm:mt-8 rounded-b-xl">
        {currentUserRole === UserRole.EMPLOYER || currentUserRole === UserRole.ADMIN ? (
          <div className="w-full text-center py-3 sm:py-4 text-base sm:text-lg font-semibold bg-gray-100 text-gray-600 rounded-lg border">
            <p>Employer View - Cannot Apply</p>
          </div>
        ) : (
          <ApplyButton
            jobId={job.id}
            job={job}
            isPublic={true}
            variant="default"
            className="w-full py-3 sm:py-4 text-base sm:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
            alreadyApplied={job.alreadyApplied || hasApplied}
            handleLocalApply={handleLocalApply}
          />
        )}
      </div>
    </div>
  );
};
