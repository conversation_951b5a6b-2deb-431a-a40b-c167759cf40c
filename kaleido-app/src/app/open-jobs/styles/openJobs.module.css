/* Open Jobs dedicated styling - uses independent CSS variable system */

/* Base styles */
.openJobsContainer {
  background-color: var(--oj-bg-secondary);
  color: var(--oj-text-primary);
  min-height: 100vh;
}

.openJobsContent {
  background-color: var(--oj-bg-primary);
  min-height: 100vh;
  color: var(--oj-text-primary);
}

/* Card styles */
.jobCard {
  background-color: var(--oj-card-bg);
  border: 1px solid var(--oj-card-border);
  border-radius: var(--oj-card-radius);
  box-shadow: var(--oj-card-shadow);
  color: var(--oj-text-primary);
}

.jobCardSelected {
  border-color: var(--oj-secondary-500);
  box-shadow: 0 0 0 1px var(--oj-secondary-500);
  background-color: var(--oj-card-bg);
}

/* Override Tailwind border classes for selected job cards */
.openJobsContainer .border-2.border-indigo-500 {
  border: 2px solid var(--oj-secondary-500);
}

.openJobsContainer .shadow-lg {
  box-shadow: var(--oj-card-shadow-lg);
}

.openJobsContainer [class*='ring-1'][class*='ring-indigo-500'] {
  box-shadow: 0 0 0 1px rgba(99, 102, 241, 0.2);
}

/* Combined shadow and ring effect for selected cards */
.openJobsContainer .shadow-lg[class*='ring-1'][class*='ring-indigo-500'] {
  box-shadow:
    0 0 0 1px rgba(99, 102, 241, 0.2),
    var(--oj-card-shadow-lg);
}

/* Text styles */
.primaryText {
  color: var(--oj-text-primary);
}

.secondaryText {
  color: var(--oj-text-secondary);
}

.accentText {
  color: var(--oj-secondary-500);
}

/* Button styles */
.primaryButton {
  background: linear-gradient(to right, var(--oj-secondary-600), var(--oj-secondary-700));
  color: var(--oj-text-white);
  border: none;
}

.secondaryButton {
  background-color: var(--oj-gray-100);
  color: var(--oj-text-primary);
  border: 1px solid var(--oj-border-medium);
}

/* Input styles */
.input {
  background-color: var(--oj-bg-primary);
  border: 1px solid var(--oj-border-medium);
  color: var(--oj-text-primary);
}

.inputPlaceholder {
  color: var(--oj-text-light);
}

/* Hero section */
.heroSection {
  background: linear-gradient(
    to bottom right,
    var(--oj-secondary-50),
    var(--oj-accent-50),
    var(--oj-primary-50)
  );
  /* color: var(--oj-text-primary) ; */
}

/* Skeleton loading */
.skeletonBg {
  background-color: var(--oj-gray-200);
}

/* Progress bar */
.progressContainer {
  background-color: rgba(229, 231, 235, 0.5);
}

.progressBarGreen {
  background-color: var(--oj-accent-700);
}

.progressBarBlue {
  background-color: var(--oj-accent-500);
}

.progressBarYellow {
  background-color: var(--oj-accent-600);
}

.progressBarGray {
  background-color: var(--oj-text-secondary);
}

/* Component-specific styles that complement the global CSS system */

/* Additional overrides for legacy compatibility */
.openJobsContainer *,
.openJobsContent * {
  color: inherit;
}

/* Job description content styling */
.openJobsContainer .job-description-content {
  color: var(--oj-text-primary);
}

.openJobsContainer .job-description-content h1,
.openJobsContainer .job-description-content h2,
.openJobsContainer .job-description-content h3,
.openJobsContainer .job-description-content h4,
.openJobsContainer .job-description-content h5,
.openJobsContainer .job-description-content h6 {
  color: var(--oj-text-primary);
}

.openJobsContainer .job-description-content p,
.openJobsContainer .job-description-content div,
.openJobsContainer .job-description-content li,
.openJobsContainer .job-description-content span {
  color: var(--oj-text-secondary);
}

.openJobsContainer .job-description-content strong {
  color: var(--oj-text-primary);
}

/* Custom checkbox for Open Jobs filter */
.ojCheckbox {
  -webkit-appearance: none;
  appearance: none;
  position: relative;
  width: 1.5rem; /* h-6 */
  height: 1.5rem; /* w-6 */
  background-color: #ffffff; /* white empty state */
  border: 1px solid #a855f7; /* purple-600 */
  border-radius: 0.25rem; /* rounded */
  outline: none;
  cursor: pointer;
}

.ojCheckbox:focus {
  box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.4); /* focus ring purple */
}

.ojCheckbox:checked {
  background-color: #a855f7; /* purple when selected */
  border-color: #a855f7;
}

/* Draw a checkmark when checked */
.ojCheckbox:checked::after {
  content: '';
  position: absolute;
  left: 0.42rem;
  top: 0.18rem;
  width: 0.35rem;
  height: 0.7rem;
  border: solid #ffffff;
  border-width: 0 0.15rem 0.15rem 0;
  transform: rotate(45deg);
}
