import { NextRequest, NextResponse } from 'next/server';
import { getAccessToken, getSession } from '@auth0/nextjs-auth0/edge';

/**
 * API route to refresh access token
 * Uses Auth0's refresh token from HTTP-only cookie
 */
export async function POST(req: NextRequest) {
  try {
    // Get fresh access token using Auth0's built-in refresh mechanism
    const tokenResult = await getAccessToken(req, new NextResponse());

    let accessToken;
    if (typeof tokenResult === 'string') {
      accessToken = tokenResult;
    } else if (tokenResult && typeof tokenResult === 'object' && 'accessToken' in tokenResult) {
      accessToken = tokenResult.accessToken;
    }

    if (!accessToken) {
      return NextResponse.json({ error: 'Failed to refresh token' }, { status: 401 });
    }

    // Get updated session with new token
    const session = await getSession(req, new NextResponse());

    // Return new access token
    // Note: The refresh token stays in HTTP-only cookie
    return NextResponse.json({
      accessToken,
      expiresAt: session?.accessTokenExpiresAt || Date.now() + 3600 * 1000,
    });
  } catch (error: any) {
    console.error('Token refresh error:', error);
    console.error('Token refresh error details:', {
      message: error?.message,
      code: error?.code,
      name: error?.name,
    });
    return NextResponse.json(
      {
        error: 'Token refresh failed',
        details: error?.message || 'Unknown error',
      },
      { status: 401 }
    );
  }
}
