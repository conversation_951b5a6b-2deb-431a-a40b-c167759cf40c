'use client';

import { formatDistanceToNow } from 'date-fns';
import { useRouter } from 'next/navigation';

import { ApplyButton } from '@/components/ui/ApplyButton';
import { JobWithApplicationStatus } from '@/components/ui/ui.types';
import { useJobSearchStore } from '@/stores/jobSearchStore';
import { formatNumberWithK } from '@/utils/formatters';
import {
  Briefcase,
  ChartBar,
  CheckCircle,
  Clock,
  Code,
  CurrencyDollar,
  Database,
  DeviceMobile,
  FileText,
  Laptop,
  MapPin,
  PencilSimple,
  PresentationChart,
  ShoppingBag,
  VideoCamera,
} from '@phosphor-icons/react';

import { CompanyData, JobData, LayoutStyles } from '../types';

// Job-related icons from phosphor-react with colors
const jobIcons = [
  { icon: Briefcase, color: '#E95793' },
  { icon: Code, color: '#3B82F6' },
  { icon: ChartBar, color: '#10B981' },
  { icon: DeviceMobile, color: '#F59E0B' },
  { icon: PresentationChart, color: '#8B5CF6' },
  { icon: ShoppingBag, color: '#EC4899' },
  { icon: Laptop, color: '#F97316' },
  { icon: Database, color: '#6366F1' },
  { icon: PencilSimple, color: '#14B8A6' },
  { icon: FileText, color: '#EF4444' },
];

const chipColors = {
  CONTRACT: { bg: 'bg-blue-50', text: 'text-blue-700' },
  FULL_TIME: { bg: 'bg-green-50', text: 'text-green-700' },
  PART_TIME: { bg: 'bg-purple-50', text: 'text-purple-700' },
  entry: { bg: 'bg-yellow-50', text: 'text-yellow-700' },
  mid: { bg: 'bg-indigo-50', text: 'text-indigo-700' },
  senior: { bg: 'bg-pink-50', text: 'text-purple-700' },
};

const formatSalary = (salary: string) => {
  // Extract numbers from the string, handling commas
  const numbers = salary.match(/[\d,]+/g);
  if (!numbers || numbers.length === 0) return salary;

  // Get currency code if it exists
  const currencyCode = salary.match(/[A-Z]{3}/)?.[0] || '';

  // Convert numbers, removing commas and format with k notation
  const convertedNumbers = numbers.map(num => formatNumberWithK(parseInt(num.replace(/,/g, ''))));

  // If we have a range (two numbers)
  if (convertedNumbers.length >= 2) {
    return `${currencyCode} ${convertedNumbers[0]}-${convertedNumbers[1]}`;
  }

  // If we only have one number
  return `${currencyCode} ${convertedNumbers[0]}`;
};

const getExperienceLevel = (experience: string) => {
  if (!experience) return '';
  const match = experience.match(/^([^(]+)/);
  return match ? match[0].trim() : experience;
};

const toTitleCase = (str: string) => {
  return str
    .toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

interface JobCardProps {
  job: JobData | JobWithApplicationStatus;
  company: CompanyData;
  styles: LayoutStyles;
  isSelected: boolean;
  onSelect: (job: JobData | JobWithApplicationStatus) => void;
  slug: string;
  onJobApply?: (jobId: string) => Promise<void>;
}

// Extended job interface to include hasApplied property and match data
interface ExtendedJobData extends JobData {
  hasApplied?: boolean;
  matchPercentage?: number;
  matchDetails?: any;
}

export function JobCard({
  job,
  company,
  styles,
  isSelected,
  onSelect,
  slug,
  onJobApply,
}: JobCardProps) {
  const router = useRouter();

  // Use job ID to create a stable index for icon selection
  // This ensures the same job always gets the same icon and color
  const getStableIconIndex = (id: string) => {
    // Create a hash from the job ID
    const hash = id.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);
    return Math.abs(hash) % jobIcons.length;
  };

  // Get a stable icon index based on job ID
  const iconIndex = getStableIconIndex(job.id);
  const { icon: IconComponent, color: iconColor } = jobIcons[iconIndex];

  // Get applied job IDs from the store
  const { appliedJobIds } = useJobSearchStore();

  // Check if job is applied from any source
  // Using type assertion to handle the hasApplied property
  const extendedJob = job as ExtendedJobData;
  const isApplied =
    job.alreadyApplied || extendedJob.hasApplied || (job.id && appliedJobIds.includes(job.id));

  const handleClick = () => {
    onSelect(job);
    router.push(`?jobId=${job.id}`, { scroll: false });
  };

  return (
    <div
      onClick={handleClick}
      className={`
        bg-white rounded-lg shadow-sm transition-all overflow-hidden cursor-pointer
        ${
          isSelected
            ? 'border-2 border-indigo-500 shadow-lg ring-1 ring-indigo-500/20'
            : 'border border-gray-100 hover:shadow-md'
        }
      `}
    >
      <div className="p-5 relative">
        {/* Top right indicators */}
        <div className="absolute top-3 right-3 flex items-center gap-2">
          {/* Match Score Indicator with Hover Effect */}
          {job.matchPercentage && (
            <div className="group relative">
              <div className="flex items-center space-x-1 bg-white/90 backdrop-blur-sm px-2 py-0.5 rounded-full shadow-sm border border-gray-100 text-xs z-10 cursor-help">
                <div
                  className={`h-1.5 w-1.5 rounded-full ${job.matchPercentage >= 80 ? 'bg-green-500' : job.matchPercentage >= 60 ? 'bg-blue-500' : job.matchPercentage >= 40 ? 'bg-yellow-500' : 'bg-gray-500'}`}
                ></div>
                <span className="font-medium text-gray-800">{job.matchPercentage}%</span>
              </div>

              {/* Hover Tooltip */}
              <div className="absolute right-0 mt-1 w-56 bg-white/95 backdrop-blur-sm rounded-lg shadow-lg p-0 z-20 border border-indigo-100 transition-all duration-200 ease-in-out opacity-0 invisible group-hover:opacity-100 group-hover:visible overflow-hidden">
                {/* Blue dot and percentage at top */}
                <div className="w-full border-l-4 border-indigo-500 px-3 py-2 flex justify-between items-center">
                  <span className="text-xs font-semibold text-gray-700">Our Recommendation</span>
                  {/* <span className="text-xs font-semibold text-amber-500">{job.matchPercentage}%</span> */}
                </div>

                {/* Content with text and icon */}
                <div className="p-3">
                  <div className="text-xs text-gray-800 leading-relaxed">
                    {job.matchDetails?.matchFeedback ||
                      (job.matchPercentage >= 70
                        ? 'Your experience aligns with this position.'
                        : 'Your experience partially matches this position. Strengthening your education would help.')}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Video icon if job has video */}
          {(job.hasVideo || job.videoUrl || (job.videoJDs && job.videoJDs[0]?.videoUrl)) && (
            <div className="text-indigo-600" title="This job has a video">
              <VideoCamera size={18} weight="bold" />
            </div>
          )}
        </div>

        {/* Header with company info */}
        <div className="flex items-center gap-3 mb-4">
          <div
            className="w-8 h-8 rounded-full flex items-center justify-center text-white"
            style={{ backgroundColor: iconColor }}
          >
            <IconComponent size={20} weight="bold" />
          </div>
          <div>
            <div className="text-gray-700 font-medium">
              {job.department || company?.companyName || 'Company'}
            </div>
            <div className="text-xs text-gray-500 flex items-center gap-1">
              <Clock size={12} weight="bold" />
              {formatDistanceToNow(new Date(job.createdAt))} ago
            </div>
          </div>
        </div>

        {/* Job title */}
        <h3 className="text-lg font-bold text-gray-900 mb-3">
          {job.title || job.jobType}
          {/* Add Applied badge when job has been applied to */}
          {isApplied && (
            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
              <CheckCircle size={12} weight="fill" className="mr-1" />
              Applied
            </span>
          )}
        </h3>

        {/* Job metadata tags */}
        <div className="flex flex-wrap gap-2 mb-4">
          {job.typeOfJob && (
            <span
              className={`px-3 py-1 rounded-full text-sm ${chipColors[job.typeOfJob]?.bg || 'bg-gray-50'} ${chipColors[job.typeOfJob]?.text || 'text-gray-700'} flex items-center gap-1`}
            >
              <Briefcase size={14} weight="bold" />
              {toTitleCase(job.typeOfJob)}
            </span>
          )}
          {job.experience && (
            <span
              className={`px-3 py-1 rounded-full text-sm ${chipColors['mid']?.bg || 'bg-gray-50'} ${chipColors['mid']?.text || 'text-gray-700'} flex items-center gap-1`}
            >
              <ChartBar size={14} weight="bold" />
              {getExperienceLevel(job.experience)}
            </span>
          )}
        </div>

        {/* TLDR Summary if available */}
        {job.tldr && (
          <div className="mb-4">
            <p className="text-sm text-gray-600 line-clamp-2">{job.tldr.summary}</p>
          </div>
        )}

        {/* Footer with salary/location and apply button */}
        <div className="mt-4 mb-1">
          <div className="flex flex-wrap gap-y-2 items-center mb-4">
            {job.salaryRange && (
              <div className="flex items-center mr-4 text-gray-700 text-sm">
                <CurrencyDollar size={16} weight="bold" className="mr-1.5 text-emerald-500" />
                <span>
                  {formatSalary(job.salaryRange)}
                  {job.paymentPeriod && (
                    <span className="text-xs text-gray-500">/{job.paymentPeriod}</span>
                  )}
                </span>
              </div>
            )}
            {job.location && job.location.length > 0 && (
              <div className="flex items-center text-gray-700 text-sm">
                <MapPin size={16} weight="bold" className="mr-1.5 text-blue-500" />
                <span>{job.location[0]}</span>
              </div>
            )}
          </div>

          <div onClick={e => e.stopPropagation()} className="relative">
            <ApplyButton
              jobId={job.id}
              isPublic={true}
              colorScheme="indigo"
              className="w-full"
              job={job} // Pass the job object to the ApplyButton
              alreadyApplied={isApplied} // Pass the isApplied state to ensure consistency
              handleLocalApply={onJobApply ? () => onJobApply(job.id) : undefined}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
