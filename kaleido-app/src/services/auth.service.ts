import {
  getSession as getServerSession,
  getAccessToken as getServerAccessToken,
} from '@auth0/nextjs-auth0';
import type { Session } from '@auth0/nextjs-auth0';
import { UserRole } from '@/types/roles';
import { AuthCacheManager } from '@/lib/auth-cache-manager';

/**
 * Industry-standard centralized authentication service
 * Handles token management, refresh, and role validation
 */
export class AuthService {
  private static instance: AuthService;
  private refreshPromise: Promise<string> | null = null;
  private cacheManager: AuthCacheManager;

  private constructor() {
    this.cacheManager = AuthCacheManager.getInstance();
  }

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Get a valid access token, refreshing if necessary
   * This prevents multiple simultaneous refresh attempts
   */
  async getValidAccessToken(): Promise<string | null> {
    try {
      // For client-side, get token from Auth0 React SDK
      if (typeof window !== 'undefined') {
        return await this.getClientAccessToken();
      }

      // For server-side, this would need request context
      console.warn('getValidAccessToken called server-side without request context');
      return null;
    } catch (error) {
      console.error('Failed to get valid access token:', error);
      this.refreshPromise = null;
      return null;
    }
  }

  /**
   * Client-side token retrieval
   */
  private async getClientAccessToken(): Promise<string | null> {
    try {
      // Check if we're already refreshing
      if (this.refreshPromise) {
        return await this.refreshPromise;
      }

      // Try to get token from Auth0
      const response = await fetch('/api/auth/me', {
        credentials: 'include',
      });

      const data = await response.json();

      // Check if session expired and requires logout
      if (!response.ok && data.requiresLogout) {
        console.error('Session expired, clearing all data and redirecting to logout');
        // Use the logout method to ensure complete cleanup
        // In test environment, use simpler redirect
        if (typeof window !== 'undefined' && process.env.NODE_ENV === 'test') {
          this.clearAllAuthData();
          window.location.href = '/api/auth/logout';
        } else {
          // Don't use federated logout - it logs out of Google
          this.logout();
        }
        return null;
      }

      if (response.ok && data.accessToken) {
        return data.accessToken;
      }

      // Token is invalid/expired but session might be valid, try refresh
      if (response.ok && !data.accessToken && !data.tokenError) {
        this.refreshPromise = this.refreshAccessToken();
        const newToken = await this.refreshPromise;
        this.refreshPromise = null;
        return newToken;
      }

      // If we have a token error but session is valid, still try to refresh
      if (data.tokenError && data.user) {
        this.refreshPromise = this.refreshAccessToken();
        const newToken = await this.refreshPromise;
        this.refreshPromise = null;
        return newToken;
      }

      return null;
    } catch (error) {
      console.error('Failed to get client access token:', error);
      this.refreshPromise = null;
      return null;
    }
  }

  /**
   * Refresh the access token using Auth0's refresh token
   */
  private async refreshAccessToken(): Promise<string> {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include', // Include HTTP-only cookies
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(
          `Failed to refresh token: ${data.error || 'Unknown error'} - ${data.details || ''}`
        );
      }

      const { accessToken } = data;
      if (!accessToken) {
        throw new Error('Refresh succeeded but no access token returned');
      }

      return accessToken;
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw error;
    }
  }

  /**
   * Get the current user session with roles from JWT claims
   */
  async getSession(): Promise<SessionWithRole | null> {
    try {
      // For client-side, check cache first
      if (typeof window !== 'undefined') {
        // Check centralized cache
        const cachedSession = this.cacheManager.getCachedSession();
        if (cachedSession && !this.cacheManager.shouldRefresh(cachedSession)) {
          return {
            user: cachedSession.user,
            accessToken: cachedSession.accessToken || '',
            accessTokenScope: '',
            accessTokenExpiresAt: cachedSession.expiresAt,
            idToken: '',
            refreshToken: '',
            role: cachedSession.role,
          };
        }

        const response = await fetch('/api/auth/me', {
          credentials: 'include',
        });

        const data = await response.json();

        // Check if session expired and requires logout
        if (!response.ok && data.requiresLogout) {
          console.error(
            'Session expired in getSession, clearing all data and redirecting to logout'
          );
          // Use the logout method to ensure complete cleanup
          // In test environment, use simpler redirect
          if (typeof window !== 'undefined' && process.env.NODE_ENV === 'test') {
            this.clearAllAuthData();
            window.location.href = '/api/auth/logout';
          } else {
            // Don't use federated logout - it logs out of Google
            this.logout();
          }
          return null;
        }

        if (!response.ok || !data.user) {
          this.cacheManager.clearCache();
          return null;
        }

        // Create session object
        const session: Session = {
          user: data.user,
          accessToken: data.accessToken,
          accessTokenScope: data.scope,
          accessTokenExpiresAt: data.expiresAt,
          idToken: data.idToken,
        };

        // Extract role from JWT claims
        const role = this.extractRoleFromClaims(session);

        // Cache the session
        this.cacheManager.cacheSession(data.user, role, data.accessToken);

        const sessionWithRole = {
          ...session,
          role,
        };

        return sessionWithRole;
      }

      // Server-side would need request context
      console.warn('getSession called server-side without request context');
      return null;
    } catch (error) {
      console.error('Failed to get session:', error);
      this.cacheManager.clearCache();
      return null;
    }
  }

  /**
   * Clear the session cache (useful after role updates)
   */
  clearSessionCache(): void {
    this.cacheManager.clearCache();
  }

  /**
   * Clear all authentication data from browser storage
   */
  private clearAllAuthData(): void {
    // Clear cache manager
    this.cacheManager.clearCache();

    if (typeof window === 'undefined') return;

    // Clear ALL localStorage items (not just auth-related)
    // This ensures complete cache clearing to prevent cross-login issues
    try {
      localStorage.clear();
    } catch (error) {
      console.error('Error clearing localStorage:', error);
      // Fallback: Clear individual keys
      const keysToRemove: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => {
        try {
          localStorage.removeItem(key);
        } catch (e) {
          console.error(`Failed to remove localStorage key ${key}:`, e);
        }
      });
    }

    // Clear sessionStorage completely
    try {
      sessionStorage.clear();
    } catch (error) {
      console.error('Error clearing sessionStorage:', error);
    }

    // Clear IndexedDB if it exists
    if ('indexedDB' in window) {
      indexedDB
        .databases()
        .then(databases => {
          databases.forEach(db => {
            if (db.name) {
              indexedDB.deleteDatabase(db.name);
            }
          });
        })
        .catch(error => {
          console.error('Error clearing IndexedDB:', error);
        });
    }

    // Clear only our application cookies, not all cookies
    // This prevents logging out of Google and other services
    const appCookiePatterns = [
      'appSession',
      'auth0',
      'a0.',
      '_auth0',
      'auth_',
      'userRole',
      'pendingRole',
      'session_',
      'jwt',
      'token',
    ];

    document.cookie.split(';').forEach(cookie => {
      const eqPos = cookie.indexOf('=');
      const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();

      // Only clear cookies that match our app patterns
      if (name && appCookiePatterns.some(pattern => name.includes(pattern))) {
        // Clear cookie for current domain and path
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
        document.cookie = `${name}=;Max-Age=0;path=/`;

        // Also try to clear for current hostname
        const hostname = window.location?.hostname || 'localhost';
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${hostname}`;
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${hostname}`;
      }
    });

    // Clear any service worker caches
    if ('caches' in window) {
      caches
        .keys()
        .then(names => {
          names.forEach(name => {
            caches.delete(name);
          });
        })
        .catch(error => {
          console.error('Error clearing service worker caches:', error);
        });
    }

    // Clear any Auth0 specific data from memory
    if ((window as any).auth0) {
      delete (window as any).auth0;
    }
  }

  /**
   * Extract user role from JWT claims
   * Following Auth0 RBAC best practices
   */
  private extractRoleFromClaims(session: Session): UserRole | null {
    const { user } = session;

    // Standard Auth0 namespace for custom claims
    const namespace = process.env.NEXT_PUBLIC_AUTH0_NAMESPACE || 'https://kaleidotalent.com';

    // Check multiple possible claim locations (Auth0 flexibility)
    const roleClaim =
      user[`${namespace}/role`] ||
      user['role'] ||
      user['pendingRole'] || // Check pendingRole field
      user['https://kaleidotalent.com/role'] ||
      user['userRole'] || // Also check userRole field
      null;

    // Validate role is valid
    if (roleClaim && Object.values(UserRole).includes(roleClaim as UserRole)) {
      return roleClaim as UserRole;
    }

    // Final fallback: check localStorage if we have user sub
    if (user.sub) {
      try {
        const storedRoleData = localStorage.getItem(`userRole_${user.sub}`);
        if (storedRoleData) {
          const parsed = JSON.parse(storedRoleData);
          if (parsed.role && Object.values(UserRole).includes(parsed.role)) {
            return parsed.role as UserRole;
          }
        }
      } catch (e) {
        // Ignore parse errors
      }
    }

    return null;
  }

  /**
   * Check if user has required role(s)
   */
  async hasRole(requiredRoles: UserRole | UserRole[]): Promise<boolean> {
    const session = await this.getSession();

    if (!session?.role) {
      return false;
    }

    const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
    return roles.includes(session.role);
  }

  /**
   * Check if user has required permission(s)
   * More granular than roles - industry best practice
   */
  async hasPermission(permission: string): Promise<boolean> {
    const session = await this.getSession();

    if (!session?.user) {
      return false;
    }

    // Get permissions from JWT claims
    const namespace = process.env.NEXT_PUBLIC_AUTH0_NAMESPACE || 'https://kaleidotalent.com';
    const permissions = session.user[`${namespace}/permissions`] || [];

    return Array.isArray(permissions) && permissions.includes(permission);
  }

  /**
   * Logout user and clear all auth data
   */
  async logout(options?: { returnTo?: string; federated?: boolean }): Promise<void> {
    try {
      // Clear all auth data FIRST before any redirect
      this.clearAllAuthData();

      // Reset any in-memory state
      this.refreshPromise = null;

      // Call the clear-session endpoint to clear server-side cookies
      try {
        await fetch('/api/auth/clear-session', {
          method: 'POST',
          credentials: 'include',
        });
      } catch (clearError) {
        console.error('Failed to clear server session:', clearError);
        // Continue with logout even if clearing fails
      }

      // Build logout URL with parameters to force fresh login
      const logoutParams = new URLSearchParams();

      // Don't use federated logout by default - it logs out of identity provider too
      // Only use it if explicitly requested
      if (options?.federated === true) {
        logoutParams.append('federated', 'true');
      }

      // Add returnTo parameter
      const returnTo = options?.returnTo || '/';
      logoutParams.append('returnTo', returnTo);

      // Force a fresh login by clearing Auth0 session cookie
      logoutParams.append('client_id', process.env.NEXT_PUBLIC_AUTH0_CLIENT_ID || '');

      // Redirect to Auth0 logout with parameters
      const logoutUrl = `/api/auth/logout?${logoutParams.toString()}`;

      // Use replace to prevent back button issues (fallback to href for tests)
      if (typeof window.location.replace === 'function') {
        window.location.replace(logoutUrl);
      } else {
        // Fallback for test environment
        window.location.href = logoutUrl;
      }
    } catch (error) {
      console.error('Logout failed:', error);
      // Force logout anyway with replace to prevent back button issues
      this.clearAllAuthData();

      // Use replace to prevent back button issues (fallback to href for tests)
      // Don't use federated logout here either
      const logoutUrl = '/api/auth/logout';
      if (typeof window.location.replace === 'function') {
        window.location.replace(logoutUrl);
      } else {
        // Fallback for test environment
        window.location.href = logoutUrl;
      }
    }
  }

  /**
   * Force token refresh (useful after role changes)
   */
  async forceRefresh(): Promise<void> {
    this.refreshPromise = null;
    await this.getValidAccessToken();
  }
}

// Export singleton instance
export const authService = AuthService.getInstance();

// Type definitions
export interface SessionWithRole extends Session {
  role: UserRole | null;
}

// Permissions enum for fine-grained access control
export enum Permission {
  // Job permissions
  VIEW_JOBS = 'view:jobs',
  CREATE_JOBS = 'create:jobs',
  EDIT_JOBS = 'edit:jobs',
  DELETE_JOBS = 'delete:jobs',

  // Candidate permissions
  VIEW_CANDIDATES = 'view:candidates',
  MANAGE_CANDIDATES = 'manage:candidates',

  // Company permissions
  VIEW_COMPANIES = 'view:companies',
  MANAGE_COMPANIES = 'manage:companies',

  // Admin permissions
  MANAGE_USERS = 'manage:users',
  VIEW_ANALYTICS = 'view:analytics',
  MANAGE_SYSTEM = 'manage:system',
}

// Role to permissions mapping
export const rolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.SUPER_ADMIN]: Object.values(Permission), // All permissions

  [UserRole.ADMIN]: [
    Permission.VIEW_JOBS,
    Permission.CREATE_JOBS,
    Permission.EDIT_JOBS,
    Permission.DELETE_JOBS,
    Permission.VIEW_CANDIDATES,
    Permission.MANAGE_CANDIDATES,
    Permission.VIEW_COMPANIES,
    Permission.MANAGE_COMPANIES,
    Permission.VIEW_ANALYTICS,
  ],

  [UserRole.EMPLOYER]: [
    Permission.VIEW_JOBS,
    Permission.CREATE_JOBS,
    Permission.EDIT_JOBS,
    Permission.VIEW_CANDIDATES,
    Permission.MANAGE_CANDIDATES,
  ],

  [UserRole.JOB_SEEKER]: [Permission.VIEW_JOBS, Permission.VIEW_COMPANIES],

  [UserRole.GRADUATE]: [Permission.VIEW_JOBS, Permission.VIEW_COMPANIES],

  [UserRole.REFERRAL_PARTNER]: [Permission.VIEW_JOBS, Permission.VIEW_CANDIDATES],
};
