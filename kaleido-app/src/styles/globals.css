/* Global utility classes - shared across the entire platform */

/* Backdrop blur utilities */
.backdrop-blur-md {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* 3D perspective utilities */
.perspective-1000 {
  perspective: 1000px;
}

.rotate-y-10 {
  transform: rotateY(10deg);
}

.-rotate-y-10 {
  transform: rotateY(-10deg);
}

/* Card stack 3D effects */
@property --card-rotate {
  syntax: '<angle>';
  initial-value: 0deg;
  inherits: false;
}

.card-stack {
  transform-style: preserve-3d;
  perspective: 1000px;
}

.card-stack > * {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Basic glass effects */
.glass-effect {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.1);
}

/* Grid background utility */
.bg-grid-white {
  background-size: 40px 40px;
  background-image:
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
}

/* Basic fade-in animation */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out forwards;
}

/* Text shadow utility class */
.text-shadow {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.25);
}

/* Ken Burns Effect for Zen Dashboard */
@keyframes ken-burns {
  0% {
    transform: scale(1) translate(0, 0);
  }
  50% {
    transform: scale(1.1) translate(-2%, -2%);
  }
  100% {
    transform: scale(1.2) translate(-4%, -4%);
  }
}

.animate-ken-burns {
  animation: ken-burns 120s ease-in-out infinite alternate;
  will-change: transform;
}
