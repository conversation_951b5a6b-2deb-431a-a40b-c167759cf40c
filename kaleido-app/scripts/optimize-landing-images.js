const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const inputDir = path.join(__dirname, '../__TODO/TO-CONVERT');
const outputDir = path.join(__dirname, '../public/images/landing');

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Parse filename to extract location and generate metadata
function parseImageName(filename) {
  const name = filename.replace(/\.(jpg|jpeg|png)$/i, '');
  const parts = name.split('-');
  
  // Extract locations and keywords
  const locationKeywords = {
    'paris': { location: 'Paris, France', mood: 'romantic' },
    'dubai': { location: 'Dubai, UAE', mood: 'luxurious' },
    'malibu': { location: 'Malibu, California', mood: 'relaxed' },
    'california': { location: 'California, USA', mood: 'sunny' },
    'portugal': { location: 'Portugal', mood: 'coastal' },
    'sri-lanka': { location: 'Sri Lanka', mood: 'tropical' },
    'spiti': { location: 'Spiti Valley, India', mood: 'adventurous' },
    'kaza': { location: 'Kaza, India', mood: 'serene' },
    'lido': { location: 'Lido di Camaiore, Italy', mood: 'peaceful' },
    'new-york': { location: 'New York City, USA', mood: 'energetic' },
    'manhattan': { location: 'Manhattan, NYC', mood: 'urban' },
    'tatras': { location: 'Tatra Mountains', mood: 'majestic' },
    'santiago': { location: 'Santiago Island', mood: 'tropical' },
    'tarrafal': { location: 'Tarrafal Beach', mood: 'relaxing' },
    'myllykoski': { location: 'Myllykoski, Finland', mood: 'natural' },
    'nurmijarvi': { location: 'Nurmijärvi, Finland', mood: 'tranquil' },
    'indonesia': { location: 'Indonesia', mood: 'exotic' },
    'hong-kong': { location: 'Hong Kong', mood: 'dynamic' },
    'china': { location: 'China', mood: 'bustling' },
    'thailand': { location: 'Thailand', mood: 'tropical' },
    'koh-lipe': { location: 'Koh Lipe, Thailand', mood: 'paradise' },
    'pattaya': { location: 'Pattaya, Thailand', mood: 'vibrant' },
    'braies': { location: 'Lago di Braies, Italy', mood: 'serene' },
    'pragser': { location: 'Pragser Wildsee, Italy', mood: 'alpine' },
    'madagascar': { location: 'Madagascar', mood: 'wild' },
    'sydney': { location: 'Sydney, Australia', mood: 'iconic' },
    'tre-cime': { location: 'Tre Cime, Italy', mood: 'dramatic' },
    'phangnga': { location: 'Phang Nga Bay, Thailand', mood: 'mystical' },
    'sametnangshe': { location: 'Samet Nangshe, Thailand', mood: 'panoramic' },
    'magpupungko': { location: 'Magpupungko, Philippines', mood: 'pristine' },
    'siargao': { location: 'Siargao, Philippines', mood: 'adventurous' },
    'philippines': { location: 'Philippines', mood: 'tropical' },
    'daku': { location: 'Daku Island, Philippines', mood: 'untouched' },
    'burj-khalifa': { location: 'Burj Khalifa, Dubai', mood: 'modern' },
    'eiffel': { location: 'Eiffel Tower, Paris', mood: 'romantic' },
    'arc-de-triomphe': { location: 'Arc de Triomphe, Paris', mood: 'historic' },
    'hawaii': { location: 'Hawaii, USA', mood: 'paradise' }
  };
  
  // Mood keywords based on time/nature
  const moodKeywords = {
    'sunrise': 'hopeful',
    'sunset': 'peaceful',
    'dawn': 'serene',
    'night': 'mysterious',
    'autumn': 'nostalgic',
    'fall': 'cozy',
    'winter': 'crisp',
    'beach': 'relaxing',
    'mountain': 'majestic',
    'forest': 'tranquil',
    'river': 'flowing',
    'ocean': 'vast',
    'sea': 'calming',
    'waves': 'dynamic',
    'waterfall': 'powerful',
    'skyline': 'urban',
    'downtown': 'bustling',
    'aerial': 'expansive',
    'drone': 'cinematic'
  };
  
  let location = 'Unknown';
  let mood = 'scenic';
  
  // Find location
  for (const [key, value] of Object.entries(locationKeywords)) {
    if (name.toLowerCase().includes(key)) {
      location = value.location;
      mood = value.mood;
      break;
    }
  }
  
  // Override mood if specific mood keyword is found
  for (const [key, value] of Object.entries(moodKeywords)) {
    if (name.toLowerCase().includes(key)) {
      mood = value;
      break;
    }
  }
  
  // Format the original filename to be human-readable
  const description = name
    .replace(/-/g, ' ')
    .replace(/\b\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}\b/g, '') // Remove timestamps
    .replace(/\butc\b/gi, '')
    .replace(/\s+/g, ' ')
    .trim()
    .replace(/\b\w/g, l => l.toUpperCase()); // Capitalize first letters
  
  return { location, mood, description };
}

async function optimizeImages() {
  const files = fs.readdirSync(inputDir).filter(file => 
    /\.(jpg|jpeg|png)$/i.test(file)
  );
  
  console.log(`Found ${files.length} images to optimize...`);
  
  const metadata = [];
  let index = 1;
  
  for (const file of files) {
    const inputPath = path.join(inputDir, file);
    const parsedInfo = parseImageName(file);
    
    // Create shortened filename with location hint
    const locationSlug = parsedInfo.location
      .toLowerCase()
      .split(',')[0]
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 15);
    
    const outputName = `${locationSlug}-${index}`;
    const outputFilename = `${outputName}.webp`;
    const outputPath = path.join(outputDir, outputFilename);
    
    try {
      // Optimize image
      await sharp(inputPath)
        .webp({
          quality: 85,
          effort: 6,
          smartSubsample: true,
          reductionEffort: 6
        })
        .resize(1920, null, {
          withoutEnlargement: true,
          fit: 'inside'
        })
        .toFile(outputPath);
      
      console.log(`✓ Optimized: ${file} → ${outputFilename}`);
      
      // Add to metadata
      metadata.push({
        path: `/images/landing/${outputFilename}`,
        location: parsedInfo.location,
        mood: parsedInfo.mood,
        description: parsedInfo.description,
        originalFile: file
      });
      
      // Delete original
      fs.unlinkSync(inputPath);
      console.log(`  Deleted original: ${file}`);
      
      index++;
    } catch (error) {
      console.error(`✗ Failed to optimize ${file}:`, error.message);
    }
  }
  
  // Save metadata JSON
  const metadataPath = path.join(outputDir, 'metadata.json');
  fs.writeFileSync(
    metadataPath,
    JSON.stringify(metadata, null, 2)
  );
  
  console.log(`\n✅ Optimization complete!`);
  console.log(`📁 Images saved to: ${outputDir}`);
  console.log(`📋 Metadata saved to: ${metadataPath}`);
  console.log(`📊 Total images processed: ${metadata.length}`);
  
  return metadata;
}

// Run the optimization
optimizeImages().catch(console.error);