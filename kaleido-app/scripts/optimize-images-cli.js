#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
let folderPath = null;
let shouldRename = false;
let quality = 85;
let maxWidth = 1200;
let keepOriginals = false;

// Parse arguments
for (let i = 0; i < args.length; i++) {
  if (args[i] === '--rename') {
    shouldRename = true;
  } else if (args[i] === '--quality' && args[i + 1]) {
    quality = parseInt(args[i + 1], 10);
    i++;
  } else if (args[i] === '--max-width' && args[i + 1]) {
    maxWidth = parseInt(args[i + 1], 10);
    i++;
  } else if (args[i] === '--keep-originals') {
    keepOriginals = true;
  } else if (args[i] === '--help' || args[i] === '-h') {
    showHelp();
    process.exit(0);
  } else if (!args[i].startsWith('--')) {
    folderPath = args[i];
  }
}

function showHelp() {
  console.log(`
Image Optimization Script

Usage: 
  pnpm optimize:images <folder-path> [options]

Options:
  --rename              Rename files with shorter, descriptive names (e.g., image-1, image-2)
  --quality <number>    Set WebP quality (1-100, default: 85)
  --max-width <number>  Set maximum width in pixels (default: 1200)
  --keep-originals      Keep original files (default: delete originals)
  --help, -h           Show this help message

Examples:
  pnpm optimize:images public/images/new
  pnpm optimize:images public/images/new --rename
  pnpm optimize:images ./assets --quality 90 --max-width 1600
  pnpm optimize:images __TODO/TO-CONVERT --rename --keep-originals
  `);
}

if (!folderPath) {
  console.error('❌ Error: Please provide a folder path');
  showHelp();
  process.exit(1);
}

// Resolve the folder path
const inputDir = path.resolve(folderPath);

if (!fs.existsSync(inputDir)) {
  console.error(`❌ Error: Directory "${inputDir}" does not exist`);
  process.exit(1);
}

async function optimizeImages() {
  // Lazy load sharp only when needed
  const sharp = require('sharp');
  // Get all image files
  const supportedFormats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'];
  const files = fs.readdirSync(inputDir).filter(file => {
    const ext = path.extname(file).toLowerCase();
    return supportedFormats.includes(ext) && !file.startsWith('.');
  });

  if (files.length === 0) {
    console.log('⚠️  No images found to optimize');
    return;
  }

  console.log(`\n🖼️  Found ${files.length} images to optimize`);
  console.log(`📁 Directory: ${inputDir}`);
  console.log(`⚙️  Settings: Quality=${quality}, Max Width=${maxWidth}px`);
  if (shouldRename) console.log(`✏️  Renaming enabled`);
  if (keepOriginals) console.log(`💾 Keeping original files`);
  console.log('');

  let successCount = 0;
  let totalOriginalSize = 0;
  let totalOptimizedSize = 0;

  for (let index = 0; index < files.length; index++) {
    const file = files[index];
    const inputPath = path.join(inputDir, file);
    const fileStats = fs.statSync(inputPath);
    totalOriginalSize += fileStats.size;
    
    // Generate output filename
    let outputName;
    if (shouldRename) {
      // Create simple numbered names
      const baseName = path.basename(inputDir);
      outputName = `${baseName}-${index + 1}`;
    } else {
      // Keep original name without extension
      outputName = path.parse(file).name;
    }
    
    // Ensure the output is always WebP
    const outputPath = path.join(inputDir, `${outputName}.webp`);
    
    // Skip if output already exists and is not the same as input
    if (fs.existsSync(outputPath) && outputPath !== inputPath) {
      console.log(`⏭️  Skipping ${file} - ${outputName}.webp already exists`);
      continue;
    }
    
    try {
      // Optimize image
      const pipeline = sharp(inputPath);
      
      // Get metadata to handle different formats properly
      const metadata = await pipeline.metadata();
      
      // Apply transformations
      await pipeline
        .resize(maxWidth, null, {
          withoutEnlargement: true,
          fit: 'inside'
        })
        .webp({
          quality,
          effort: 6,
          smartSubsample: true,
          reductionEffort: 6,
          // Preserve alpha channel if present
          alphaQuality: metadata.hasAlpha ? 100 : undefined
        })
        .toFile(outputPath);
      
      const outputStats = fs.statSync(outputPath);
      totalOptimizedSize += outputStats.size;
      
      const originalSizeMB = (fileStats.size / 1024 / 1024).toFixed(2);
      const optimizedSizeMB = (outputStats.size / 1024 / 1024).toFixed(2);
      const savings = ((1 - outputStats.size / fileStats.size) * 100).toFixed(1);
      
      console.log(`✅ ${file}`);
      console.log(`   → ${outputName}.webp (${originalSizeMB}MB → ${optimizedSizeMB}MB, -${savings}%)`);
      
      // Delete original file unless keeping originals or it's the same file
      if (!keepOriginals && inputPath !== outputPath) {
        fs.unlinkSync(inputPath);
        console.log(`   🗑️  Deleted original`);
      }
      
      successCount++;
    } catch (error) {
      console.error(`❌ Failed to optimize ${file}:`, error.message);
    }
  }
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 Summary:');
  console.log(`   ✅ Successfully optimized: ${successCount}/${files.length} images`);
  
  if (successCount > 0) {
    const totalOriginalMB = (totalOriginalSize / 1024 / 1024).toFixed(2);
    const totalOptimizedMB = (totalOptimizedSize / 1024 / 1024).toFixed(2);
    const totalSavings = ((1 - totalOptimizedSize / totalOriginalSize) * 100).toFixed(1);
    
    console.log(`   📦 Original size: ${totalOriginalMB}MB`);
    console.log(`   🗜️  Optimized size: ${totalOptimizedMB}MB`);
    console.log(`   💾 Total savings: ${totalSavings}%`);
  }
  
  console.log('='.repeat(60));
  console.log('\n✨ Image optimization complete!\n');
}

// Run the optimization
optimizeImages().catch(error => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});