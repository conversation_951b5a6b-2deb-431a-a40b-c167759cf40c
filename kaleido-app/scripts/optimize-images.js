const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const inputDir = path.join(__dirname, '../__TODO/TO-CONVERT');
const outputDir = inputDir;

// Shorter, more descriptive filenames
const fileNameMap = {
  'group-of-young-students-bonding-outdoors-2025-03-15-04-27-17-utc.jpg': 'students-group',
  'image-of-multiethnic-young-business-people-working-2025-02-16-20-19-44-utc.jpg': 'business-team',
  'the-successful-friend-businessman-and-woman-is-mee-2025-04-01-04-57-04-utc.jpg': 'business-meeting',
  'two-happy-young-adult-couples-piggybacking-the-str-2024-10-19-05-29-39-utc.jpg': 'happy-couples',
};

async function optimizeImages() {
  const files = fs.readdirSync(inputDir).filter(file => file.endsWith('.jpg'));
  
  console.log(`Found ${files.length} images to optimize...`);
  
  for (const file of files) {
    const inputPath = path.join(inputDir, file);
    const newName = fileNameMap[file] || file.replace('.jpg', '');
    const outputPath = path.join(outputDir, `${newName}.webp`);
    
    try {
      await sharp(inputPath)
        .webp({ 
          quality: 85,
          effort: 6,
          smartSubsample: true,
          reductionEffort: 6
        })
        .resize(1200, null, { 
          withoutEnlargement: true,
          fit: 'inside'
        })
        .toFile(outputPath);
      
      console.log(`✓ Optimized: ${file} -> ${newName}.webp`);
      
      // Delete original file
      fs.unlinkSync(inputPath);
      console.log(`  Deleted original: ${file}`);
    } catch (error) {
      console.error(`✗ Failed to optimize ${file}:`, error.message);
    }
  }
  
  console.log('\n✅ Image optimization complete!');
}

optimizeImages().catch(console.error);