# Backend UUID Type Mismatch Fix

## Problem
Users were getting a 500 Internal Server Error when accessing the referral partners backend API:
```
QueryFailedError: operator does not exist: uuid = character varying
```

This error occurred when the backend tried to query referrals by `referralPartnerId`.

## Root Cause
In the `ReferralService.findAll()` method, the query builder was incorrectly trying to cast UUID parameters inline with PostgreSQL's `::uuid` syntax:

```typescript
// INCORRECT - causes type mismatch error
query.andWhere('referral."referralPartnerId" = :referralPartnerId::uuid', {
  referralPartnerId: filters.referralPartnerId,
});
```

TypeORM doesn't properly handle inline type casting in parameterized queries, leading to PostgreSQL receiving a string value where it expects a UUID.

## Solution
Removed the explicit `::uuid` casting and let TypeORM handle the parameter binding correctly:

```typescript
// CORRECT - TypeORM handles UUID comparison properly
query.andWhere('referral."referralPartnerId" = :referralPartnerId', {
  referralPartnerId: filters.referralPartnerId,
});
```

## Files Modified
- `/kaleido-backend/src/modules/referral/services/referral.service.ts`
  - Lines 81-100: Removed `::uuid` casting from all UUID parameter comparisons

## Testing
After deploying this fix:
1. The `/api/referrals` endpoint should work correctly with UUID parameters
2. Referral partners should be able to view their referrals without errors
3. The error "operator does not exist: uuid = character varying" should no longer appear

## Technical Details
- TypeORM automatically handles UUID type conversion when the entity column is defined as `type: 'uuid'`
- The Referral entity correctly defines UUID columns: `@Column({ type: 'uuid' })`
- PostgreSQL will properly compare UUIDs when TypeORM handles the parameter binding

## Deployment
This fix needs to be deployed to the backend production environment for the changes to take effect.