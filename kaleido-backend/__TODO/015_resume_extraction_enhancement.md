# Resume Extraction Enhancement - Complete Implementation Guide with Intelligent Inference

## Overview
This document outlines the comprehensive enhancement of resume extraction across all services (Groq, OpenAI, and resumeParser) to fully populate JobSeeker and Candidate entities with all available information from resumes, including intelligent inference of missing data.

## Key Intelligence Features

### Smart Defaults and Inference
1. **Language Auto-Detection**: Automatically adds the resume's language to the languages array
2. **Current Position Intelligence**: Infers current job title and company from most recent experience
3. **Skills Extraction**: Extracts skills from job descriptions, not just skills section
4. **Career Level Calculation**: Automatically determines seniority based on experience and titles
5. **Location Hierarchy**: Falls back through multiple sources (contact → recent job → education)
6. **Availability Prediction**: Estimates notice period based on seniority level
7. **Salary Estimation**: Provides intelligent salary ranges based on role, location, and experience
8. **Work Authorization**: Infers from education and work history patterns
9. **Remote Preference**: Analyzes job history to determine remote work preferences
10. **Technology Stack Completion**: Adds related technologies (e.g., React → JavaScript, HTML, CSS)

## Entity Field Mapping

### JobSeeker Entity Fields to Extract
```typescript
{
  // Basic Information
  firstName: string,
  lastName: string,
  email: string,
  phone?: string,
  location?: string,
  
  // Professional Summary
  summary?: string,
  
  // Skills & Expertise
  skills: string[],
  
  // Experience (with detailed structure)
  experience?: CandidateExperience[],
  
  // Education
  education?: Education[],
  
  // URLs & Online Presence
  linkedinUrl?: string,
  githubUrl?: string,
  portfolioUrl?: string,
  
  // Certifications & Languages
  certifications?: Certification[],
  languages?: string[],
  
  // Career Preferences (extracted/inferred)
  preferences?: {
    jobTypes?: string[],
    locations?: string[],
    remotePreference?: 'REMOTE_ONLY' | 'HYBRID' | 'ONSITE' | 'FLEXIBLE',
    industries?: string[],
    companySizes?: string[],
    desiredSalary?: {
      min?: number,
      max?: number,
      currency?: string,
      period?: 'HOURLY' | 'MONTHLY' | 'YEARLY'
    }
  },
  
  // Additional Information
  achievements?: Achievement[],
  recommendations?: Recommendation[],
  socialProfiles?: SocialProfile[],
  
  // Work Availability
  workAvailability?: {
    availableFrom?: Date,
    noticePeriod?: string,
    workAuthorization?: string,
    willingToRelocate?: boolean,
    preferredWorkSchedule?: string
  },
  
  // Compensation
  compensation?: {
    currentSalary?: number,
    expectedSalary?: number,
    currency?: string,
    benefits?: string[]
  }
}
```

### Candidate Entity Fields to Extract
```typescript
{
  // Basic Information
  fullName: string,
  firstName: string,
  lastName: string,
  email?: string,
  phone?: string,
  
  // Professional Information
  jobTitle: string,
  currentCompany?: string,
  location: string,
  preferredLocation?: string,
  
  // Summary & Skills
  summary?: string,
  skills: string[],
  
  // Experience
  experience: CandidateExperience[],
  yearsOfExperience?: number,
  
  // URLs & Online Presence
  linkedinUrl?: string,
  githubUrl?: string,
  profileUrl?: string,
  
  // Salary Information
  salary?: {
    min?: number,
    max?: number,
    currency?: string
  },
  
  // Availability
  availableFrom?: Date,
  isRemoteOnly?: boolean,
  
  // Documents & Metadata
  extractionMetadata?: {
    filename: string,
    extractedAt: Date,
    extractionSource: string,
    rawExtractedData?: any
  }[]
}
```

## Enhanced Extraction Prompt with Intelligent Inference

### System Prompt
```
You are an expert resume parser with deep understanding of various resume formats and industry terminology. Your task is to extract comprehensive information from resumes, including both explicit data and intelligently inferred information based on context clues.

Key capabilities:
1. Extract explicit information accurately
2. Intelligently infer missing information from context
3. Apply smart defaults based on resume patterns
4. Recognize industry-specific terminology and skills
5. Extract contact information from various formats
6. Identify certifications, achievements, and awards
7. Parse education details including degrees, institutions, and dates
8. Make logical assumptions based on available data

INTELLIGENT INFERENCE RULES:
1. Always include the language of the resume itself in languages array
2. Current job title = most recent position if not explicitly stated
3. Current company = company of most recent position without end date
4. Years of experience = calculate from earliest job to present
5. Career level = infer from years of experience and job titles
6. Preferred location = current location if not specified
7. Work authorization = infer from location and education
8. Availability = immediate if currently unemployed, otherwise standard notice
9. Remote preference = infer from recent remote positions
10. Skills = extract from job descriptions if not listed separately
```

### Enhanced User Prompt Structure with Smart Inference
```
Extract comprehensive information from this resume and return a detailed JSON object. Apply intelligent inference for missing fields.

Resume content: [RESUME_CONTENT]

Instructions:
1. Extract ALL available information, not just basic fields
2. INTELLIGENTLY INFER missing information using these rules:
   - If resume is in English, add "English" to languages with "PROFESSIONAL" proficiency
   - If resume is in another language, add both that language (NATIVE) and English (level based on quality)
   - Current job title = title from most recent experience entry
   - Current company = company from experience entry with no end date OR most recent if all have end dates
   - Total years of experience = calculate from earliest start date to present
   - Career level: 0-2 years = ENTRY, 3-5 = MID, 6-10 = SENIOR, 11-15 = EXPERT, 16+ = EXECUTIVE
   - If location mentioned in recent job but not in contact, use job location
   - Preferred location = current location unless relocation mentioned
   - Immediate availability if no current job (all positions have end dates)
   - 2-4 weeks notice if currently employed (position without end date)
   - Skills: Extract from job descriptions, responsibilities, and projects even if no skills section
   - Remote preference: REMOTE_ONLY if last 2+ jobs remote, FLEXIBLE if mix, ONSITE if all onsite
   - Work authorization: Infer from education location and current location
   - Expected salary: Research typical range for role/location if not mentioned
3. Parse dates into ISO format (YYYY-MM-DD)
4. Extract both technical and soft skills from entire document
5. Identify current employment status (employed/seeking)
6. Identify work preferences from job history patterns
7. Extract all certifications and qualifications
8. Infer proficiency levels for technical skills based on usage duration
9. Extract any achievements, awards, or recognition
10. Make educated guesses for important missing fields rather than leaving null

Return JSON with this comprehensive structure:
{
  "personalInfo": {
    "fullName": "string",
    "firstName": "string",
    "lastName": "string",
    "email": "string",
    "phone": "string",
    "alternatePhone": "string or null",
    "location": {
      "city": "string",
      "state": "string",
      "country": "string",
      "zipCode": "string or null"
    },
    "nationality": "string or null",
    "dateOfBirth": "YYYY-MM-DD or null",
    "gender": "string or null"
  },
  
  "professionalSummary": {
    "summary": "string - professional summary or objective",
    "headline": "string - professional headline or tagline",
    "currentTitle": "string - current job title",
    "currentCompany": "string - current employer",
    "totalExperienceYears": "number - calculated from work history",
    "careerLevel": "ENTRY | MID | SENIOR | EXECUTIVE | EXPERT"
  },
  
  "skills": {
    "technical": ["array of technical skills"],
    "soft": ["array of soft skills"],
    "tools": ["array of tools and software"],
    "frameworks": ["array of frameworks and libraries"],
    "languages": ["programming languages"],
    "methodologies": ["array of methodologies (Agile, Scrum, etc.)"],
    "industryKnowledge": ["array of industry-specific knowledge"],
    "proficiencyLevels": {
      "skillName": "BEGINNER | INTERMEDIATE | ADVANCED | EXPERT"
    }
  },
  
  "experience": [
    {
      "title": "string - job title",
      "company": "string - company name",
      "location": "string - job location",
      "startDate": "YYYY-MM-DD",
      "endDate": "YYYY-MM-DD or null if current",
      "isCurrent": "boolean",
      "duration": "string - e.g., '2 years 3 months'",
      "description": "string - job description",
      "responsibilities": ["array of key responsibilities"],
      "achievements": ["array of achievements in this role"],
      "technologies": ["technologies used in this role"],
      "teamSize": "number or null",
      "reportingTo": "string or null",
      "industry": "string - industry sector"
    }
  ],
  
  "education": [
    {
      "degree": "string - degree type (BS, MS, PhD, etc.)",
      "field": "string - field of study",
      "institution": "string - university/college name",
      "location": "string - institution location",
      "startDate": "YYYY-MM-DD or null",
      "endDate": "YYYY-MM-DD or null",
      "graduationYear": "YYYY or null",
      "gpa": "number or null",
      "honors": ["array of honors/distinctions"],
      "relevantCoursework": ["array of relevant courses"],
      "thesis": "string or null",
      "activities": ["extracurricular activities"]
    }
  ],
  
  "certifications": [
    {
      "name": "string - certification name",
      "issuer": "string - issuing organization",
      "issueDate": "YYYY-MM-DD or null",
      "expiryDate": "YYYY-MM-DD or null",
      "credentialId": "string or null",
      "credentialUrl": "string or null",
      "skills": ["skills validated by this certification"]
    }
  ],
  
  "languages": [
    {
      "language": "string - language name",
      "proficiency": "NATIVE | FLUENT | PROFESSIONAL | CONVERSATIONAL | BASIC",
      "canRead": "boolean",
      "canWrite": "boolean",
      "canSpeak": "boolean"
    }
  ],
  
  "onlinePresence": {
    "linkedin": "URL or null",
    "github": "URL or null",
    "portfolio": "URL or null",
    "personalWebsite": "URL or null",
    "twitter": "URL or null",
    "stackoverflow": "URL or null",
    "medium": "URL or null",
    "behance": "URL or null",
    "dribbble": "URL or null",
    "kaggle": "URL or null",
    "otherProfiles": [
      {
        "platform": "string",
        "url": "URL"
      }
    ]
  },
  
  "achievements": [
    {
      "title": "string - achievement title",
      "description": "string - achievement description",
      "date": "YYYY-MM-DD or null",
      "issuer": "string or null",
      "type": "AWARD | RECOGNITION | PUBLICATION | PATENT | SPEAKING | OTHER"
    }
  ],
  
  "projects": [
    {
      "name": "string - project name",
      "description": "string - project description",
      "role": "string - role in project",
      "startDate": "YYYY-MM-DD or null",
      "endDate": "YYYY-MM-DD or null",
      "url": "URL or null",
      "technologies": ["technologies used"],
      "outcomes": ["project outcomes/results"]
    }
  ],
  
  "publications": [
    {
      "title": "string - publication title",
      "type": "JOURNAL | CONFERENCE | BOOK | ARTICLE | OTHER",
      "publisher": "string or null",
      "date": "YYYY-MM-DD or null",
      "url": "URL or null",
      "coAuthors": ["array of co-authors"],
      "description": "string or null"
    }
  ],
  
  "volunteerWork": [
    {
      "organization": "string",
      "role": "string",
      "cause": "string or null",
      "startDate": "YYYY-MM-DD or null",
      "endDate": "YYYY-MM-DD or null",
      "description": "string",
      "achievements": ["array of achievements"]
    }
  ],
  
  "preferences": {
    "jobTypes": ["FULL_TIME", "PART_TIME", "CONTRACT", "FREELANCE", "INTERNSHIP"],
    "workLocation": ["REMOTE", "HYBRID", "ONSITE"],
    "willingToRelocate": "boolean or null",
    "preferredLocations": ["array of preferred cities/countries"],
    "industries": ["array of preferred industries"],
    "companySizes": ["STARTUP", "SMB", "ENTERPRISE"],
    "travelWillingness": "0-100% or null",
    "availability": {
      "startDate": "YYYY-MM-DD or null",
      "noticePeriod": "string or null",
      "immediatelyAvailable": "boolean or null"
    }
  },
  
  "compensation": {
    "currentSalary": {
      "amount": "number or null",
      "currency": "string or null",
      "period": "HOURLY | MONTHLY | YEARLY"
    },
    "expectedSalary": {
      "min": "number or null",
      "max": "number or null",
      "currency": "string or null",
      "period": "HOURLY | MONTHLY | YEARLY"
    },
    "otherCompensation": ["array of other compensation expectations"]
  },
  
  "references": [
    {
      "name": "string",
      "title": "string",
      "company": "string",
      "relationship": "string",
      "email": "string or null",
      "phone": "string or null",
      "canContact": "boolean"
    }
  ],
  
  "additionalInfo": {
    "securityClearance": "string or null",
    "workAuthorization": "string or null",
    "visaStatus": "string or null",
    "driversLicense": "boolean or null",
    "militaryService": {
      "branch": "string or null",
      "rank": "string or null",
      "startDate": "YYYY-MM-DD or null",
      "endDate": "YYYY-MM-DD or null",
      "honors": ["array of military honors"]
    },
    "disabilities": "string or null",
    "interests": ["array of personal interests"],
    "hobbies": ["array of hobbies"]
  },
  
  "metadata": {
    "resumeFormat": "PDF | DOCX | TXT | HTML | OTHER",
    "lastUpdated": "inferred date or null",
    "resumeLength": "number of pages or sections",
    "extractionConfidence": {
      "overall": "0-100",
      "personalInfo": "0-100",
      "experience": "0-100",
      "education": "0-100",
      "skills": "0-100"
    },
    "missingCriticalInfo": ["array of missing critical fields"],
    "parseWarnings": ["array of parsing warnings or issues"]
  }
}

IMPORTANT RULES:
1. Return ONLY valid JSON without markdown formatting
2. NEVER use null when you can intelligently infer a value
3. Ensure all dates are in YYYY-MM-DD format or infer approximate dates
4. Calculate totalExperienceYears by summing all work experiences
5. Infer careerLevel from years of experience and job titles
6. Separate technical and soft skills intelligently
7. If salary not mentioned, provide educated range based on role/location/experience
8. Identify current employment status from experience dates
9. Parse location into structured format when possible
10. Extract ALL URLs found in the resume
11. Add confidence scores for inferred vs extracted data
```

## Intelligent Inference Rules (Detailed)

### Language Detection and Inference
```javascript
// Automatic language detection
if (resumeLanguage === 'English') {
  languages.push({ language: 'English', proficiency: 'NATIVE' });
} else {
  languages.push({ language: detectedLanguage, proficiency: 'NATIVE' });
  // If resume has good English despite being in another language
  if (hasGoodEnglishSections) {
    languages.push({ language: 'English', proficiency: 'PROFESSIONAL' });
  }
}

// Infer from education location
if (education.country === 'USA' || education.country === 'UK') {
  ensureLanguageExists('English', 'NATIVE');
}

// Infer from international experience
if (hasInternationalExperience) {
  ensureLanguageExists('English', 'PROFESSIONAL');
}
```

### Current Position Inference
```javascript
// Smart current position detection
currentJobTitle = experience.find(job => !job.endDate)?.title 
  || experience.sort(byEndDate)[0].title;

currentCompany = experience.find(job => !job.endDate)?.company
  || (recentJobEndedWithin(3, 'months') ? experience[0].company : null);

// Employment status
isCurrentlyEmployed = experience.some(job => !job.endDate)
  || (mostRecentJob.endDate && daysSince(mostRecentJob.endDate) < 30);
```

### Skills Extraction Intelligence
```javascript
// Extract skills from multiple sources
skills = [
  ...explicitSkillsSection,
  ...extractFromJobDescriptions(experience),
  ...extractFromProjects(projects),
  ...extractFromEducation(education),
  ...inferFromJobTitles(experience),
  ...inferFromCertifications(certifications)
];

// Categorize automatically
technicalSkills = skills.filter(isTeechnical);
softSkills = inferSoftSkills(jobDescriptions, achievements);
tools = extractTools(entireResume);
```

### Career Level Inference
```javascript
function inferCareerLevel(yearsExp, jobTitles, education) {
  // Title-based inference
  if (jobTitles.includes(['CEO', 'CTO', 'VP', 'Director'])) return 'EXECUTIVE';
  if (jobTitles.includes(['Senior', 'Lead', 'Principal'])) return 'SENIOR';
  if (jobTitles.includes(['Junior', 'Associate', 'Entry'])) return 'ENTRY';
  
  // Experience-based inference
  if (yearsExp >= 15) return 'EXECUTIVE';
  if (yearsExp >= 10) return 'EXPERT';
  if (yearsExp >= 6) return 'SENIOR';
  if (yearsExp >= 3) return 'MID';
  return 'ENTRY';
  
  // Education recency adjustment
  if (education.graduationYear && yearsSince(education.graduationYear) < 2) {
    return 'ENTRY';
  }
}
```

### Location and Preference Inference
```javascript
// Location inference hierarchy
location = personalInfo.location
  || mostRecentJob.location
  || education[0].location
  || inferFromPhoneNumber(phone);

// Work preference inference
remotePreference = inferFromHistory();
function inferFromHistory() {
  const recentJobs = experience.slice(0, 3);
  const remoteCount = recentJobs.filter(job => 
    job.location.includes('Remote') || job.description.includes('remote')
  ).length;
  
  if (remoteCount === recentJobs.length) return 'REMOTE_ONLY';
  if (remoteCount > 0) return 'FLEXIBLE';
  return 'ONSITE';
}
```

### Availability Inference
```javascript
// Smart availability detection
if (!isCurrentlyEmployed) {
  availability = { 
    startDate: 'immediately',
    noticePeriod: '0 days'
  };
} else {
  // Infer based on seniority
  const noticePeriod = inferNoticePeriod(careerLevel);
  availability = {
    startDate: addWeeks(today, noticePeriod),
    noticePeriod: `${noticePeriod} weeks`
  };
}

function inferNoticePeriod(level) {
  switch(level) {
    case 'EXECUTIVE': return 12;
    case 'EXPERT': return 8;
    case 'SENIOR': return 4;
    case 'MID': return 2;
    default: return 2;
  }
}
```

### Compensation Inference
```javascript
// Intelligent salary estimation
if (!explicitSalary) {
  compensation = {
    expectedSalary: estimateSalaryRange(
      jobTitle,
      location,
      yearsOfExperience,
      skills,
      education,
      careerLevel
    ),
    currency: inferCurrencyFromLocation(location),
    confidence: 'ESTIMATED'
  };
}

function estimateSalaryRange(title, location, experience, skills, education, level) {
  // Base salary by role and location
  let baseSalary = getSalaryBenchmark(title, location);
  
  // Adjustments
  baseSalary *= (1 + (experience * 0.05)); // 5% per year
  baseSalary *= hasAdvancedDegree(education) ? 1.15 : 1;
  baseSalary *= hasInDemandSkills(skills) ? 1.2 : 1;
  
  return {
    min: baseSalary * 0.9,
    max: baseSalary * 1.1
  };
}
```

### Work Authorization Inference
```javascript
// Smart work authorization detection
workAuthorization = explicitVisa
  || inferFromEducationAndLocation();

function inferFromEducationAndLocation() {
  const studiedInCountry = education.some(edu => 
    edu.country === currentLocation.country
  );
  const workingInCountry = experience.some(job => 
    job.location.includes(currentLocation.country)
  );
  
  if (studiedInCountry && workingInCountry) {
    return 'Authorized to work (inferred from education and experience)';
  }
  return 'May require sponsorship';
}
```

## Implementation Updates Required

### 1. Groq Service (groq.service.ts)
- Update line 54-98: Replace with enhanced system and user prompts
- Expand return structure to include all new fields
- Add field mapping logic to convert extracted data to entity format

### 2. OpenAI Service (via resumeParser.ts)
- Update line 47-75: Replace with enhanced extraction prompt
- Expand JSON structure to capture all fields
- Add comprehensive field mapping

### 3. Resume Parser (resumeParser.ts)
- Update line 105-133: Add mapping for all new extracted fields
- Add logic to populate Candidate entity completely
- Include extraction metadata

### 4. Additional Services to Check
- Search for other resume extraction implementations
- Update bulk upload services if they exist
- Update any API endpoints that handle resume uploads

## Additional Smart Inference Examples

### Technology Stack Inference
```javascript
// Infer related technologies
if (skills.includes('React')) {
  ensureSkills(['JavaScript', 'HTML', 'CSS', 'npm']);
}
if (skills.includes('Django')) {
  ensureSkills(['Python', 'SQL']);
}
if (skills.includes('Spring Boot')) {
  ensureSkills(['Java', 'Maven/Gradle']);
}

// Infer from project descriptions
if (projectDescription.includes('REST API')) {
  ensureSkills(['API Development', 'HTTP', 'JSON']);
}
```

### Soft Skills Inference
```javascript
// Infer from experience and achievements
softSkills = [];
if (managedTeamSize > 0) softSkills.push('Leadership', 'Team Management');
if (experience.some(job => job.title.includes('Senior'))) softSkills.push('Mentoring');
if (projects.some(p => p.description.includes('client'))) softSkills.push('Client Communication');
if (experience.length > 3) softSkills.push('Adaptability');
if (achievements.length > 0) softSkills.push('Results-Oriented');
```

### Education Level Inference
```javascript
// Infer highest education level
if (!education || education.length === 0) {
  // Infer from job titles and experience
  if (jobTitle.includes('PhD') || jobTitle.includes('Scientist')) {
    education = [{ degree: 'PhD (Inferred)', field: relatedField }];
  } else if (yearsOfExperience > 5 && seniorPosition) {
    education = [{ degree: 'Bachelor (Inferred)', field: 'Related Field' }];
  }
}
```

### Industry and Domain Inference
```javascript
// Infer industry from companies and skills
industries = [];
if (companies.some(c => c.includes('Bank') || c.includes('Financial'))) {
  industries.push('Finance', 'Banking');
}
if (skills.some(s => ['React', 'Angular', 'Vue'].includes(s))) {
  industries.push('Technology', 'Software Development');
}
if (experience.some(e => e.description.includes('patient') || e.description.includes('medical'))) {
  industries.push('Healthcare');
}
```

### Company Size Preference Inference
```javascript
// Infer from work history
companySizePreference = [];
const companySizes = experience.map(job => inferCompanySize(job.company));
if (companySizes.includes('startup')) companySizePreference.push('STARTUP');
if (companySizes.includes('enterprise')) companySizePreference.push('ENTERPRISE');
if (companySizes.every(s => s === 'startup')) {
  companySizePreference = ['STARTUP'];
} else if (companySizes.every(s => s === 'enterprise')) {
  companySizePreference = ['ENTERPRISE'];
} else {
  companySizePreference = ['STARTUP', 'SMB', 'ENTERPRISE'];
}
```

## Field Mapping Logic with Intelligent Defaults

### From Extracted Data to JobSeeker Entity
```typescript
function mapToJobSeeker(extractedData: any): Partial<JobSeeker> {
  // Apply intelligent defaults
  const enrichedData = applyIntelligentDefaults(extractedData);
  
  return {
    // Basic Info
    firstName: enrichedData.personalInfo.firstName,
    lastName: enrichedData.personalInfo.lastName,
    email: enrichedData.personalInfo.email,
    phone: enrichedData.personalInfo.phone,
    location: formatLocation(enrichedData.personalInfo.location),
    
    // Professional
    summary: extractedData.professionalSummary.summary,
    skills: [
      ...extractedData.skills.technical,
      ...extractedData.skills.soft,
      ...extractedData.skills.tools
    ],
    
    // Experience
    experience: extractedData.experience.map(exp => ({
      title: exp.title,
      company: exp.company,
      location: exp.location,
      startDate: exp.startDate,
      endDate: exp.endDate,
      description: exp.description,
      responsibilities: exp.responsibilities,
      achievements: exp.achievements
    })),
    
    // Education
    education: extractedData.education.map(edu => ({
      degree: edu.degree,
      field: edu.field,
      institution: edu.institution,
      location: edu.location,
      startDate: edu.startDate,
      endDate: edu.endDate,
      gpa: edu.gpa,
      honors: edu.honors
    })),
    
    // Online Presence
    linkedinUrl: extractedData.onlinePresence.linkedin,
    githubUrl: extractedData.onlinePresence.github,
    portfolioUrl: extractedData.onlinePresence.portfolio,
    
    // Certifications
    certifications: extractedData.certifications.map(cert => ({
      name: cert.name,
      issuer: cert.issuer,
      issueDate: cert.issueDate,
      expiryDate: cert.expiryDate,
      credentialId: cert.credentialId,
      credentialUrl: cert.credentialUrl
    })),
    
    // Languages
    languages: extractedData.languages.map(lang => 
      `${lang.language} (${lang.proficiency})`
    ),
    
    // Preferences
    preferences: {
      jobTypes: extractedData.preferences.jobTypes,
      locations: extractedData.preferences.preferredLocations,
      remotePreference: mapRemotePreference(extractedData.preferences.workLocation),
      industries: extractedData.preferences.industries,
      companySizes: extractedData.preferences.companySizes,
      desiredSalary: extractedData.compensation.expectedSalary
    },
    
    // Achievements
    achievements: extractedData.achievements.map(ach => ({
      title: ach.title,
      description: ach.description,
      date: ach.date,
      issuer: ach.issuer,
      type: ach.type
    })),
    
    // Work Availability
    workAvailability: {
      availableFrom: extractedData.preferences.availability.startDate,
      noticePeriod: extractedData.preferences.availability.noticePeriod,
      workAuthorization: extractedData.additionalInfo.workAuthorization,
      willingToRelocate: extractedData.preferences.willingToRelocate
    },
    
    // Compensation
    compensation: {
      currentSalary: extractedData.compensation.currentSalary?.amount,
      expectedSalary: extractedData.compensation.expectedSalary?.min,
      currency: extractedData.compensation.expectedSalary?.currency,
      benefits: extractedData.compensation.otherCompensation
    },
    
    // Social Profiles
    socialProfiles: extractedData.onlinePresence.otherProfiles?.map(profile => ({
      platform: profile.platform,
      url: profile.url
    }))
  };
}
```

### From Extracted Data to Candidate Entity
```typescript
function mapToCandidate(extractedData: any, jobId: string): Partial<Candidate> {
  const yearsOfExperience = extractedData.professionalSummary.totalExperienceYears;
  
  return {
    // Basic Info
    fullName: extractedData.personalInfo.fullName,
    firstName: extractedData.personalInfo.firstName,
    lastName: extractedData.personalInfo.lastName,
    email: extractedData.personalInfo.email,
    phone: extractedData.personalInfo.phone,
    
    // Professional Info
    jobTitle: extractedData.professionalSummary.currentTitle,
    currentCompany: extractedData.professionalSummary.currentCompany,
    location: `${extractedData.personalInfo.location.city}, ${extractedData.personalInfo.location.country}`,
    preferredLocation: extractedData.preferences.preferredLocations?.[0],
    
    // Summary & Skills
    summary: extractedData.professionalSummary.summary,
    skills: [
      ...extractedData.skills.technical,
      ...extractedData.skills.soft,
      ...extractedData.skills.tools
    ],
    
    // Experience
    experience: extractedData.experience.map(exp => ({
      title: exp.title,
      company: exp.company,
      location: exp.location,
      startDate: exp.startDate,
      endDate: exp.endDate,
      duration: exp.duration,
      description: exp.description
    })),
    yearsOfExperience: yearsOfExperience,
    
    // URLs
    linkedinUrl: extractedData.onlinePresence.linkedin,
    githubUrl: extractedData.onlinePresence.github,
    profileUrl: extractedData.onlinePresence.portfolio,
    
    // Salary
    salary: extractedData.compensation.expectedSalary ? {
      min: extractedData.compensation.expectedSalary.min,
      max: extractedData.compensation.expectedSalary.max,
      currency: extractedData.compensation.expectedSalary.currency
    } : undefined,
    
    // Availability
    availableFrom: extractedData.preferences.availability?.startDate ? 
      new Date(extractedData.preferences.availability.startDate) : undefined,
    isRemoteOnly: extractedData.preferences.workLocation?.includes('REMOTE') &&
                  extractedData.preferences.workLocation.length === 1,
    
    // Metadata
    extractionMetadata: [{
      filename: 'resume.pdf', // Will be replaced with actual filename
      extractedAt: new Date(),
      extractionSource: 'AI_EXTRACTION',
      rawExtractedData: extractedData,
      matchScore: extractedData.metadata.extractionConfidence.overall
    }],
    
    // Additional fields
    jobId: jobId,
    source: 'RESUME_UPLOAD',
    status: CandidateStatus.NEW,
    contacted: false,
    contactMethod: ContactMethod.EMAIL
  };
}
```

## Testing Strategy

### Test Cases to Validate
1. Basic resume with minimal information
2. Comprehensive resume with all fields
3. Resume with non-standard formatting
4. Resume with multiple languages
5. Resume with salary expectations
6. Resume with certifications and achievements
7. Technical vs non-technical resumes
8. Executive vs entry-level resumes

### Validation Checklist
- [ ] All personal information extracted correctly
- [ ] Experience calculated accurately
- [ ] Skills categorized properly
- [ ] Education parsed with dates
- [ ] Certifications extracted
- [ ] Languages identified
- [ ] URLs found and validated
- [ ] Salary information captured
- [ ] Preferences inferred correctly
- [ ] Availability dates parsed

## Migration Considerations

### Backward Compatibility
- Ensure existing resume data is not affected
- Provide fallback for missing fields
- Handle partial extraction gracefully

### Performance Optimization
- Consider caching extraction results
- Implement batch processing for bulk uploads
- Add extraction confidence scores
- Log extraction metrics for improvement

## Security Considerations

### Data Privacy
- Never expose sensitive information in logs
- Sanitize extracted data before storage
- Implement PII detection and masking
- Ensure GDPR compliance for EU candidates

### Input Validation
- Validate all extracted URLs
- Sanitize HTML content
- Check for malicious patterns
- Limit extraction sizes

## Current Resume Extraction Services Identified

### 1. **Groq Service** (`src/shared/services/groq.service.ts`)
- **Location**: Lines 45-174 (extractResumeData method)
- **Current Fields Extracted**: Basic fields only (name, email, phone, location, jobTitle, skills, experience, education, linkedinUrl, githubUrl)
- **Model Used**: llama-3.3-70b-versatile
- **Speed**: 500+ tokens/second
- **Needs Update**: Expand extraction to include all entity fields

### 2. **OpenAI Service** (`src/shared/services/openai.service.ts`)
- **Location**: Uses ContentGeneratorService and ApplicationProcessorService
- **Current Usage**: Primarily for job information extraction, not resume parsing
- **Needs Update**: May need to add resume extraction capability if not present

### 3. **Resume Parser** (`src/shared/resumeParser.ts`)
- **Location**: Lines 25-221
- **Current Fields Extracted**: Basic fields similar to Groq
- **Model Used**: GPT-4O-MINI
- **Needs Update**: Expand prompt and field mapping

### 4. **Resume Parser Service** (`src/modules/candidate/services/resume-parser.service.ts`)
- **Location**: Lines 43-765
- **Current Fields Extracted**: More comprehensive than others (includes email, phone, currentCompany, yearsOfExperience, preferredLocation)
- **Models Used**: GPT-4 (default), GPT-3.5-turbo (fallback)
- **Needs Update**: Still missing many JobSeeker fields

### 5. **Multi-AI Resume Parser Service** (`src/shared/services/multi-ai-resume-parser.service.ts`)
- **Purpose**: Load balancing across multiple AI providers
- **Providers**: Groq (priority 1), OpenAI GPT-4O-MINI (priority 2), OpenAI GPT-4 (priority 3)
- **Needs Update**: Ensure all providers use enhanced prompts

### 6. **Enhanced Resume Parser Service** (`src/shared/services/enhanced-resume-parser.service.ts`)
- **Purpose**: Wrapper for multi-AI service with performance tracking
- **Needs Update**: Ensure it handles all new fields

### 7. **Bulk Talent Upload Service** (`src/modules/candidate/services/bulk-talent-upload.service.ts`)
- **Purpose**: Handles bulk CSV and resume uploads
- **Uses**: Groq service for extraction
- **Needs Update**: Ensure CSV mapping includes all fields

## Implementation Plan (No Code Changes Yet)

### Phase 1: Analysis and Documentation ✅
1. ✅ Document all entity fields needed
2. ✅ Create comprehensive extraction prompt
3. ✅ Identify all services that need updates
4. ✅ Create field mapping logic

### Phase 2: Prompt Enhancement (To Be Done)
1. **Groq Service Update**
   - Replace system prompt (lines 54-98)
   - Expand JSON structure
   - Add comprehensive field mapping

2. **Resume Parser Update** 
   - Update prompt generation (lines 47-75)
   - Expand extraction fields
   - Add new field mappings

3. **Resume Parser Service Update**
   - Update prompts in generateSystemPrompt and generateUserPrompt
   - Expand fallbackToOpenAI method
   - Update mapProcessedDataToCandidateDto

4. **Multi-AI Service Update**
   - Ensure convertToCreateCandidateDto handles all fields
   - Update extraction prompts for all providers

### Phase 3: Entity Mapping (To Be Done)
1. Create comprehensive mapping functions
2. Handle null/undefined values gracefully
3. Add data validation
4. Implement field transformations

### Phase 4: Testing (To Be Done)
1. Unit tests for each extraction service
2. Integration tests with sample resumes
3. Performance benchmarks
4. Field coverage validation

### Phase 5: Deployment (To Be Done)
1. Feature flag implementation
2. Gradual rollout
3. Monitoring setup
4. Rollback plan

## Missing Fields Currently Not Extracted

### Critical Fields Missing
1. **Certifications** - Professional certifications and credentials
2. **Languages** - Language proficiencies
3. **Achievements** - Awards and recognitions
4. **Work Availability** - Start date, notice period
5. **Compensation** - Current/expected salary
6. **Education Details** - GPA, honors, activities
7. **Portfolio URL** - Portfolio website
8. **Video Intro URL** - Video introduction
9. **Social Profiles** - Other social media profiles
10. **References** - Professional references

### Nice-to-Have Fields Missing
1. **Projects** - Personal/professional projects
2. **Publications** - Published works
3. **Volunteer Work** - Community service
4. **Security Clearance** - Government clearances
5. **Work Authorization** - Visa status
6. **Interests/Hobbies** - Personal interests
7. **Metadata** - Extraction confidence scores

## Prompt Comparison

### Current Groq Prompt (Limited)
```json
{
  "fullName": "string",
  "firstName": "string", 
  "lastName": "string",
  "email": "string",
  "phone": "string",
  "location": "string",
  "jobTitle": "string",
  "currentCompany": "string",
  "yearsOfExperience": number,
  "summary": "string",
  "skills": ["string"],
  "experience": [...],
  "education": [...],
  "linkedinUrl": "string",
  "githubUrl": "string"
}
```

### Enhanced Prompt (Comprehensive)
- See "Enhanced User Prompt Structure" section above for full structure
- Includes 20+ additional field categories
- Covers 100+ individual data points

## Risk Assessment

### High Risk Areas
1. **Performance Impact**: More comprehensive extraction may slow down processing
2. **Token Limits**: Larger prompts and responses may hit token limits
3. **Accuracy**: More fields may reduce extraction accuracy
4. **Cost**: More tokens = higher API costs

### Mitigation Strategies
1. **Incremental Rollout**: Start with most important fields
2. **Parallel Processing**: Use load balancing for bulk uploads
3. **Caching**: Cache extraction results
4. **Monitoring**: Track extraction quality metrics
5. **Fallback Logic**: Keep existing extraction as fallback

## Quick Inference Reference Guide

### Always Infer These Fields
```
✅ Language: Always include resume language (English → PROFESSIONAL/NATIVE)
✅ Current Job Title: From most recent experience entry
✅ Current Company: From job without end date or most recent
✅ Years of Experience: Calculate from all experiences
✅ Career Level: Based on years (0-2=ENTRY, 3-5=MID, 6-10=SENIOR, 11+=EXPERT/EXECUTIVE)
✅ Skills: Extract from entire document, not just skills section
✅ Preferred Location: Default to current location
✅ Availability: Immediate if unemployed, 2-12 weeks based on seniority
✅ Remote Preference: Based on recent job patterns
✅ Education: Infer degree level from job titles if missing
```

### Inference Priority Order
1. **Explicit mention** → Use exact value
2. **Recent experience** → Infer from last 1-2 jobs
3. **Historical pattern** → Analyze entire work history
4. **Industry standards** → Apply typical defaults
5. **Educated guess** → Better than null

### Common Inference Patterns
- **No end date on job** → Currently employed
- **All recent jobs remote** → Prefers remote work
- **International experience** → Likely bilingual
- **Startup history** → Prefers startup environment
- **Management titles** → Has leadership skills
- **Long tenure** → Values stability
- **Frequent changes** → Open to new opportunities
- **Academic background** → May have research skills
- **Military experience** → Strong discipline and leadership

## Recommendations

### Immediate Actions (Before Code Changes)
1. ✅ Review this documentation thoroughly
2. ✅ Understand all inference rules
3. Prioritize which fields are most important
4. Decide on rollout strategy
5. Set up monitoring infrastructure
6. Create test dataset with various resume formats

### Implementation Order
1. Start with `groq.service.ts` (fastest provider)
2. Update `resume-parser.service.ts` (most comprehensive)
3. Update `resumeParser.ts` (simplest)
4. Update multi-AI and enhanced services
5. Update bulk upload service

### Testing Strategy
1. Create test resumes with all field variations
2. Benchmark extraction accuracy
3. Measure performance impact
4. Validate field mappings
5. Test edge cases and error handling

## Next Steps

1. Review and approve this documentation
2. Prioritize fields for extraction
3. Create feature flags for gradual rollout
4. Begin implementation with highest priority service
5. Set up monitoring and metrics
6. Deploy to staging for testing
7. Gradual production rollout with monitoring