# Resume Extraction Enhancement - Exact Entity Field Mapping

## Overview
This document focuses on extracting and intelligently inferring data to populate ONLY the existing fields in JobSeeker and Candidate entities. No new fields or improvements - just maximizing data extraction for current schema.

## Exact JobSeeker Entity Fields to Populate

```typescript
// From src/modules/job-seeker/entities/job-seeker.entity.ts
{
  // Basic Information (REQUIRED)
  firstName: string,              // Extract from name
  lastName: string,               // Extract from name
  email: string,                  // Extract email address
  
  // Basic Information (OPTIONAL)
  phone?: string,                 // Extract phone number
  location?: string,              // Extract/infer from resume
  myProfileImage?: string,        // Cannot extract from resume (skip)
  
  // Professional Information
  summary?: string,               // Extract or generate from experience
  skills: string[],               // Extract from entire document
  experience?: CandidateExperience[], // Extract all work experience
  
  // URLs
  resumeUrl?: string,             // Set to uploaded file URL
  linkedinUrl?: string,           // Extract if mentioned
  githubUrl?: string,             // Extract if mentioned
  portfolioUrl?: string,          // Extract if mentioned
  videoIntroUrl?: string,         // Cannot extract from resume (skip)
  
  // Education & Certifications
  education?: Education[],        // Extract education history
  certifications?: Certification[], // Extract certifications
  languages?: string[],           // Extract + infer from resume language
  
  // Preferences (Can be intelligently inferred)
  preferences?: JobPreferences,   // Infer from experience patterns
  
  // Additional Fields
  achievements?: Achievement[],    // Extract awards/achievements
  recommendations?: Recommendation[], // Extract if references mentioned
  workAvailability?: WorkAvailability, // Infer from current employment
  compensation?: Compensation,     // Extract/infer salary expectations
  socialProfiles?: SocialProfile[], // Extract other social media URLs
  
  // Values (Cannot extract)
  myValues?: string[],            // Cannot extract (skip)
  passportId?: string,            // Cannot extract (skip)
}
```

## Exact Candidate Entity Fields to Populate

```typescript
// From src/modules/candidate/entities/candidate.entity.ts
{
  // Basic Information (REQUIRED)
  fullName: string,               // Combine first + last name
  firstName: string,              // Extract from name
  lastName: string,               // Extract from name
  
  // Professional Information
  jobTitle: string,               // Current/most recent job title
  location: string,               // Current location
  summary?: string,               // Professional summary
  skills: string[],               // All skills
  experience?: CandidateExperience[], // Work history
  
  // Contact Information
  email?: string,                 // Email address
  phone?: string,                 // Phone number
  
  // URLs
  profileUrl?: string,            // Resume URL
  linkedinUrl?: string,           // LinkedIn profile
  githubUrl?: string,             // GitHub profile
  
  // Current Employment
  currentCompany?: string,        // Infer from latest job
  yearsOfExperience?: number,     // Calculate from experience
  
  // Preferences
  preferredLocation?: string,     // Infer from current location
  isRemoteOnly?: boolean,         // Infer from job history
  
  // Availability & Compensation
  availableFrom?: Date,           // Infer from employment status
  salary?: {                      // Extract/infer if mentioned
    min?: number,
    max?: number,
    currency?: string
  },
  
  // Metadata
  extractionMetadata?: {          // Track extraction details
    filename: string,
    extractedAt: Date,
    extractionSource: string,
    rawExtractedData?: any,
    matchScore?: number
  }[]
}
```

## Enhanced Extraction Prompt with Smart Inference (Entity-Specific)

```
You are an expert resume parser. Extract information to populate JobSeeker and Candidate entities.

ALWAYS INFER these fields when not explicitly stated:
1. languages: ALWAYS include the resume's language (e.g., if resume is in English, add "English")
2. jobTitle: Use the most recent job title from experience
3. currentCompany: Company from job without end date OR most recent job
4. yearsOfExperience: Calculate from earliest job to present
5. preferredLocation: Default to current location if not specified
6. isRemoteOnly: true if last 2+ jobs were remote, false otherwise
7. availableFrom: Immediate (today) if unemployed, otherwise add 2-4 weeks
8. summary: Generate from experience if not provided
9. salary: Estimate based on role/location/experience if not mentioned
10. workAvailability: Infer notice period based on seniority

Resume content: [RESUME_CONTENT]

Return JSON with this structure:
{
  "personalInfo": {
    "fullName": "extract full name",
    "firstName": "first name",
    "lastName": "last name",
    "email": "email address or null",
    "phone": "phone number or null",
    "location": "city, country or best guess"
  },
  
  "professional": {
    "currentJobTitle": "most recent job title",
    "currentCompany": "current employer or most recent",
    "summary": "extract or generate professional summary",
    "yearsOfExperience": calculate as number,
    "isCurrentlyEmployed": boolean based on dates
  },
  
  "skills": ["extract ALL skills from entire document"],
  
  "experience": [
    {
      "title": "job title",
      "company": "company name",
      "location": "job location if mentioned",
      "startDate": "YYYY-MM-DD",
      "endDate": "YYYY-MM-DD or null if current",
      "description": "job description",
      "responsibilities": ["key responsibilities"],
      "achievements": ["achievements in role"]
    }
  ],
  
  "education": [
    {
      "degree": "degree name",
      "field": "field of study",
      "institution": "school name",
      "location": "school location",
      "startDate": "YYYY-MM-DD or null",
      "endDate": "YYYY-MM-DD or null",
      "gpa": number or null,
      "honors": ["honors/awards"]
    }
  ],
  
  "certifications": [
    {
      "name": "certification name",
      "issuer": "issuing organization",
      "issueDate": "YYYY-MM-DD or null",
      "expiryDate": "YYYY-MM-DD or null",
      "credentialId": "ID or null"
    }
  ],
  
  "languages": ["ALWAYS include resume language, plus any others mentioned"],
  
  "achievements": [
    {
      "title": "achievement name",
      "description": "details",
      "date": "YYYY-MM-DD or null",
      "issuer": "organization or null"
    }
  ],
  
  "urls": {
    "linkedin": "LinkedIn URL or null",
    "github": "GitHub URL or null",
    "portfolio": "Portfolio URL or null",
    "otherSocial": ["other social media URLs"]
  },
  
  "preferences": {
    "locations": ["preferred work locations - default to current"],
    "remoteOnly": boolean based on job history,
    "jobTypes": ["infer from experience: FULL_TIME, CONTRACT, etc"],
    "industries": ["infer from companies worked at"]
  },
  
  "availability": {
    "availableFrom": "YYYY-MM-DD based on employment status",
    "noticePeriod": "immediate or X weeks based on seniority"
  },
  
  "compensation": {
    "current": number or null,
    "expectedMin": number or estimate,
    "expectedMax": number or estimate,
    "currency": "USD/EUR/GBP etc based on location"
  }
}

INFERENCE RULES:
- If currently employed (job without end date): availableFrom = today + 2-4 weeks
- If unemployed (all jobs have end dates): availableFrom = today
- Always include resume's language in languages array
- If no summary, generate one from most recent experience
- Extract skills from job descriptions, not just skills section
- Infer remote preference from job location patterns
- Estimate salary if not mentioned based on title/location/experience
```

## Intelligent Field Mapping

### JobSeeker Entity Population

```typescript
function mapToJobSeeker(extracted: any): Partial<JobSeeker> {
  return {
    // Basic Info
    firstName: extracted.personalInfo.firstName,
    lastName: extracted.personalInfo.lastName,
    email: extracted.personalInfo.email,
    phone: extracted.personalInfo.phone,
    location: extracted.personalInfo.location,
    
    // Professional
    summary: extracted.professional.summary || generateSummary(extracted),
    skills: extracted.skills,
    experience: extracted.experience,
    
    // URLs
    linkedinUrl: extracted.urls.linkedin,
    githubUrl: extracted.urls.github,
    portfolioUrl: extracted.urls.portfolio,
    
    // Education & Certs
    education: extracted.education.map(edu => ({
      degree: edu.degree,
      field: edu.field,
      institution: edu.institution,
      location: edu.location,
      startDate: edu.startDate,
      endDate: edu.endDate,
      gpa: edu.gpa,
      honors: edu.honors
    })),
    
    certifications: extracted.certifications.map(cert => ({
      name: cert.name,
      issuer: cert.issuer,
      issueDate: cert.issueDate,
      expiryDate: cert.expiryDate,
      credentialId: cert.credentialId
    })),
    
    languages: extracted.languages, // ALWAYS populated with at least resume language
    
    // Preferences (Inferred)
    preferences: {
      jobTypes: extracted.preferences.jobTypes,
      locations: extracted.preferences.locations,
      remotePreference: extracted.preferences.remoteOnly ? 'REMOTE_ONLY' : 'FLEXIBLE',
      industries: extracted.preferences.industries,
      desiredSalary: {
        min: extracted.compensation.expectedMin,
        max: extracted.compensation.expectedMax,
        currency: extracted.compensation.currency,
        period: 'YEARLY'
      }
    },
    
    // Achievements
    achievements: extracted.achievements,
    
    // Work Availability (Inferred)
    workAvailability: {
      availableFrom: extracted.availability.availableFrom,
      noticePeriod: extracted.availability.noticePeriod,
      willingToRelocate: false, // Conservative default
      workAuthorization: inferWorkAuth(extracted)
    },
    
    // Compensation
    compensation: {
      currentSalary: extracted.compensation.current,
      expectedSalary: extracted.compensation.expectedMin,
      currency: extracted.compensation.currency
    },
    
    // Social Profiles
    socialProfiles: extracted.urls.otherSocial?.map(url => ({
      platform: detectPlatform(url),
      url: url
    }))
  };
}
```

### Candidate Entity Population

```typescript
function mapToCandidate(extracted: any, jobId: string): Partial<Candidate> {
  return {
    // Basic Info
    fullName: extracted.personalInfo.fullName,
    firstName: extracted.personalInfo.firstName,
    lastName: extracted.personalInfo.lastName,
    
    // Professional
    jobTitle: extracted.professional.currentJobTitle,
    location: extracted.personalInfo.location,
    summary: extracted.professional.summary,
    skills: extracted.skills,
    experience: extracted.experience,
    
    // Contact
    email: extracted.personalInfo.email,
    phone: extracted.personalInfo.phone,
    
    // URLs
    linkedinUrl: extracted.urls.linkedin,
    githubUrl: extracted.urls.github,
    profileUrl: '', // Will be set to resume URL
    
    // Current Employment
    currentCompany: extracted.professional.currentCompany,
    yearsOfExperience: extracted.professional.yearsOfExperience,
    
    // Preferences
    preferredLocation: extracted.preferences.locations[0] || extracted.personalInfo.location,
    isRemoteOnly: extracted.preferences.remoteOnly,
    
    // Availability
    availableFrom: new Date(extracted.availability.availableFrom),
    
    // Salary
    salary: {
      min: extracted.compensation.expectedMin,
      max: extracted.compensation.expectedMax,
      currency: extracted.compensation.currency
    },
    
    // Metadata
    extractionMetadata: [{
      filename: 'resume.pdf',
      extractedAt: new Date(),
      extractionSource: 'AI_EXTRACTION',
      rawExtractedData: extracted
    }],
    
    // Required fields
    jobId: jobId,
    source: 'RESUME_UPLOAD',
    status: CandidateStatus.NEW,
    contacted: false
  };
}
```

## Key Inference Examples

### 1. Language Auto-Population
```javascript
// ALWAYS populate languages
if (!languages || languages.length === 0) {
  languages = [];
}
// Add resume language
if (resumeIsInEnglish) {
  languages.push('English');
} else if (detectedLanguage) {
  languages.push(detectedLanguage);
  // If non-English resume has good English, add it too
  if (hasEnglishContent) {
    languages.push('English');
  }
}
```

### 2. Current Job Title & Company
```javascript
// Get from most recent experience
const currentJob = experience.find(job => !job.endDate) 
  || experience[0]; // Fallback to most recent

jobTitle = currentJob?.title || '';
currentCompany = currentJob?.company || '';
```

### 3. Years of Experience Calculation
```javascript
// Calculate from all experiences
let totalMonths = 0;
for (const job of experience) {
  if (job.startDate) {
    const start = new Date(job.startDate);
    const end = job.endDate ? new Date(job.endDate) : new Date();
    const months = (end.getFullYear() - start.getFullYear()) * 12 
      + (end.getMonth() - start.getMonth());
    totalMonths += Math.max(0, months);
  }
}
yearsOfExperience = Math.round(totalMonths / 12);
```

### 4. Availability Inference
```javascript
// Based on employment status
const isEmployed = experience.some(job => !job.endDate);
if (isEmployed) {
  // Add notice period based on seniority
  const weeks = yearsOfExperience >= 10 ? 4 : 2;
  availableFrom = addWeeks(new Date(), weeks);
  noticePeriod = `${weeks} weeks`;
} else {
  availableFrom = new Date(); // Immediate
  noticePeriod = 'Immediate';
}
```

### 5. Remote Preference Detection
```javascript
// Analyze recent jobs
const recentJobs = experience.slice(0, 3);
const remoteJobs = recentJobs.filter(job => 
  job.location?.toLowerCase().includes('remote')
);
isRemoteOnly = remoteJobs.length === recentJobs.length;
```

### 6. Summary Generation
```javascript
// Generate if not provided
if (!summary && experience.length > 0) {
  const recent = experience[0];
  summary = `${recent.title} with ${yearsOfExperience} years of experience. ` +
    `Most recently at ${recent.company}. ` +
    `Skilled in ${skills.slice(0, 3).join(', ')}.`;
}
```

### 7. Salary Estimation
```javascript
// Estimate based on role and location
if (!salary) {
  const baseSalary = getMarketRate(jobTitle, location);
  const multiplier = 1 + (yearsOfExperience * 0.05);
  salary = {
    min: baseSalary * multiplier * 0.9,
    max: baseSalary * multiplier * 1.1,
    currency: getCurrencyForLocation(location)
  };
}
```

## Services to Update

1. **groq.service.ts** - Lines 54-98: Update prompt and mapping
2. **resumeParser.ts** - Lines 47-75: Update prompt and field mapping
3. **resume-parser.service.ts** - Lines 386-422: Update fallback prompt
4. **multi-ai-resume-parser.service.ts** - Update conversion function
5. **bulk-talent-upload.service.ts** - Ensure it uses updated Groq service

## Important Notes

- DO NOT add any new fields to entities
- Focus on extracting/inferring data for EXISTING fields only
- Always populate languages with at least the resume's language
- Generate summaries when missing
- Calculate years of experience from work history
- Infer availability based on employment status
- Estimate salary when not provided
- Default preferred location to current location
- Extract skills from entire document, not just skills section