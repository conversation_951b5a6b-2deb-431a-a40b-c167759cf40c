{"version": "1.0.0", "name": "kaleido-backend", "scripts": {"build": "NODE_OPTIONS=\"--max-old-space-size=16384 --max-semi-space-size=2048\" nest build && ([ -z \"$SKIP_SENTRY_RELEASE\" ] && pnpm sentry:sourcemaps || echo \"Skipping Sentry sourcemap upload\")", "build:with-redis": "NODE_OPTIONS=\"--max-old-space-size=16384 --max-semi-space-size=2048\" nest build && chmod +x ./scripts/setup-redis-do.sh && ./scripts/setup-redis-do.sh && ([ -z \"$SKIP_SENTRY_RELEASE\" ] && pnpm sentry:sourcemaps || echo \"Skipping Sentry sourcemap upload\")", "format": "prettier --write \"src/**/*.ts\"", "start": "NODE_OPTIONS=\"--max-old-space-size=8192\" nest start", "dev": "NODE_OPTIONS=\"--max-old-space-size=8192\" nest start --watch --preserveWatchOutput", "dev:offline": "NODE_ENV=development AUTH_OFFLINE_DEV=true NODE_OPTIONS=\"--max-old-space-size=8192\" nest start --watch --preserveWatchOutput", "dev:memory": "NODE_OPTIONS=\"--max-old-space-size=16384 --max-semi-space-size=2048\" nest start --watch", "dev:tunnel": "NODE_OPTIONS=\"--max-old-space-size=8192\" ./start-dev.sh", "start:prod:tunnel": "NODE_OPTIONS=\"--max-old-space-size=8192\" ./start-dev.sh prod", "start:debug": "NODE_OPTIONS=\"--max-old-space-size=8192\" nest start --debug --watch --preserveWatchOutput", "start:prod": "NODE_OPTIONS=\"--max-old-space-size=8192\" node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "clean:logs": "node scripts/remove-console-logs-improved.js", "clean:logs:dry": "node scripts/remove-console-logs-improved.js --dry-run", "test": "NODE_OPTIONS=\"--max-old-space-size=8192\" jest", "test:watch": "NODE_OPTIONS=\"--max-old-space-size=8192\" jest --watch", "test:coverage": "NODE_OPTIONS=\"--max-old-space-size=8192\" jest --coverage", "test:debug": "NODE_OPTIONS=\"--max-old-space-size=8192\" node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "NODE_OPTIONS=\"--max-old-space-size=8192\" jest --config ./test/jest-e2e.json", "test:e2e:watch": "NODE_OPTIONS=\"--max-old-space-size=8192\" jest --config ./test/jest-e2e.json --watch", "test:integration": "NODE_OPTIONS=\"--max-old-space-size=8192\" jest --config ./test/jest-integration.json", "test:unit": "NODE_OPTIONS=\"--max-old-space-size=8192\" jest --testPathIgnorePatterns=.*\\.integration\\.spec\\.ts --testPathIgnorePatterns=.*\\.e2e-spec\\.ts", "test:ci": "NODE_OPTIONS=\"--max-old-space-size=8192\" jest --ci --coverage --watchAll=false", "test:ci:e2e": "NODE_OPTIONS=\"--max-old-space-size=8192\" jest --config ./test/jest-e2e.json --ci --watchAll=false", "test:dashboard": "NODE_OPTIONS=\"--max-old-space-size=8192\" jest --coverage --passWithNoTests", "test:dashboard:watch": "NODE_OPTIONS=\"--max-old-space-size=8192\" jest --coverage --passWithNoTests --watch", "test:dashboard:setup": "node scripts/setup-test-dashboard.js", "test:dashboard:sizes": "node scripts/check-file-sizes.js", "prepare": "husky install", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "NODE_OPTIONS=\"--max-old-space-size=8192\" ts-node scripts/generate-migration.ts", "migration:generate:enhanced": "NODE_OPTIONS=\"--max-old-space-size=8192\" ts-node scripts/generate-migration-enhanced.ts", "migration:troubleshoot": "NODE_OPTIONS=\"--max-old-space-size=8192\" ts-node scripts/migration-troubleshoot.ts", "migration:create": "NODE_OPTIONS=\"--max-old-space-size=8192\" pnpm run typeorm -- migration:create", "migration:show": "NODE_OPTIONS=\"--max-old-space-size=8192\" pnpm run typeorm -- migration:show -d src/config/migration.config.ts", "migration:revert": "NODE_OPTIONS=\"--max-old-space-size=8192\" pnpm run typeorm -- migration:revert -d src/config/migration.config.ts", "migration:run": "NODE_OPTIONS=\"--max-old-space-size=8192\" pnpm run typeorm -- migration:run -d src/config/migration.config.ts", "check:migrations": "NODE_OPTIONS=\"--max-old-space-size=8192\" ts-node scripts/check-migrations.ts", "check:migrations:ci": "CI=true NODE_OPTIONS=\"--max-old-space-size=8192\" ts-node scripts/check-migrations.ts --force", "entities:update": "NODE_OPTIONS=\"--max-old-space-size=8192\" ts-node scripts/update-entity-exports.ts", "db:sync:check": "ts-node scripts/database-sync-check.ts", "db:sync:check:prod": "NODE_ENV=production ts-node -r tsconfig-paths/register scripts/database-sync-check.ts --check-data", "db:safe:sync": "ts-node scripts/database-safe-sync.ts", "db:safe:sync:prod": "NODE_ENV=production ts-node scripts/database-safe-sync.ts --execute --force", "migration:run:safe": "ts-node scripts/database-migration-runner.ts", "migration:run:prod": "NODE_ENV=production ts-node scripts/database-migration-runner.ts", "deploy:safe": "ts-node scripts/deploy-with-db-safety.ts", "deploy:safe:prod": "NODE_ENV=production ts-node scripts/deploy-with-db-safety.ts", "sentry:sourcemaps": "sentry-cli releases new --org kaleido-talent --project kaleido-backend $(node -e \"console.log(require('./package.json').version)\") && sentry-cli sourcemaps inject --org kaleido-talent --project kaleido-backend dist && sentry-cli sourcemaps upload --org kaleido-talent --project kaleido-backend --url-prefix '~/' dist && sentry-cli releases finalize --org kaleido-talent --project kaleido-backend $(node -e \"console.log(require('./package.json').version)\")", "setup:redis": "chmod +x ./scripts/setup-redis-do.sh && ./scripts/setup-redis-do.sh", "setup:redis:docker": "chmod +x ./scripts/setup-redis-docker.sh && ./scripts/setup-redis-docker.sh", "redis:status:docker": "chmod +x ./scripts/check-redis-docker.sh && ./scripts/check-redis-docker.sh", "redis:test": "node ./scripts/test-redis-connection.js", "script:update-linkedin-images": "NODE_OPTIONS=\"--max-old-space-size=8192\" ts-node scripts/update-linkedin-profile-images-standalone.ts", "script:update-linkedin-images:browser": "NODE_OPTIONS=\"--max-old-space-size=8192\" ts-node scripts/update-linkedin-profile-images-browser.ts", "redis:cleanup": "rm -f ./setup-pnpm-only.sh ./setup-pnpm-redis.sh ./setup-pnpm-redis-debug.sh ./setup-redis-binary.sh ./install-redis-server.sh ./scripts/setup-redis-simple.sh", "docker:build": "docker build -t kaleido-backend:latest .", "docker:build:prod": "docker build -t kaleido-backend:prod -f Dockerfile.prod .", "docker:build:safe": "chmod +x ./scripts/docker-build-safe.sh && ./scripts/docker-build-safe.sh", "docker:run": "docker run -p 8080:8080 --env-file .env kaleido-backend:latest", "docker:run:prod": "docker run -p 8080:8080 --env-file .env kaleido-backend:prod", "docker:compose": "docker-compose up -d", "docker:compose:prod": "docker-compose -f docker-compose.prod-safe.yml up -d", "docker:compose:internal-redis": "docker-compose -f docker-compose.internal-redis.yml up -d", "validate:migrations": "node scripts/validate-migrations.js", "deploy": "pnpm validate:migrations && pnpm build && NODE_OPTIONS=\"--max-old-space-size=8192\" pnpm start:prod", "deploy:production": "NODE_ENV=production pnpm run deploy:safe:prod", "do:start": "node scripts/do-app-start.js", "do:check": "node scripts/do-migration-check.js", "do:test": "DO_APP_PLATFORM=true npm run do:start", "predeploy": "chmod +x ./scripts/setup-redis-do.sh && ./scripts/setup-redis-do.sh", "typecheck": "tsc --noEmit", "typecheck:watch": "tsc --noEmit --watch", "prebuild:disabled": "pnpm typecheck"}, "dependencies": {"@auth0/nextjs-auth0": "^4.0.1", "@aws-sdk/client-s3": "^3.842.0", "@fastify/compress": "^8.1.0", "@fastify/cors": "^11.1.0", "@fastify/helmet": "^13.0.1", "@fastify/multipart": "^9.0.3", "@fastify/static": "^8.2.0", "@nestjs/axios": "^4.0.1", "@nestjs/bull": "11.0.3", "@nestjs/cli": "^11.0.10", "@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.5", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.5", "@nestjs/platform-fastify": "^11.1.5", "@nestjs/swagger": "^11.2.0", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^11.0.0", "@react-email/components": "^0.0.33", "@react-email/render": "^1.0.5", "@sentry/cli": "^2.43.1", "@sentry/nestjs": "^9.15.0", "@sentry/node": "^9.15.0", "@sentry/profiling-node": "^9.15.0", "@types/cheerio": "^1.0.0", "@types/dotenv": "^8.2.3", "@types/fluent-ffmpeg": "^2.1.27", "@types/react": "^19.0.8", "auth0": "^4.26.0", "axios": "^1.7.9", "bottleneck": "^2.19.5", "boxen": "^5.1.2", "bull": "^4.16.5", "chalk": "^4.1.2", "cheerio": "^1.1.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cli-table3": "^0.6.5", "csv-parser": "^3.2.0", "date-fns": "4.1.0", "dotenv": "^16.4.7", "express-jwt": "^8.4.1", "express-oauth2-jwt-bearer": "^1.6.0", "fastify": "^5.4.0", "ffmpeg-static": "^5.2.0", "ffprobe-static": "^3.1.0", "figlet": "^1.8.2", "fluent-ffmpeg": "^2.1.3", "googleapis": "^150.0.1", "gradient-string": "^2.0.2", "groq-sdk": "^0.26.0", "ioredis": "^5.3.2", "json5": "^2.2.3", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "limiter": "^2.1.0", "mailparser": "^3.7.2", "mammoth": "^1.8.0", "mermaid": "^11.5.0", "multer": "1.4.5-lts.1", "nanoid": "^5.1.5", "next-auth": "^4.24.11", "openai": "^4.28.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdf.js-extract": "^0.2.1", "pdfkit": "^0.15.1", "pg": "^8.13.1", "puppeteer": "^24.15.0", "qrcode": "^1.5.4", "react": "^19.0.0", "readline": "^1.3.0", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "resend": "^4.1.2", "rxjs": "^7.8.2", "sanitize-html": "^2.13.1", "stream": "^0.0.3", "string-sanitizer": "^2.0.2", "string-similarity": "^4.0.4", "stripe": "^18.1.0", "ts-loader": "^9.5.1", "unzipper": "^0.10.14", "uuid": "^11.1.0", "word-extractor": "^1.0.4", "zustand": "^5.0.3"}, "devDependencies": {"@nestjs/testing": "^11.1.5", "@types/express": "^4.17.21", "@types/express-jwt": "^7.4.2", "@types/express-serve-static-core": "^4.17.41", "@types/figlet": "^1.7.0", "@types/glob": "^9.0.0", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.10", "@types/mailparser": "^3.4.4", "@types/multer": "^1.4.12", "@types/node": "^20.17.10", "@types/passport-jwt": "^4.0.1", "@types/pdf-parse": "^1.1.4", "@types/pdfkit": "^0.13.7", "@types/pg": "^8.11.11", "@types/qrcode": "^1.5.5", "@types/sanitize-html": "^2.9.5", "@types/string-similarity": "^4.0.2", "@types/supertest": "^6.0.2", "@types/unzipper": "^0.10.9", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "glob": "^11.0.3", "husky": "^8.0.0", "jest": "^29.7.0", "lint-staged": "^15.0.0", "prettier": "^3.3.3", "run-script-webpack-plugin": "^0.2.3", "sqlite3": "^5.1.7", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typeorm": "^0.3.20", "typescript": "~5.7.3", "webpack": "^5.101.0", "webpack-cli": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "engines": {"node": ">=18.19.0"}, "packageManager": "pnpm@10.14.0+sha512.ad27a79641b49c3e481a16a805baa71817a04bbe06a38d17e60e2eaee83f6a146c6a688125f5792e48dd5ba30e7da52a5cda4c3992b9ccf333f9ce223af84748"}