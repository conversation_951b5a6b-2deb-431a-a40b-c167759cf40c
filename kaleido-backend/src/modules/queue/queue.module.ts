import { OpenaiModule } from '@/shared/modules/openai.module';
import { QueueConfigModule } from '@/shared/modules/queue-config.module';
import { RedisModule } from '@/shared/modules/redis.module';
import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AtsModule } from '../ats/ats.module';
import { CandidateModule } from '../candidate/candidate.module';
import { CandidateEvaluation } from '../candidate/entities/candidate-evaluation.entity';
import { Candidate } from '../candidate/entities/candidate.entity';
import { CareerInsight } from '../career-insights/entities/career-insight.entity';
import { Job } from '../job/entities/job.entity';
import { SubscriptionModule } from '../subscription/subscription.module';
import { CandidateComparison } from '../comparison/entities/candidate-comparison.entity';
import { CandidateComparisonModule } from '../comparison/candidate-comparison.module';
import { AtsCandidateFetchProcessor } from './processors/ats-candidate-fetch.processor';
import { CareerInsightsProcessor } from './processors/career-insights.processor';
import { MatchRankProcessor } from './processors/match-rank.processor';
import { CandidateComparisonProcessor } from './processors/candidate-comparison.processor';
import { TalentUploadProcessor } from './processors/talent-upload.processor';
import { QueueService } from './queue.service';

@Module({
  imports: [
    RedisModule,
    forwardRef(() => QueueConfigModule), // Use centralized queue configuration with forwardRef to avoid circular dependency
    forwardRef(() => AtsModule),
    forwardRef(() => SubscriptionModule), // Add subscription module with forwardRef to handle circular dependency
    forwardRef(() => CandidateComparisonModule), // Add comparison module with forwardRef
    forwardRef(() => CandidateModule), // Add candidate module for BulkTalentUploadService
    TypeOrmModule.forFeature([
      Candidate,
      CandidateEvaluation,
      Job,
      CareerInsight,
      CandidateComparison,
    ]),
    OpenaiModule,
  ],
  providers: [
    QueueService,
    AtsCandidateFetchProcessor,
    MatchRankProcessor,
    CareerInsightsProcessor,
    CandidateComparisonProcessor,
    TalentUploadProcessor,
  ],
  exports: [QueueService],
})
export class QueueModule {}
