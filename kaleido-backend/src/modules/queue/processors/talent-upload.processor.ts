import { Job } from 'bull';

import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { QUEUE_NAMES } from '@/shared/constants/queue.constants';
import { BulkTalentUploadService } from '../../candidate/services/bulk-talent-upload.service';

interface TalentUploadJobData {
  file: {
    buffer: Buffer;
    originalname: string;
    mimetype: string;
    size: number;
  };
  clientId: string;
  jobId?: string;
}

interface TalentUploadJobResult {
  status: string;
  successCount: number;
  errorCount: number;
  duplicatesSkipped: number;
  errors?: Array<{
    filename: string;
    error: string;
  }>;
  processedCandidates?: Array<{
    id: string;
    name: string;
    email: string;
    status: string;
  }>;
  message?: string;
}

@Injectable()
@Processor(QUEUE_NAMES.FILE_UPLOAD)
export class TalentUploadProcessor {
  private readonly logger = new Logger(TalentUploadProcessor.name);

  constructor(private readonly bulkTalentUploadService: BulkTalentUploadService) {}

  @Process('process-resume')
  async processResume(job: Job<TalentUploadJobData>): Promise<TalentUploadJobResult> {
    const { file, clientId, jobId } = job.data;

    this.logger.log(`Processing resume file: ${file.originalname} for client: ${clientId}`);

    try {
      // Update job progress to 10%
      await job.progress(10);

      // Convert buffer back to Express.Multer.File format
      const multerFile: Express.Multer.File = {
        fieldname: 'files',
        originalname: file.originalname,
        encoding: '7bit',
        mimetype: file.mimetype,
        buffer: Buffer.from(file.buffer),
        size: file.size,
        stream: null as any,
        destination: '',
        filename: '',
        path: '',
      };

      // Update progress to 30%
      await job.progress(30);

      // Process the file using the direct processing method (no queuing)
      const result = await this.bulkTalentUploadService.processFilesDirectly(
        [multerFile],
        clientId,
        jobId,
      );

      // Update progress to 90%
      await job.progress(90);

      // Transform result to match expected format
      const jobResult: TalentUploadJobResult = {
        status: result.errorCount > 0 ? 'completed_with_errors' : 'completed',
        successCount: result.successCount,
        errorCount: result.errorCount,
        duplicatesSkipped: result.duplicatesSkipped || 0,
        errors: result.errors,
        processedCandidates: result.processedCandidates?.map((candidate: any) => ({
          id: candidate.id,
          name: candidate.fullName || `${candidate.firstName} ${candidate.lastName}`,
          email: candidate.email,
          status: 'processed',
        })),
        message: `Successfully processed ${result.successCount} candidate(s)`,
      };

      // Update progress to 100%
      await job.progress(100);

      this.logger.log(
        `Completed processing resume file: ${file.originalname}, success: ${result.successCount}, errors: ${result.errorCount}`,
      );

      return jobResult;
    } catch (error) {
      this.logger.error(`Error processing resume file ${file.originalname}:`, error);

      // Return error result
      return {
        status: 'failed',
        successCount: 0,
        errorCount: 1,
        duplicatesSkipped: 0,
        errors: [
          {
            filename: file.originalname,
            error: error instanceof Error ? error.message : 'Unknown error occurred',
          },
        ],
        message: `Failed to process file: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  @Process('process-batch')
  async processBatch(
    job: Job<{ files: TalentUploadJobData['file'][]; clientId: string; jobId?: string }>,
  ): Promise<TalentUploadJobResult> {
    const { files, clientId, jobId } = job.data;

    this.logger.log(`Processing batch of ${files.length} files for client: ${clientId}`);

    let totalSuccess = 0;
    let totalErrors = 0;
    let totalDuplicates = 0;
    const allErrors: Array<{ filename: string; error: string }> = [];
    const allProcessedCandidates: Array<{
      id: string;
      name: string;
      email: string;
      status: string;
    }> = [];

    try {
      // Update initial progress
      await job.progress(10);

      // Convert all buffers back to Express.Multer.File format
      const multerFiles: Express.Multer.File[] = files.map((file) => ({
        fieldname: 'files',
        originalname: file.originalname,
        encoding: '7bit',
        mimetype: file.mimetype,
        buffer: Buffer.from(file.buffer),
        size: file.size,
        stream: null as any,
        destination: '',
        filename: '',
        path: '',
      }));

      // Process files in batches to avoid memory issues
      const batchSize = 5;
      for (let i = 0; i < multerFiles.length; i += batchSize) {
        const batch = multerFiles.slice(i, i + batchSize);

        // Calculate progress
        const progressPercentage = 10 + (i / multerFiles.length) * 80;
        await job.progress(Math.round(progressPercentage));

        const result = await this.bulkTalentUploadService.processFilesDirectly(
          batch,
          clientId,
          jobId,
        );

        totalSuccess += result.successCount;
        totalErrors += result.errorCount;
        totalDuplicates += result.duplicatesSkipped || 0;

        if (result.errors) {
          allErrors.push(...result.errors);
        }

        if (result.processedCandidates) {
          allProcessedCandidates.push(
            ...result.processedCandidates.map((candidate: any) => ({
              id: candidate.id,
              name: candidate.fullName || `${candidate.firstName} ${candidate.lastName}`,
              email: candidate.email,
              status: 'processed',
            })),
          );
        }
      }

      // Update progress to 100%
      await job.progress(100);

      const jobResult: TalentUploadJobResult = {
        status: totalErrors > 0 ? 'completed_with_errors' : 'completed',
        successCount: totalSuccess,
        errorCount: totalErrors,
        duplicatesSkipped: totalDuplicates,
        errors: allErrors.length > 0 ? allErrors : undefined,
        processedCandidates: allProcessedCandidates,
        message: `Processed ${totalSuccess} candidate(s) from ${files.length} file(s)`,
      };

      this.logger.log(
        `Completed batch processing: ${totalSuccess} success, ${totalErrors} errors, ${totalDuplicates} duplicates`,
      );

      return jobResult;
    } catch (error) {
      this.logger.error('Error processing batch:', error);

      return {
        status: 'failed',
        successCount: totalSuccess,
        errorCount: files.length,
        duplicatesSkipped: totalDuplicates,
        errors: [
          {
            filename: 'batch',
            error: error instanceof Error ? error.message : 'Batch processing failed',
          },
        ],
        message: `Failed to process batch: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }
}
