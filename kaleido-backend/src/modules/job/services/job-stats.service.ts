import { endOfDay, format, startOfDay, subDays, subMonths, subWeeks, subYears } from 'date-fns';
import { Between, Repository } from 'typeorm';

import { JobStatus } from '@/shared/types/job.types';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Candidate } from '../../candidate/entities/candidate.entity';
import { VideoJD } from '../../video-jd/entities/video-jd.entity';
import { Job } from '../entities/job.entity';
import { JobService } from '../job.service';

@Injectable()
export class JobStatsService {
  constructor(
    @InjectRepository(Job)
    private jobRepository: Repository<Job>,
    @InjectRepository(VideoJD)
    private videoJDRepository: Repository<VideoJD>,
    @InjectRepository(Candidate)
    private candidateRepository: Repository<Candidate>,
    private readonly jobService: JobService,
  ) {}

  async getJobStats(userId: string, timeFrame: 'daily' | 'weekly' | 'monthly' | 'yearly') {
    // Calculate date range based on time frame
    const dateRange = this.getDateRangeFromTimeFrame(timeFrame);

    // Get total jobs count
    const totalJobs = await this.jobRepository.count({
      where: {
        clientId: userId,
        createdAt: Between(dateRange.startDate, dateRange.endDate),
      },
    });

    // Get jobs by status
    const jobsByStatus = await this.getJobsByStatus(userId, dateRange);

    // Get jobs with video JDs
    const jobsWithVideoJDs = await this.getJobsWithVideoJDs(userId, dateRange);

    // Get jobs with candidates
    const jobsWithCandidates = await this.getJobsWithCandidates(userId, dateRange);

    // Get department distribution
    const departmentDistribution = await this.getDepartmentDistribution(userId, dateRange);

    return {
      totalJobs,
      jobsByStatus,
      jobsWithVideoJDs,
      jobsWithCandidates,
      departmentDistribution,
      timeFrame,
    };
  }

  async getJobTrends(userId: string, timeFrame: 'daily' | 'weekly' | 'monthly' | 'yearly') {
    const dateRange = this.getDateRangeFromTimeFrame(timeFrame);

    // Get job creation trends
    const jobCreationTrends = await this.getJobCreationTrends(userId, timeFrame, dateRange);

    // Get candidate addition trends
    const candidateAdditionTrends = await this.getCandidateAdditionTrends(
      userId,
      timeFrame,
      dateRange,
    );

    return {
      jobCreationTrends,
      candidateAdditionTrends,
      timeFrame,
    };
  }

  private getDateRangeFromTimeFrame(timeFrame: 'daily' | 'weekly' | 'monthly' | 'yearly') {
    const now = new Date();
    let startDate: Date;

    switch (timeFrame) {
      case 'daily':
        startDate = startOfDay(subDays(now, 1));
        break;
      case 'weekly':
        startDate = startOfDay(subWeeks(now, 1));
        break;
      case 'monthly':
        startDate = startOfDay(subMonths(now, 1));
        break;
      case 'yearly':
        startDate = startOfDay(subYears(now, 1));
        break;
      default:
        startDate = startOfDay(subWeeks(now, 1));
    }

    return {
      startDate,
      endDate: endOfDay(now),
    };
  }

  private async getJobsByStatus(userId: string, dateRange: { startDate: Date; endDate: Date }) {
    const jobStatuses = Object.values(JobStatus);
    const result: Record<JobStatus, number> = {} as Record<JobStatus, number>;

    // Get counts for each status
    for (const status of jobStatuses) {
      const count = await this.jobRepository.count({
        where: {
          clientId: userId,
          status,
          createdAt: Between(dateRange.startDate, dateRange.endDate),
        },
      });
      result[status] = count;
    }

    return result;
  }

  private async getJobsWithVideoJDs(userId: string, dateRange: { startDate: Date; endDate: Date }) {
    // Get jobs with video JDs
    const jobsWithVideoJDs = await this.videoJDRepository
      .createQueryBuilder('videoJD')
      .innerJoin('videoJD.job', 'job')
      .where('job.clientId = :userId', { userId })
      .andWhere('job.createdAt BETWEEN :startDate AND :endDate', {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
      })
      .select('COUNT(DISTINCT job.id)', 'count')
      .getRawOne();

    return jobsWithVideoJDs?.count || 0;
  }

  private async getJobsWithCandidates(
    userId: string,
    dateRange: { startDate: Date; endDate: Date },
  ) {
    // Get jobs with candidates
    const jobsWithCandidates = await this.candidateRepository
      .createQueryBuilder('candidate')
      .innerJoin('candidate.job', 'job')
      .where('job.clientId = :userId', { userId })
      .andWhere('job.createdAt BETWEEN :startDate AND :endDate', {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
      })
      .select('COUNT(DISTINCT job.id)', 'count')
      .getRawOne();

    return jobsWithCandidates?.count || 0;
  }

  private async getDepartmentDistribution(
    userId: string,
    dateRange: { startDate: Date; endDate: Date },
  ) {
    // Get department distribution
    const departmentDistribution = await this.jobRepository
      .createQueryBuilder('job')
      .select('job.department', 'name')
      .addSelect('COUNT(job.id)', 'value')
      .where('job.clientId = :userId', { userId })
      .andWhere('job.createdAt BETWEEN :startDate AND :endDate', {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
      })
      .groupBy('job.department')
      .getRawMany();

    return departmentDistribution.map((item) => ({
      name: item.name || 'Unspecified',
      value: parseInt(item.value, 10),
    }));
  }

  private async getJobCreationTrends(
    userId: string,
    timeFrame: 'daily' | 'weekly' | 'monthly' | 'yearly',
    dateRange: { startDate: Date; endDate: Date },
  ) {
    let dateFormat: string;
    let groupBy: string;

    switch (timeFrame) {
      case 'daily':
        dateFormat = 'HH:00';
        groupBy = 'HOUR';
        break;
      case 'weekly':
        dateFormat = 'EEE';
        groupBy = 'DAY';
        break;
      case 'monthly':
        dateFormat = 'dd MMM';
        groupBy = 'DAY';
        break;
      case 'yearly':
        dateFormat = 'MMM';
        groupBy = 'MONTH';
        break;
      default:
        dateFormat = 'EEE';
        groupBy = 'DAY';
    }

    let query = this.jobRepository
      .createQueryBuilder('job')
      .where('job.clientId = :userId', { userId })
      .andWhere('job.createdAt BETWEEN :startDate AND :endDate', {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
      });

    if (groupBy === 'HOUR') {
      query = query
        .select(`DATE_TRUNC('hour', job.createdAt)`, 'date')
        .addSelect('COUNT(job.id)', 'count')
        .groupBy(`DATE_TRUNC('hour', job.createdAt)`)
        .orderBy(`DATE_TRUNC('hour', job.createdAt)`);
    } else if (groupBy === 'DAY') {
      query = query
        .select(`DATE_TRUNC('day', job.createdAt)`, 'date')
        .addSelect('COUNT(job.id)', 'count')
        .groupBy(`DATE_TRUNC('day', job.createdAt)`)
        .orderBy(`DATE_TRUNC('day', job.createdAt)`);
    } else if (groupBy === 'MONTH') {
      query = query
        .select(`DATE_TRUNC('month', job.createdAt)`, 'date')
        .addSelect('COUNT(job.id)', 'count')
        .groupBy(`DATE_TRUNC('month', job.createdAt)`)
        .orderBy(`DATE_TRUNC('month', job.createdAt)`);
    }

    const results = await query.getRawMany();

    return results.map((item) => ({
      name: format(new Date(item.date), dateFormat),
      value: parseInt(item.count, 10),
    }));
  }

  private async getCandidateAdditionTrends(
    userId: string,
    timeFrame: 'daily' | 'weekly' | 'monthly' | 'yearly',
    dateRange: { startDate: Date; endDate: Date },
  ) {
    let dateFormat: string;
    let groupBy: string;

    switch (timeFrame) {
      case 'daily':
        dateFormat = 'HH:00';
        groupBy = 'HOUR';
        break;
      case 'weekly':
        dateFormat = 'EEE';
        groupBy = 'DAY';
        break;
      case 'monthly':
        dateFormat = 'dd MMM';
        groupBy = 'DAY';
        break;
      case 'yearly':
        dateFormat = 'MMM';
        groupBy = 'MONTH';
        break;
      default:
        dateFormat = 'EEE';
        groupBy = 'DAY';
    }

    let query = this.candidateRepository
      .createQueryBuilder('candidate')
      .innerJoin('candidate.job', 'job')
      .where('job.clientId = :userId', { userId })
      .andWhere('candidate.createdAt BETWEEN :startDate AND :endDate', {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
      });

    if (groupBy === 'HOUR') {
      query = query
        .select(`DATE_TRUNC('hour', candidate.createdAt)`, 'date')
        .addSelect('COUNT(candidate.id)', 'count')
        .groupBy(`DATE_TRUNC('hour', candidate.createdAt)`)
        .orderBy(`DATE_TRUNC('hour', candidate.createdAt)`);
    } else if (groupBy === 'DAY') {
      query = query
        .select(`DATE_TRUNC('day', candidate.createdAt)`, 'date')
        .addSelect('COUNT(candidate.id)', 'count')
        .groupBy(`DATE_TRUNC('day', candidate.createdAt)`)
        .orderBy(`DATE_TRUNC('day', candidate.createdAt)`);
    } else if (groupBy === 'MONTH') {
      query = query
        .select(`DATE_TRUNC('month', candidate.createdAt)`, 'date')
        .addSelect('COUNT(candidate.id)', 'count')
        .groupBy(`DATE_TRUNC('month', candidate.createdAt)`)
        .orderBy(`DATE_TRUNC('month', candidate.createdAt)`);
    }

    const results = await query.getRawMany();

    return results.map((item) => ({
      name: format(new Date(item.date), dateFormat),
      value: parseInt(item.count, 10),
    }));
  }

  async getRankedJobsStats(userId: string, _page: number = 1, _limit: number = 10) {
    const jobsResponse = await this.jobService.findByClientId(userId, [
      'candidates',
      'candidateEvaluations',
    ]);
    const allJobs = jobsResponse.data;

    // Initialize statistics object
    const stats: any = {
      totalJobs: allJobs.length,
      totalCandidates: 0,
      averageMatchScore: 0,
      jobsWithCandidates: 0,
      topTierCandidates: 0,
      secondTierCandidates: 0,
      jobsByDepartment: {},
      candidatesByMatchScore: {
        excellent: 0,
        veryGood: 0,
        good: 0,
        fair: 0,
        poor: 0,
      },
      recentActivity: {
        newJobsLast7Days: 0,
        newJobsLast30Days: 0,
        newCandidatesLast7Days: 0,
        newCandidatesLast30Days: 0,
      },
    };

    let totalMatchScore = 0;
    let totalScorableCandidates = 0;
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime());
    sevenDaysAgo.setDate(now.getDate() - 7);
    const thirtyDaysAgo = new Date(now.getTime());
    thirtyDaysAgo.setDate(now.getDate() - 30);

    // Calculate job creation dates
    stats.recentActivity.newJobsLast7Days = allJobs.filter((job) => {
      if (!job.createdAt) return false;
      const createdAt = job.createdAt instanceof Date ? job.createdAt : new Date(job.createdAt);
      return createdAt >= sevenDaysAgo;
    }).length;

    stats.recentActivity.newJobsLast30Days = allJobs.filter((job) => {
      if (!job.createdAt) return false;
      const createdAt = job.createdAt instanceof Date ? job.createdAt : new Date(job.createdAt);
      return createdAt >= thirtyDaysAgo;
    }).length;

    // Process each job
    for (const job of allJobs) {
      // Count jobs by department
      if (job.department) {
        stats.jobsByDepartment[job.department] = (stats.jobsByDepartment[job.department] || 0) + 1;
      }

      // Process candidates
      if (job.candidates && job.candidates.length > 0) {
        stats.jobsWithCandidates++;
        stats.totalCandidates += job.candidates.length;

        let recentCandidates7Days = 0;
        let recentCandidates30Days = 0;

        // Process each candidate
        job.candidates.forEach((candidate) => {
          // Check if candidate has an evaluation with a match score
          if (candidate.evaluation && typeof candidate.evaluation.matchScore === 'number') {
            // Normalize the score to ensure it's between 0-100
            const rawScore = candidate.evaluation.matchScore;
            // If the score is already in the 0-100 range, use it directly
            // If it's a decimal between 0-1, multiply by 100 to get percentage
            const score = rawScore > 1 ? Math.min(100, rawScore) : Math.min(100, rawScore * 100);

            totalMatchScore += score;
            totalScorableCandidates++;

            // Categorize by score
            if (score >= 90) {
              stats.candidatesByMatchScore.excellent++;
            } else if (score >= 80) {
              stats.candidatesByMatchScore.veryGood++;
            } else if (score >= 70) {
              stats.candidatesByMatchScore.good++;
              stats.topTierCandidates++;
            } else if (score >= 50) {
              stats.candidatesByMatchScore.fair++;
              stats.secondTierCandidates++;
            } else {
              stats.candidatesByMatchScore.poor++;
            }
          }

          // Check when candidate was added
          if (candidate.createdAt) {
            const candidateCreatedAt =
              candidate.createdAt instanceof Date
                ? candidate.createdAt
                : new Date(candidate.createdAt);
            if (candidateCreatedAt >= sevenDaysAgo) {
              recentCandidates7Days++;
            }
            if (candidateCreatedAt >= thirtyDaysAgo) {
              recentCandidates30Days++;
            }
          }
        });

        stats.recentActivity.newCandidatesLast7Days += recentCandidates7Days;
        stats.recentActivity.newCandidatesLast30Days += recentCandidates30Days;
      }
    }

    // Calculate average match score
    stats.averageMatchScore =
      totalScorableCandidates > 0
        ? Number((totalMatchScore / totalScorableCandidates).toFixed(1))
        : 0;

    return stats;
  }
}
