import { Injectable, Logger, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Job } from '../entities/job.entity';
import { JobService } from '../job.service';
import { JobSeekerService } from '../../job-seeker/job-seeker.service';
import { JobMatchingService } from '../job-matching.service';
import { JobApplication } from '../../job-seeker/entities/job-application.entity';
import { JobsResponse, PublicJobData } from '../jobs.types';

type EnhancedJobType = PublicJobData & {
  hasApplied: boolean;
  matchPercentage?: number;
  matchDetails?: any;
  jobComplete: boolean;
  graduateSuitable: boolean;
  criteriaChanged: boolean;
};

@Injectable()
export class JobSeekerJobsService {
  private readonly logger = new Logger(JobSeekerJobsService.name);

  constructor(
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    @InjectRepository(JobApplication)
    private readonly jobApplicationRepository: Repository<JobApplication>,
    private readonly jobService: JobService,
    private readonly jobSeekerService: JobSeekerService,
    private readonly jobMatchingService: JobMatchingService,
  ) {}

  /**
   * Search published jobs for job seeker or employer
   */
  async searchJobsForJobSeeker(
    authUser: any,
    searchParams: {
      all?: boolean;
      applied?: string;
      postedByMe?: string;
      page?: number;
      limit?: number;
      search?: string;
      filters?: Record<string, string>;
    },
  ) {
    const {
      all,
      applied,
      postedByMe: _postedByMe,
      page = 1,
      limit = 50,
      search,
      filters,
    } = searchParams;

    // For employers, always set postedByMe to true
    if (authUser.roles.includes('employer')) {
      this.logger.log('User is an employer, forcing postedByMe=true');
    }

    try {
      let jobs;

      // If user is an employer, get only jobs posted by this employer
      if (authUser.roles.includes('employer')) {
        this.logger.log('Employer is viewing their own posted jobs');
        jobs = await this.jobService.findByClientId(authUser.userId, ['videoJDs'], true);
        this.logger.log(`Found ${jobs.data.length} published jobs for employer ${authUser.userId}`);
      } else if (authUser.roles.includes('graduate')) {
        jobs = await this.jobService.getJobsForGraduates(authUser.userId);
      } else {
        jobs = await this.jobService.getJobsForJobSeeker(authUser.userId, all);
      }

      const applications = await this.jobSeekerService.getApplications(authUser.userId);
      const appliedJobIds = new Set(
        [...applications.active, ...applications.withdrawn].map((app) => app.jobId),
      );

      // Get the job seeker to calculate match scores
      const jobSeeker = await this.jobSeekerService.getByUserId(authUser.userId);

      // Enhance jobs with application status and calculate match scores
      let enhancedJobs = await this.enhanceJobsWithMatchScores(jobs, appliedJobIds, jobSeeker, all);

      // Apply filters
      enhancedJobs = this.applyFilters(enhancedJobs, {
        applied,
        search,
        filters,
      });

      // Apply pagination
      return this.paginateResults(enhancedJobs, page, limit);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Error getting jobs for job seeker: ${errorMessage}`, errorStack);
      throw new InternalServerErrorException('Failed to get jobs for job seeker');
    }
  }

  /**
   * Get all job applications for a job seeker
   */
  async getJobSeekerApplications(userId: string) {
    return this.jobService.getJobSeekerApplications(userId);
  }

  /**
   * Get job application status for the current job seeker
   */
  async getJobApplicationStatus(jobId: string, userId: string) {
    return this.jobService.getJobApplicationStatusForJobSeeker(jobId, userId);
  }

  /**
   * Enhance jobs with match scores and application status
   */
  private async enhanceJobsWithMatchScores(
    jobs: JobsResponse,
    appliedJobIds: Set<string>,
    jobSeeker: any,
    all?: boolean,
  ): Promise<JobsResponse> {
    const enhancedData = await Promise.all(
      jobs.data.map(async (job) => {
        const hasApplied = job.id ? appliedJobIds.has(job.id) : false;
        const enhancedJob = {
          ...job,
          hasApplied,
          jobComplete: job.status === 'OPEN',
          graduateSuitable: job.isGraduateRole || false,
          criteriaChanged: false,
        } as EnhancedJobType;

        // Only calculate match scores for relevant jobs
        const isRelevant =
          hasApplied || all || jobSeeker?.preferences?.jobTypes?.includes(job.jobType);

        if (isRelevant && jobSeeker && job.id) {
          try {
            const matchScore = await this.jobMatchingService.calculateMatchPercentage(
              job.id,
              jobSeeker.id,
            );

            if (matchScore > 0) {
              enhancedJob.matchPercentage = matchScore;

              // Get match details from job metrics if available
              if (job.metrics?.matchDetails && job.metrics.matchDetails[jobSeeker.id]) {
                enhancedJob.matchDetails = job.metrics.matchDetails[jobSeeker.id];
              }
            }
          } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.warn(
              `Failed to calculate match score for job ${job.id} and job seeker ${jobSeeker.id}: ${errorMessage}`,
            );
          }
        }

        return enhancedJob;
      }),
    );

    return {
      ...jobs,
      data: enhancedData as any,
    };
  }

  /**
   * Apply filters to the job list
   */
  private applyFilters(
    jobs: JobsResponse,
    options: {
      applied?: string;
      search?: string;
      filters?: Record<string, string>;
    },
  ): JobsResponse {
    let filteredData = jobs.data as any[];

    // Filter by application status
    if (options.applied === 'false') {
      filteredData = filteredData.filter((job) => !job.hasApplied);
    } else if (options.applied === 'true') {
      filteredData = filteredData.filter((job) => job.hasApplied);
    }

    // Apply search filter
    if (options.search) {
      const searchLower = options.search.toLowerCase();
      filteredData = filteredData.filter((job) => {
        return (
          job.jobType?.toLowerCase().includes(searchLower) ||
          job.companyName?.toLowerCase().includes(searchLower) ||
          job.department?.toLowerCase().includes(searchLower) ||
          job.location?.some((loc: any) => loc.toLowerCase().includes(searchLower))
        );
      });
    }

    // Apply other filters
    if (options.filters && Object.keys(options.filters).length > 0) {
      filteredData = filteredData.filter((job) => {
        const filters = options.filters!;
        if (filters.location && !job.location?.includes(filters.location)) return false;
        if (filters.jobType && job.jobType !== filters.jobType) return false;
        if (filters.department && job.department !== filters.department) return false;
        if (filters.experienceLevel && job.experienceLevel !== filters.experienceLevel)
          return false;
        return true;
      });
    }

    return {
      ...jobs,
      data: filteredData as any,
      metadata: {
        ...jobs.metadata,
        filtered: filteredData.length,
      },
    };
  }

  /**
   * Paginate the results
   */
  private paginateResults(jobs: JobsResponse, page: number, limit: number): JobsResponse {
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = jobs.data.slice(startIndex, endIndex);

    return {
      ...jobs,
      data: paginatedData,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(jobs.data.length / limit),
        totalItems: jobs.data.length,
        itemsPerPage: limit,
      },
    };
  }
}
