import { Module, forwardRef } from '@nestjs/common';

import { ApolloModule } from '../../shared/services/apollo/apollo.module';
import { CandidateApplicationController } from './candidate-application.controller';
import { CandidateController } from './candidate.controller';
import { ApolloScoutController } from './controllers/apollo-scout.controller';
import { CandidateApplication } from './entities/candidate-application.entity';
import { Candidate } from './entities/candidate.entity';
import { CandidateActivityService } from './services/candidate-activity.service';
import { CandidateApplicationService } from './services/candidate-application.service';
import { CandidateEmailService } from './services/candidate-email.service';
import { CandidateEnhancementService } from './services/candidate-enhancement.service';
import { CandidateCreatorUtil } from './utils/candidate-creator.util';
// New entities and services
import { QueueConfigModule } from '@/shared/modules/queue-config.module';
import { EnhancedResumeParserService } from '@/shared/services/enhanced-resume-parser.service';
import { GroqService } from '@/shared/services/groq.service';
import { MultiAIResumeParserService } from '@/shared/services/multi-ai-resume-parser.service';
import { SharedModule } from '@/shared/shared.module';
import { Graduate } from '@modules/graduate/entities/graduate.entity';
import { JobApplication } from '@modules/job-seeker/entities/job-application.entity';
import { JobSeeker } from '@modules/job-seeker/entities/job-seeker.entity';
import { Job } from '@modules/job/entities/job.entity';
import { VideoResponse } from '@modules/video-response/entities/video-response.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OpenaiModule } from '@shared/modules/openai.module';
import { DigitalOceanSpacesService } from '@shared/services/digital-ocean-spaces.service';
import { CompanyModule } from '../company/company.module';
import { EmailModule } from '../email/email.module';
import { JobSeekerModule } from '../job-seeker/job-seeker.module';
import { JobModule } from '../job/job.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { CandidateProfileController } from './candidate-profile.controller';
import { CandidateService } from './candidate.service';
import { CandidateServiceUtils } from './candidate.service.utils';
import { CandidateManagementController } from './controllers/candidate-management.controller';
import { LinkedInScoutController } from './controllers/linkedin-scout.controller';
import { PDLScoutController } from './controllers/pdl-scout.controller';
import { ScoutedCandidateController } from './controllers/scouted-candidate.controller';
import { CandidateEvaluation } from './entities/candidate-evaluation.entity';
import { CandidateProfile } from './entities/candidate-profile.entity';
import { ScoutedCandidate } from './entities/scouted-candidate.entity';
import { CandidateManagementService } from './services/candidate-management.service';
import { CandidateProfileService } from './services/candidate-profile.service';
import { CandidateQueryService } from './services/candidate-query.service';
import { CandidateStatusService } from './services/candidate-status.service';
import { LinkedInScoutService } from './services/linkedin-scout.service';
import { ResumeParserService } from './services/resume-parser.service';
import { ScoutedCandidateService } from './services/scouted-candidate.service';
import { CandidateUploadService } from './services/candidate-upload.service';
import { CandidateCacheService } from './services/candidate-cache.service';
import { CandidateCrudService } from './services/candidate-crud.service';
import { CandidateSearchService } from './services/candidate-search.service';
import { CandidateScoutingService } from './services/candidate-scouting.service';
import { EmployerDashboardService } from './services/employer-dashboard.service';
import { CsvParserService } from './services/csv-parser.service';
import { BulkTalentUploadService } from './services/bulk-talent-upload.service';
import { BulkUploadController } from './controllers/bulk-upload.controller';
import { UploadStatusController } from './controllers/upload-status.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Candidate,
      VideoResponse,
      Job,
      Graduate,
      CandidateProfile,
      CandidateApplication,
      CandidateEvaluation,
      JobApplication,
      JobSeeker,
      ScoutedCandidate,
    ]),
    forwardRef(() => JobModule),
    forwardRef(() => JobSeekerModule),
    forwardRef(() => EmailModule),
    CompanyModule,
    OpenaiModule,
    SharedModule,
    SubscriptionModule, // Add SubscriptionModule to have access to SubscriptionValidationEngine
    forwardRef(() => QueueConfigModule), // Add QueueConfigModule to have access to PDLScoutService
    ApolloModule, // Add ApolloModule for enhanced Apollo services
  ],
  controllers: [
    CandidateController,
    CandidateProfileController,
    CandidateApplicationController,
    CandidateManagementController,
    ScoutedCandidateController,
    LinkedInScoutController,
    ApolloScoutController,
    PDLScoutController,
    BulkUploadController,
    UploadStatusController,
  ],
  providers: [
    CandidateService,
    ResumeParserService,
    // New optimized resume processing services
    GroqService,
    MultiAIResumeParserService,
    EnhancedResumeParserService,
    // Existing services
    CandidateProfileService,
    CandidateApplicationService,
    CandidateManagementService,
    DigitalOceanSpacesService,
    CandidateCreatorUtil,
    CandidateServiceUtils,
    LinkedInScoutService,
    ScoutedCandidateService,
    CandidateStatusService,
    CandidateEmailService,
    CandidateEnhancementService,
    CandidateQueryService,
    CandidateActivityService,
    // New refactored services
    CandidateUploadService,
    CandidateCacheService,
    CandidateCrudService,
    CandidateSearchService,
    CandidateScoutingService,
    EmployerDashboardService,
    CsvParserService,
    BulkTalentUploadService,
  ],
  exports: [
    CandidateService,
    CandidateProfileService,
    CandidateApplicationService,
    CandidateManagementService,
    ResumeParserService,
    // Export new optimized resume processing services
    GroqService,
    MultiAIResumeParserService,
    EnhancedResumeParserService,
    // Existing exports
    CandidateCreatorUtil,
    CandidateServiceUtils,
    LinkedInScoutService,
    ScoutedCandidateService,
    CandidateStatusService,
    CandidateQueryService,
    CandidateActivityService,
    // New refactored services
    CandidateUploadService,
    CandidateCacheService,
    CandidateCrudService,
    CandidateSearchService,
    CandidateScoutingService,
    EmployerDashboardService,
    // Bulk upload services
    CsvParserService,
    BulkTalentUploadService,
  ],
})
export class CandidateModule {}
