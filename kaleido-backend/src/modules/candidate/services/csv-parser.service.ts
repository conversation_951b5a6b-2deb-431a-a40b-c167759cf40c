import { Injectable, Logger } from '@nestjs/common';
import csv from 'csv-parser';
import { Readable } from 'stream';
import { CandidateStatus } from '@/shared/types/candidate.types';

export interface CsvCandidateData {
  fullName: string;
  firstName?: string;
  lastName?: string;
  email: string;
  phone?: string;
  location?: string;
  jobTitle?: string;
  currentCompany?: string;
  yearsOfExperience?: number;
  summary?: string;
  skills?: string[];
  linkedinUrl?: string;
  githubUrl?: string;
  preferredLocation?: string;
  isRemoteOnly?: boolean;
  salary?: {
    min?: number;
    max?: number;
    currency?: string;
  };
}

@Injectable()
export class CsvParserService {
  private readonly logger = new Logger(CsvParserService.name);

  async parseCandidatesCsv(file: Express.Multer.File): Promise<CsvCandidateData[]> {
    return new Promise((resolve, reject) => {
      const results: CsvCandidateData[] = [];
      const stream = Readable.from(file.buffer);
      let rowCount = 0;
      const errors: string[] = [];

      stream
        .pipe(csv())
        .on('data', (data) => {
          rowCount++;
          try {
            const processedCandidate = this.processCsvRow(data);
            if (processedCandidate) {
              results.push(processedCandidate);
            } else {
              errors.push(`Row ${rowCount}: Missing required fields (fullName or email)`);
            }
          } catch (error) {
            errors.push(`Row ${rowCount}: ${error.message}`);
          }
        })
        .on('end', () => {
          if (errors.length > 0) {
            this.logger.warn(`CSV parsing completed with ${errors.length} errors`, errors);
          }
          this.logger.log(`Successfully parsed ${results.length} candidates from CSV`);
          resolve(results);
        })
        .on('error', (error) => {
          this.logger.error('CSV parsing failed', error);
          reject(error);
        });
    });
  }

  private processCsvRow(row: any): CsvCandidateData | null {
    // Validate required fields
    if (!row.fullName?.trim() && (!row.firstName?.trim() || !row.lastName?.trim())) {
      return null;
    }
    if (!row.email?.trim()) {
      return null;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(row.email.trim())) {
      throw new Error(`Invalid email format: ${row.email}`);
    }

    // Parse name
    const fullName =
      row.fullName?.trim() || `${row.firstName?.trim() || ''} ${row.lastName?.trim() || ''}`.trim();
    const nameParts = fullName.split(' ');
    const firstName = row.firstName?.trim() || nameParts[0] || '';
    const lastName = row.lastName?.trim() || nameParts.slice(1).join(' ') || '';

    // Parse skills (comma-separated)
    const skills = row.skills
      ? row.skills
          .split(',')
          .map((s: string) => s.trim())
          .filter((s: string) => s.length > 0)
      : [];

    // Parse years of experience
    const yearsOfExperience = row.yearsOfExperience
      ? parseInt(row.yearsOfExperience, 10)
      : undefined;

    // Parse salary if provided
    let salary;
    if (row.salaryMin || row.salaryMax) {
      salary = {
        min: row.salaryMin ? parseFloat(row.salaryMin) : undefined,
        max: row.salaryMax ? parseFloat(row.salaryMax) : undefined,
        currency: row.currency?.trim() || 'USD',
      };
    }

    // Parse boolean for remote only
    const isRemoteOnly =
      row.isRemoteOnly?.toLowerCase() === 'true' ||
      row.isRemoteOnly === '1' ||
      row.isRemoteOnly === 'yes';

    return {
      fullName,
      firstName,
      lastName,
      email: row.email.trim(),
      phone: row.phone?.trim(),
      location: row.location?.trim(),
      jobTitle: row.jobTitle?.trim() || row.position?.trim(),
      currentCompany: row.currentCompany?.trim() || row.company?.trim(),
      yearsOfExperience,
      summary: row.summary?.trim() || row.bio?.trim(),
      skills,
      linkedinUrl: this.normalizeUrl(row.linkedinUrl),
      githubUrl: this.normalizeUrl(row.githubUrl),
      preferredLocation: row.preferredLocation?.trim(),
      isRemoteOnly,
      salary,
    };
  }

  private normalizeUrl(url?: string): string | undefined {
    if (!url?.trim()) return undefined;

    const trimmedUrl = url.trim();

    // Add https:// if no protocol is specified
    if (!trimmedUrl.startsWith('http://') && !trimmedUrl.startsWith('https://')) {
      if (trimmedUrl.includes('linkedin.com')) {
        return `https://${trimmedUrl}`;
      }
      if (trimmedUrl.includes('github.com')) {
        return `https://${trimmedUrl}`;
      }
    }

    return trimmedUrl;
  }

  generateCsvTemplate(): string {
    const headers = [
      'fullName',
      'firstName',
      'lastName',
      'email',
      'phone',
      'location',
      'jobTitle',
      'currentCompany',
      'yearsOfExperience',
      'skills',
      'linkedinUrl',
      'githubUrl',
      'summary',
      'preferredLocation',
      'isRemoteOnly',
      'salaryMin',
      'salaryMax',
      'currency',
    ];

    const exampleRow = [
      'John Doe',
      'John',
      'Doe',
      '<EMAIL>',
      '+1234567890',
      'New York, NY',
      'Senior Software Engineer',
      'Tech Corp',
      '5',
      'JavaScript,React,Node.js,TypeScript',
      'https://linkedin.com/in/johndoe',
      'https://github.com/johndoe',
      'Experienced software engineer with expertise in full-stack development',
      'San Francisco, CA',
      'false',
      '120000',
      '150000',
      'USD',
    ];

    return `${headers.join(',')}\n${exampleRow.join(',')}`;
  }
}
