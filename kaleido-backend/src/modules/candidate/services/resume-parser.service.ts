import { OpenAI } from 'openai';

import { EnhancedResumeParserService } from '@/shared/services/enhanced-resume-parser.service';
import { ResumeProcessorService } from '@/shared/services/resume-processor.service';
import { extractFileContents } from '@/utils/file-extractor.util';
import { safeJsonParse } from '@/utils/json-cleaner.util';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ModuleRef } from '@nestjs/core';

import { JobService } from '../../job/job.service';
import { CreateCandidateDto } from '../dto/create-candidate.dto';
import { Candidate } from '../entities/candidate.entity';

const GPT_MODELS = {
  DEFAULT: 'gpt-4',
  FALLBACK: 'gpt-3.5-turbo',
} as const;

const BATCH_SIZE = 50; // Increased from 3 to 50 resumes to process in parallel
const RATE_LIMIT_DELAY = 100; // Reduced from 1000ms to 100ms delay between batches

const defaultModel = 'gpt-4';

@Injectable()
export class ResumeParserService {
  private readonly openai: OpenAI;
  private readonly logger = new Logger(ResumeParserService.name);
  private readonly useSLM: boolean;

  constructor(
    private readonly configService: ConfigService,
    private readonly moduleRef: ModuleRef,
    private readonly enhancedResumeParser: EnhancedResumeParserService,
    private readonly jobService: JobService,
  ) {
    this.openai = new OpenAI({
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
    });
    // Disable SLM usage - using optimized multi-AI load balancing instead
    this.useSLM = false; // Force disable SLM for performance
  }

  private generateSystemPrompt(): string {
    return `You are a precise resume parser. Extract only factual information present in the resume.
Do not invent, assume, or infer information not explicitly stated. If a field is not found, return null or an empty value.
Format dates as YYYY-MM-DD when possible, or return null if unclear.
IMPORTANT: Return ONLY valid JSON. Do not include markdown formatting, code blocks, or any explanatory text.
Do not wrap the JSON in backticks or include any text before or after the JSON object.`;
  }

  private generateUserPrompt(resumeContent: string): string {
    return `
Extract key information from this resume and return ONLY valid JSON without any markdown formatting, explanatory text, or code blocks.
Resume content: ${resumeContent}

Return ONLY this JSON structure (no text before or after, no backticks):
{
  "fullName": "candidate's full name",
  "jobTitle": "most recent job title",
  "location": "location if present",
  "summary": "brief professional summary",
  "skills": ["all technical and soft skills"],
  "experience": [
    {
      "title": "job title",
      "company": "company name",
      "duration": "calculated duration",
      "startDate": "YYYY-MM-DD or null",
      "endDate": "YYYY-MM-DD or null if current",
      "responsibilities": ["key responsibilities"]
    }
  ],
  "education": [
    {
      "degree": "degree name",
      "institution": "school name",
      "graduationDate": "YYYY-MM-DD or null"
    }
  ],
  "certifications": [
    {
      "name": "certification name",
      "issuer": "issuing organization",
      "issueDate": "YYYY-MM-DD or null",
      "expiryDate": "YYYY-MM-DD or null"
    }
  ],
  "linkedinUrl": "LinkedIn URL or null",
  "githubUrl": "GitHub URL or null",
  "portfolioUrl": "Portfolio URL or null"
}`;
  }

  private sanitizeContent(text: string): string {
    return text
      .replace(/\r\n/g, '\n')
      .replace(/\n{3,}/g, '\n\n')
      .replace(/\t/g, ' ')
      .replace(/ {2,}/g, ' ')
      .trim();
  }

  private async parseWithGPT(content: string, retryCount = 0): Promise<any> {
    const maxRetries = 2;
    const model = retryCount === 0 ? GPT_MODELS.DEFAULT : GPT_MODELS.FALLBACK;

    try {
      const response = await this.openai.chat.completions.create({
        model,
        messages: [
          {
            role: 'system',
            content: this.generateSystemPrompt(),
          },
          {
            role: 'user',
            content: this.generateUserPrompt(content),
          },
        ],
        max_tokens: 5000,
        temperature: 0,
      });

      const parsedContent = response?.choices[0]?.message?.content;
      if (!parsedContent) {
        throw new Error('No content received from OpenAI');
      }

      return JSON.parse(parsedContent);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`GPT parsing error: ${error.message}`);

        if (retryCount < maxRetries) {
          this.logger.log(`Retrying with ${model} (attempt ${retryCount + 1}/${maxRetries})`);
          return this.parseWithGPT(content, retryCount + 1);
        }
      }
      throw error;
    }
  }

  private validateParsedData(data: any): void {
    const requiredFields = ['fullName', 'jobTitle', 'skills', 'experience'];
    const missingFields = requiredFields.filter((field) => !data[field]);

    if (missingFields.length > 0) {
      throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
    }
  }

  async parseResumeData(
    file: {
      mimetype: string;
      buffer: Buffer;
      originalname: string;
      textContent?: string;
    },
    jobId: string,
  ): Promise<CreateCandidateDto> {
    // Validate that we have a valid buffer
    if (!file.buffer) {
      console.error('❌ File buffer is missing or undefined');
      throw new Error('File buffer is missing or invalid. Cannot process the resume.');
    }

    if (!Buffer.isBuffer(file.buffer)) {
      console.warn('⚠️ File buffer is not a proper Buffer instance, attempting to convert');
      try {
        // Try to convert to Buffer if it's in a serialized format
        if (file.buffer && typeof file.buffer === 'object' && 'data' in file.buffer) {
          // @ts-expect-error - TypeScript doesn't understand the dynamic type check
          file.buffer = Buffer.from(file.buffer.data);
        } else if (Array.isArray(file.buffer)) {
          file.buffer = Buffer.from(file.buffer);
        } else {
          console.error('❌ Unable to convert to Buffer:', typeof file.buffer);
          throw new Error('Invalid buffer format. Cannot process the resume.');
        }
      } catch (error) {
        console.error('❌ Error converting buffer:', error);
        throw new Error('Failed to convert file buffer. Cannot process the resume.');
      }
    }

    let content: string;

    if (file.textContent) {
      content = file.textContent;
    } else {
      console.time('extractFileContents');
      try {
        const extractionResult = await extractFileContents([file as Express.Multer.File], []);
        console.timeEnd('extractFileContents');

        if (extractionResult.duplicateFiles.length > 0) {
          console.warn('⚠️ Duplicate resume detected');
          throw new Error('Duplicate resume detected.');
        }

        if (extractionResult.contents.length === 0) {
          console.error('❌ No content extracted from the resume');
          throw new Error('Could not extract text from the provided file');
        }

        content = extractionResult.contents[0].content;

        // Check if content is empty or too short
        if (!content || content.trim().length < 50) {
          console.error('❌ Extracted content is too short or empty:', content?.length || 0);
          throw new Error('Could not extract meaningful text from the provided file');
        }
      } catch (error) {
        console.error('❌ Error during content extraction:', error);
        throw new Error(
          'Could not extract text from the provided file: ' +
            (error instanceof Error ? error.message : String(error)),
        );
      }
    }

    // Check if SLM is enabled
    if (this.useSLM) {
      try {
        const timerId = `parserResumeProcessing_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
        console.time(timerId);

        const resumeProcessorService = this.moduleRef.get(ResumeProcessorService, {
          strict: false,
        });
        if (!resumeProcessorService) {
          throw new Error('ResumeProcessorService not available');
        }

        const processedData = await resumeProcessorService.processResume(content);

        console.timeEnd(timerId);

        const candidateDto = this.mapProcessedDataToCandidateDto(processedData, jobId);
        return candidateDto;
      } catch (error) {
        console.error('❌ Error processing resume with SLM:', error);
      }
    }
    // Use the new optimized enhanced resume parser service
    return this.useOptimizedParser(file, jobId);
  }

  /**
   * Use the new optimized enhanced resume parser with load balancing
   */
  private async useOptimizedParser(
    file: {
      mimetype: string;
      buffer: Buffer;
      originalname: string;
      textContent?: string;
    },
    jobId: string,
  ): Promise<CreateCandidateDto> {
    const startTime = Date.now();

    try {
      // Use the enhanced resume parser service for single file processing
      const result = await this.enhancedResumeParser.parseResumeData(file, jobId);

      const processingTime = (Date.now() - startTime) / 1000;

      // Update job metrics after successful candidate creation
      await this.updateJobMetricsAfterSingle(jobId);

      return result;
    } catch (error: any) {
      const processingTime = (Date.now() - startTime) / 1000;
      console.error(`❌ Optimized parser failed after ${processingTime.toFixed(2)}s:`, error);

      // Fallback to the old OpenAI method if the new system fails
      const content = file.textContent || (await this.extractTextContent(file));
      return this.fallbackToOpenAI(content, jobId);
    }
  }

  /**
   * Extract text content from file buffer
   */
  private async extractTextContent(file: {
    mimetype: string;
    buffer: Buffer;
    originalname: string;
  }): Promise<string> {
    const { contents } = await extractFileContents([file as any], []);
    if (contents.length === 0) {
      throw new Error('No content extracted from the resume');
    }
    return contents[0].content;
  }

  /**
   * Update job metrics after successful single file processing
   */
  private async updateJobMetricsAfterSingle(jobId: string): Promise<void> {
    try {
      // Get the actual candidate count from database for accuracy
      const actualCandidateCount = await this.jobService.getCandidatesCount(jobId);

      // Get the current job to update metrics
      const job = await this.jobService.findOne(jobId);
      if (!job) {
        console.warn(`⚠️ Job ${jobId} not found for single metrics update`);
        return;
      }

      // Initialize metrics if not present
      const currentMetrics = job.metrics || {
        views: 0,
        applications: 0,
        viewSources: {},
        applicationSources: {},
        matchScores: {},
      };

      // Update application sources
      const applicationSources = currentMetrics.applicationSources || {};
      applicationSources['resume_upload'] = (applicationSources['resume_upload'] || 0) + 1;

      // Update the job metrics with actual count
      const updatedMetrics = {
        ...currentMetrics,
        applications: actualCandidateCount, // Use actual count for accuracy
        applicationSources,
      };

      // Update the job metrics directly using repository
      await this.jobService['jobRepository'].update(jobId, { metrics: updatedMetrics });
    } catch (error: any) {
      console.error(`❌ Error updating job metrics after single upload for job ${jobId}:`, error);
      // Don't throw error to avoid breaking the resume processing flow
    }
  }

  /**
   * Update job metrics after successful batch processing
   */
  private async updateJobMetricsAfterBatch(jobId: string, successfulCount: number): Promise<void> {
    try {
      // Get the actual candidate count from database for accuracy
      const actualCandidateCount = await this.jobService.getCandidatesCount(jobId);

      // Get the current job to update metrics
      const job = await this.jobService.findOne(jobId);
      if (!job) {
        console.warn(`⚠️ Job ${jobId} not found for batch metrics update`);
        return;
      }

      // Initialize metrics if not present
      const currentMetrics = job.metrics || {
        views: 0,
        applications: 0,
        viewSources: {},
        applicationSources: {},
        matchScores: {},
      };

      // Update application sources
      const applicationSources = currentMetrics.applicationSources || {};
      applicationSources['batch_upload'] =
        (applicationSources['batch_upload'] || 0) + successfulCount;

      // Update the job metrics with actual count
      const updatedMetrics = {
        ...currentMetrics,
        applications: actualCandidateCount, // Use actual count for accuracy
        applicationSources,
      };

      // Update the job metrics directly
      await this.jobService['jobRepository'].update(jobId, { metrics: updatedMetrics });
    } catch (error: any) {
      console.error(`❌ Error updating job metrics after batch for job ${jobId}:`, error);
      // Don't throw error to avoid breaking the resume processing flow
    }
  }

  private async fallbackToOpenAI(content: string, jobId: string): Promise<CreateCandidateDto> {
    const openaiApi = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

    const improvedUserPrompt = `
You are an expert resume parser. Extract information to populate JobSeeker and Candidate entities.

ALWAYS INFER these fields when not explicitly stated:
1. languages: ALWAYS include the resume's language (e.g., if resume is in English, add "English")
2. jobTitle: Use the most recent job title from experience
3. currentCompany: Company from job without end date OR most recent job
4. yearsOfExperience: Calculate from earliest job to present
5. preferredLocation: Default to current location if not specified
6. isRemoteOnly: true if last 2+ jobs were remote, false otherwise
7. availableFrom: Immediate (today) if unemployed, otherwise add 2-4 weeks
8. summary: Generate from experience if not provided
9. salary: Estimate based on role/location/experience if not mentioned

Resume content: ${content}

IMPORTANT: Return ONLY valid JSON. Do not include any text, markdown formatting, or code blocks. Start directly with { and end with }. Here is the required structure:
{
  "fullName": "extract full name",
  "email": "extract email if present",
  "phone": "extract phone if present",
  "jobTitle": "extract most recent job title",
  "location": "extract current location if present",
  "preferredLocation": "extract preferred location if mentioned, otherwise use current location",
  "currentCompany": "extract current company or most recent",
  "yearsOfExperience": calculate total years of experience as number,
  "summary": "extract or generate professional summary from experience",
  "skills": ["extract ALL skills from entire document"],
  "experience": [
    {
      "title": "exact job title",
      "company": "exact company name",
      "location": "location of this job if mentioned",
      "duration": "calculate total time",
      "startDate": "find start date YYYY-MM-DD",
      "endDate": "find end date YYYY-MM-DD or null if current",
      "responsibilities": ["key responsibilities"],
      "achievements": ["achievements in role"]
    }
  ],
  "education": [
    {
      "degree": "degree name",
      "field": "field of study",
      "institution": "school name",
      "location": "school location",
      "startDate": "YYYY-MM-DD or null",
      "endDate": "YYYY-MM-DD or null",
      "gpa": number or null
    }
  ],
  "certifications": [
    {
      "name": "certification name",
      "issuer": "issuing organization",
      "issueDate": "YYYY-MM-DD or null",
      "expiryDate": "YYYY-MM-DD or null"
    }
  ],
  "languages": ["ALWAYS include resume language, plus any others mentioned"],
  "linkedinUrl": "find linkedin URL or null",
  "githubUrl": "find github URL or null",
  "portfolioUrl": "find portfolio URL or null",
  "isRemoteOnly": boolean based on job history,
  "availableFrom": "YYYY-MM-DD based on employment status",
  "expectedSalary": {
    "min": number or estimate,
    "max": number or estimate,
    "currency": "USD/EUR/GBP etc based on location"
  }
}

INFERENCE RULES:
- If currently employed (job without end date): availableFrom = today + 2-4 weeks
- If unemployed (all jobs have end dates): availableFrom = today
- Always include resume's language in languages array
- If no summary, generate one from most recent experience
- Extract skills from job descriptions, not just skills section
- Infer remote preference from job location patterns
- Estimate salary if not mentioned based on title/location/experience`;

    console.time('openAIProcessing');
    try {
      const response = await openaiApi.chat.completions.create({
        model: defaultModel,
        messages: [
          {
            role: 'system',
            content:
              'You are an expert resume parser. Extract information accurately and infer missing fields intelligently. CRITICAL: Return ONLY valid JSON without any markdown formatting, code blocks (no json code blocks), or explanatory text. Start your response with { and end with }.',
          },
          {
            role: 'user',
            content: improvedUserPrompt,
          },
        ],
        max_tokens: 5000,
        temperature: 0,
      });
      console.timeEnd('openAIProcessing');

      const content = response?.choices[0]?.message?.content;

      if (!content) {
        throw new Error('Failed to extract resume information');
      }

      let parsedData;
      try {
        parsedData = safeJsonParse(content);
      } catch (parseError: any) {
        console.error('❌ JSON parsing failed:', content);
        throw new Error(`Failed to parse resume data: ${parseError.message}`);
      }

      // Process the parsed data
      const candidate = new Candidate();
      candidate.jobId = jobId;

      // Prepare experience array for further processing
      const experience = Array.isArray(parsedData.experience) ? parsedData.experience : [];

      // Sort experience by end date (most recent first)
      const sortedExperience = [...experience].sort((a, b) => {
        const aDate = a.endDate ? new Date(a.endDate) : new Date(); // Current job if no end date
        const bDate = b.endDate ? new Date(b.endDate) : new Date();
        return bDate.getTime() - aDate.getTime();
      });

      // Get most recent job title and company as fallbacks
      const latestJobTitle = sortedExperience.length > 0 ? sortedExperience[0].title : '';
      const latestCompany = sortedExperience.length > 0 ? sortedExperience[0].company : '';

      // Get location from most recent experience if available
      const latestLocation =
        sortedExperience.length > 0 && sortedExperience[0].location
          ? sortedExperience[0].location
          : '';

      // Generate a summary if none is provided
      let generatedSummary = '';
      if (!parsedData.summary && sortedExperience.length > 0) {
        const recentJob = sortedExperience[0];
        const totalYears = this.calculateYearsOfExperience(experience);
        generatedSummary = `Professional with ${totalYears} years of experience, most recently as ${recentJob.title} at ${recentJob.company}.`;
      }

      // Map extracted data to new structure
      candidate.fullName = parsedData.fullName;
      candidate.jobTitle = parsedData.jobTitle || latestJobTitle; // Use latest job title as fallback
      candidate.location = parsedData.location || latestLocation; // Use location from most recent job as fallback
      candidate.summary = parsedData.summary || generatedSummary; // Use generated summary as fallback
      candidate.skills = Array.isArray(parsedData.skills) ? parsedData.skills : [];
      candidate.experience = experience;
      candidate.linkedinUrl = parsedData.linkedinUrl || null;
      candidate.githubUrl = parsedData.githubUrl || null;
      candidate.profileUrl = '';
      candidate.source = 'RESUME_UPLOAD';

      // Set preferred location to current location if not provided
      candidate.preferredLocation =
        parsedData.preferredLocation || parsedData.location || latestLocation;

      // Add new fields
      candidate.email = parsedData.email || null;
      candidate.phone = parsedData.phone || null;
      candidate.currentCompany = parsedData.currentCompany || latestCompany; // Use latest company as fallback

      // Calculate years of experience if not provided
      let yearsOfExp = null;
      if (parsedData.yearsOfExperience) {
        yearsOfExp = parseInt(parsedData.yearsOfExperience.toString(), 10);
      } else if (experience.length > 0) {
        // Calculate from experience using the existing method
        yearsOfExp = this.calculateYearsOfExperience(experience);
      }
      candidate.yearsOfExperience = yearsOfExp;

      // Split full name
      const nameParts = parsedData.fullName.split(' ');
      candidate.firstName = nameParts[0];
      candidate.lastName = nameParts.slice(1).join(' ');

      return candidate as CreateCandidateDto;
    } catch (error) {
      console.error('❌ OpenAI fallback failed:', error);
      throw new Error('Failed to process resume with both SLM and OpenAI');
    }
  }

  private mapProcessedDataToCandidateDto(processedData: any, jobId: string): CreateCandidateDto {
    // Map the processed data from ResumeProcessorService to CreateCandidateDto
    const names =
      processedData.firstName && processedData.lastName
        ? [processedData.firstName, processedData.lastName]
        : (processedData.fullName || '').split(' ');

    const firstName = names[0] || '';
    const lastName = names.slice(1).join(' ') || '';

    // Extract the most recent job title from experience
    let currentJobTitle = '';
    let currentCompany = '';
    let latestLocation = '';
    let generatedSummary = '';

    if (processedData.experience && processedData.experience.length > 0) {
      const sortedExperience = [...processedData.experience].sort((a, b) => {
        const aDate = a.endDate ? new Date(a.endDate) : new Date();
        const bDate = b.endDate ? new Date(b.endDate) : new Date();
        return bDate.getTime() - aDate.getTime();
      });

      currentJobTitle = sortedExperience[0].title || '';
      currentCompany = sortedExperience[0].company || '';

      // Get location from most recent experience if available
      if (sortedExperience[0].location) {
        latestLocation = sortedExperience[0].location;
      }

      // Generate a summary if none is provided
      if (!processedData.summary) {
        const recentJob = sortedExperience[0];
        const mappedExperience = this.mapExperience(processedData.experience || []);
        const totalYears = this.calculateYearsOfExperience(mappedExperience);
        generatedSummary = `Professional with ${totalYears} years of experience, most recently as ${recentJob.title} at ${recentJob.company}.`;
      }
    }

    // Create a candidate object that matches CreateCandidateDto
    const candidate = new Candidate();
    candidate.firstName = firstName;
    candidate.lastName = lastName;
    candidate.email = processedData.email || '';
    candidate.phone = processedData.phone || '';
    candidate.jobTitle = processedData.jobTitle || currentJobTitle;
    candidate.location = processedData.location || latestLocation;
    candidate.summary = processedData.summary || generatedSummary;
    candidate.skills = processedData.skills || [];

    // Map and store experience
    const mappedExperience = this.mapExperience(processedData.experience || []);
    candidate.experience = mappedExperience;

    // Set currentCompany if not already provided
    candidate.currentCompany = processedData.currentCompany || currentCompany;

    // Set preferred location to current location if not provided
    candidate.preferredLocation =
      processedData.preferredLocation || processedData.location || latestLocation;

    // Calculate years of experience if not provided
    if (processedData.yearsOfExperience) {
      candidate.yearsOfExperience =
        typeof processedData.yearsOfExperience === 'number'
          ? processedData.yearsOfExperience
          : parseInt(processedData.yearsOfExperience.toString(), 10);
    } else if (mappedExperience && mappedExperience.length > 0) {
      candidate.yearsOfExperience = this.calculateYearsOfExperience(mappedExperience);
    }

    candidate.linkedinUrl = processedData.linkedinUrl || '';
    candidate.githubUrl = processedData.githubUrl || '';
    candidate.jobId = jobId;
    candidate.profileUrl = '';
    candidate.source = 'RESUME_UPLOAD';
    candidate.fullName = `${firstName} ${lastName}`.trim();

    return candidate as CreateCandidateDto;
  }

  private calculateYearsOfExperience(experience: any[]): number {
    if (!experience || !Array.isArray(experience) || experience.length === 0) {
      return 0;
    }

    let totalMonths = 0;

    for (const job of experience) {
      if (job.startDate) {
        const startDate = new Date(job.startDate);
        const endDate = job.endDate ? new Date(job.endDate) : new Date();

        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
          const months =
            (endDate.getFullYear() - startDate.getFullYear()) * 12 +
            (endDate.getMonth() - startDate.getMonth());
          totalMonths += Math.max(0, months);
        }
      }
    }

    return Math.round(totalMonths / 12);
  }

  private mapExperience(experience: any[]): any[] {
    return experience.map((job) => ({
      title: job.title || '',
      company: job.company || '',
      duration: job.duration || '',
      startDate: job.startDate || '',
      endDate: job.endDate || null,
      location: job.location || '', // Preserve location if available
    }));
  }

  async parseMultipleResumes(
    files: Array<{
      mimetype: string;
      buffer: Buffer;
      originalname: string;
    }>,
    jobId: string,
  ): Promise<{
    successful: CreateCandidateDto[];
    failed: { filename: string; error: string }[];
  }> {
    const results = {
      successful: [] as CreateCandidateDto[],
      failed: [] as { filename: string; error: string }[],
    };

    console.time('batchProcessingTotal');

    // Check if we should use load balancing
    if (files.length >= 5) {
      try {
        // Use the enhanced resume parser service for batch processing
        const batchResult = await this.enhancedResumeParser.processResumes(files, jobId);

        // Convert to the expected format
        results.successful.push(...batchResult.successful);
        results.failed.push(...batchResult.failed);

        // Update job metrics after successful batch processing
        if (batchResult.successful.length > 0) {
          await this.updateJobMetricsAfterBatch(jobId, batchResult.successful.length);
        }

        console.timeEnd('batchProcessingTotal');
        return results;
      } catch (error) {}
    }

    try {
      // Extract content from all files first
      console.time('extractAllContents');

      const extractionResults = await extractFileContents(files as Express.Multer.File[], []);

      console.timeEnd('extractAllContents');

      // Map files to their contents
      const fileContents = extractionResults.contents.map((item) => item.content);

      if (fileContents.length === 0) {
        console.error('❌ No content extracted from any resumes');
        return results;
      }

      // Get the ResumeProcessorService from the module
      const resumeProcessorService = this.moduleRef.get(ResumeProcessorService, { strict: false });
      if (!resumeProcessorService) {
        throw new Error('ResumeProcessorService not available');
      }

      console.time('parallelProcessing');

      // Use the batch processing functionality
      const processedResults = await resumeProcessorService.processBatch(fileContents, false);

      console.timeEnd('parallelProcessing');

      // Map results back to files
      for (let i = 0; i < processedResults.length; i++) {
        try {
          const processedData = processedResults[i];
          const originalFile = files[i];

          if (!processedData) {
            results.failed.push({
              filename: originalFile.originalname,
              error: 'Failed to process resume',
            });
            continue;
          }

          // Map the processed data to the CreateCandidateDto format
          const candidateDto = this.mapProcessedDataToCandidateDto(processedData, jobId);
          results.successful.push(candidateDto);
        } catch (error) {
          const originalFile = files[i] || { originalname: `File ${i}` };
          results.failed.push({
            filename: originalFile.originalname,
            error: error instanceof Error ? error.message : 'Unknown error during mapping',
          });
        }
      }
    } catch (error) {
      console.error('❌ Error during batch processing:', error);

      // Fallback to sequential processing if batch processing fails

      await Promise.all(
        files.map(async (file) => {
          try {
            const candidate = await this.parseResumeData(file, jobId);
            results.successful.push(candidate);
          } catch (error) {
            results.failed.push({
              filename: file.originalname,
              error: error instanceof Error ? error.message : 'Unknown error',
            });
          }
        }),
      );
    }

    console.timeEnd('batchProcessingTotal');

    return results;
  }
}
