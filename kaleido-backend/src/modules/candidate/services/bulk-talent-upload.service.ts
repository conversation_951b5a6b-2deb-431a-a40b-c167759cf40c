import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CsvParserService, CsvCandidateData } from './csv-parser.service';
import { GroqService } from '../../../shared/services/groq.service';
import { CandidateService } from '../candidate.service';
import { Candidate, CandidateTier } from '../entities/candidate.entity';
import { CandidateStatus } from '@/shared/types/candidate.types';
import { extractFileContents } from '@/utils/file-extractor.util';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';
import { QUEUE_NAMES } from '@/shared/constants/queue.constants';

export interface BulkUploadResult {
  successCount: number;
  errorCount: number;
  errors: Array<{
    filename: string;
    error: string;
  }>;
  processedCandidates: Array<{
    id: string;
    fullName: string;
    email: string;
  }>;
  duplicatesSkipped: number;
}

export interface ExtractedCandidateData {
  fullName: string;
  firstName?: string;
  lastName?: string;
  email: string;
  phone?: string;
  location?: string;
  jobTitle?: string;
  currentCompany?: string;
  yearsOfExperience?: number;
  summary?: string;
  skills?: string[];
  experience?: Array<{
    title: string;
    company: string;
    duration?: string;
    startDate?: string;
    endDate?: string;
    location?: string;
    description?: string;
  }>;
  education?: Array<{
    degree: string;
    institution: string;
    year?: string;
    location?: string;
    gpa?: string;
    fieldOfStudy?: string;
  }>;
  linkedinUrl?: string;
  githubUrl?: string;
  preferredLocation?: string;
  isRemoteOnly?: boolean;
  salary?: {
    min?: number;
    max?: number;
    currency?: string;
  };
  availableFrom?: string;
  certifications?: string[];
  languages?: string[];
  projects?: Array<{
    name: string;
    description?: string;
    technologies?: string[];
    url?: string;
  }>;
}

@Injectable()
export class BulkTalentUploadService {
  private readonly logger = new Logger(BulkTalentUploadService.name);

  constructor(
    private readonly csvParser: CsvParserService,
    private readonly groqService: GroqService,
    private readonly candidateService: CandidateService,
    @InjectRepository(Candidate)
    private readonly candidateRepository: Repository<Candidate>,
    @InjectQueue(QUEUE_NAMES.FILE_UPLOAD)
    private readonly fileUploadQueue: Queue,
  ) {}

  async processFilesDirectly(
    files: Express.Multer.File[],
    clientId: string,
    jobId?: string,
  ): Promise<BulkUploadResult> {
    const results: BulkUploadResult = {
      successCount: 0,
      errorCount: 0,
      errors: [],
      processedCandidates: [],
      duplicatesSkipped: 0,
    };

    // Group files by type for batch processing
    const csvFiles = files.filter((f) => this.getFileType(f.originalname) === 'csv');
    const resumeFiles = files.filter((f) => this.getFileType(f.originalname) === 'resume');

    // Process CSV files first
    if (csvFiles.length > 0) {
      await this.processCsvFiles(csvFiles, clientId, jobId, results);
    }

    // Process resume files
    if (resumeFiles.length > 0) {
      await this.processResumeFiles(resumeFiles, clientId, jobId, results);
    }

    return results;
  }

  async processBulkUpload(
    files: Express.Multer.File[],
    clientId: string,
    jobId?: string,
  ): Promise<any> {
    // Validate file count
    if (files.length > 50) {
      throw new BadRequestException('Maximum 50 files allowed per upload');
    }

    const totalFiles = files.length;

    // Always queue for background processing (simplified approach)
    const queueJob = await this.queueAllFiles(files, clientId, jobId);

    return {
      success: true,
      message: `Queued ${totalFiles} file${totalFiles > 1 ? 's' : ''} for processing`,
      queueJobId: queueJob.id.toString(),
      jobId: queueJob.id.toString(), // Use queue job ID for polling
      status: 'queued',
      filesQueued: totalFiles,
    };
  }

  private async processCsvFiles(
    files: Express.Multer.File[],
    clientId: string,
    jobId: string | undefined,
    results: BulkUploadResult,
  ): Promise<void> {
    for (const file of files) {
      try {
        const candidatesData = await this.csvParser.parseCandidatesCsv(file);

        for (const candidateData of candidatesData) {
          try {
            // Check for duplicates
            const isDuplicate = await this.checkDuplicate(candidateData.email, clientId);
            if (isDuplicate) {
              results.duplicatesSkipped++;
              continue;
            }

            const candidate = await this.createCandidate(
              candidateData as ExtractedCandidateData,
              file.originalname,
              clientId,
              jobId,
              'CSV_UPLOAD',
            );

            results.processedCandidates.push({
              id: candidate.id,
              fullName: candidate.fullName || '',
              email: candidate.email || '',
            });
            results.successCount++;
          } catch (error) {
            results.errorCount++;
            results.errors.push({
              filename: file.originalname,
              error: `Failed to process candidate ${candidateData.email}: ${error.message}`,
            });
          }
        }
      } catch (error) {
        results.errorCount++;
        results.errors.push({
          filename: file.originalname,
          error: error.message,
        });
      }
    }
  }

  private async processResumeFiles(
    files: Express.Multer.File[],
    clientId: string,
    jobId: string | undefined,
    results: BulkUploadResult,
  ): Promise<void> {
    for (const file of files) {
      try {
        const candidateData = await this.processResumeFile(file);

        // Check for duplicates
        const isDuplicate = await this.checkDuplicate(candidateData.email, clientId);
        if (isDuplicate) {
          results.duplicatesSkipped++;
          continue;
        }

        const candidate = await this.createCandidate(
          candidateData,
          file.originalname,
          clientId,
          jobId,
          'RESUME_UPLOAD',
        );

        results.processedCandidates.push({
          id: candidate.id,
          fullName: candidate.fullName || '',
          email: candidate.email || '',
        });
        results.successCount++;
      } catch (error) {
        results.errorCount++;
        results.errors.push({
          filename: file.originalname,
          error: error.message,
        });
      }
    }
  }

  private async queueAllFiles(
    files: Express.Multer.File[],
    clientId: string,
    jobId: string | undefined,
  ): Promise<any> {
    // Create a batch job for all files
    const batchData = {
      files: files.map((file) => ({
        buffer: file.buffer,
        originalname: file.originalname,
        mimetype: file.mimetype,
        type: this.getFileType(file.originalname),
      })),
      clientId,
      jobId,
      totalFiles: files.length,
      timestamp: new Date().toISOString(),
    };

    // Add to queue for background processing
    const queueJob = await this.fileUploadQueue.add('process-batch', batchData, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: false,
      removeOnFail: false,
    });

    this.logger.log(
      `Queued batch of ${files.length} files for processing with job ID: ${queueJob.id}`,
    );

    return queueJob;
  }

  private async processResumeFile(file: Express.Multer.File): Promise<ExtractedCandidateData> {
    // Extract text from file
    const { contents } = await extractFileContents([file as any], []);

    if (contents.length === 0 || !contents[0].content) {
      throw new Error('Could not extract text from resume');
    }

    const resumeText = contents[0].content;

    if (resumeText.trim().length < 50) {
      throw new Error('Could not extract sufficient text from resume');
    }

    // Use Groq to extract structured data
    try {
      const extractedData = await this.groqService.extractResumeData(resumeText);

      // Validate required fields
      if (!extractedData.email || !extractedData.fullName) {
        throw new Error('Could not extract required information (email and name) from resume');
      }

      // Map to our ExtractedCandidateData format
      return {
        ...extractedData,
        availableFrom: extractedData.availableFrom || undefined,
        certifications: extractedData.certifications || [],
        languages: extractedData.languages || [],
        projects: extractedData.projects || [],
        preferredLocation: extractedData.preferredLocation || undefined,
        isRemoteOnly: extractedData.isRemoteOnly || false,
        salary: extractedData.salary || undefined,
      } as ExtractedCandidateData;
    } catch (error) {
      this.logger.error('Failed to parse resume with Groq', error);
      throw new Error(`Resume parsing failed: ${error.message}`);
    }
  }

  private getFileType(filename: string): 'resume' | 'csv' {
    const ext = filename.toLowerCase().split('.').pop() || '';
    if (!ext) throw new Error(`No file extension found for: ${filename}`);
    if (['csv', 'xlsx', 'xls'].includes(ext)) return 'csv';
    if (['pdf', 'doc', 'docx'].includes(ext)) return 'resume';
    throw new Error(`Unsupported file extension: ${ext}`);
  }

  private async checkDuplicate(email: string, clientId: string): Promise<boolean> {
    const existing = await this.candidateRepository.findOne({
      where: {
        email,
        userId: clientId,
      },
    });
    return !!existing;
  }

  private async createCandidate(
    candidateData: ExtractedCandidateData,
    originalFilename: string,
    clientId: string,
    jobId: string | undefined,
    source: string,
  ): Promise<Candidate> {
    // Validate and sanitize numeric fields
    const yearsOfExperience = candidateData.yearsOfExperience;
    const validYearsOfExperience =
      yearsOfExperience !== undefined &&
      yearsOfExperience !== null &&
      !isNaN(Number(yearsOfExperience))
        ? Number(yearsOfExperience)
        : null;

    // Validate salary fields if they exist
    let salary = candidateData.salary;
    if (salary) {
      const minSalary = salary.min;
      const maxSalary = salary.max;
      salary = {
        ...salary,
        min: minSalary !== undefined && !isNaN(Number(minSalary)) ? Number(minSalary) : undefined,
        max: maxSalary !== undefined && !isNaN(Number(maxSalary)) ? Number(maxSalary) : undefined,
      };
    }

    const candidate = this.candidateRepository.create({
      ...candidateData,
      yearsOfExperience: validYearsOfExperience,
      salary,
      userId: clientId,
      jobId,
      source,
      originalFilename,
      status: CandidateStatus.NEW,
      contacted: false,
      tier: CandidateTier.OTHER,
      createdAt: new Date(),
      updatedAt: new Date(),
      extractionMetadata: [
        {
          filename: originalFilename,
          extractedAt: new Date(),
          extractionSource: 'groq_ai',
          rawExtractedData: candidateData,
        },
      ],
    });

    return this.candidateRepository.save(candidate);
  }

  async exportCandidatesToCsv(clientId: string, jobId?: string): Promise<string> {
    const query = this.candidateRepository
      .createQueryBuilder('candidate')
      .where('candidate.userId = :clientId', { clientId });

    if (jobId) {
      query.andWhere('candidate.jobId = :jobId', { jobId });
    }

    const candidates = await query.getMany();

    if (candidates.length === 0) {
      throw new BadRequestException('No candidates found to export');
    }

    // Generate CSV header
    const headers = [
      'fullName',
      'firstName',
      'lastName',
      'email',
      'phone',
      'location',
      'jobTitle',
      'currentCompany',
      'yearsOfExperience',
      'skills',
      'linkedinUrl',
      'githubUrl',
      'summary',
      'status',
      'tier',
      'createdAt',
    ];

    // Generate CSV rows
    const rows = candidates.map((candidate) => {
      return [
        candidate.fullName || '',
        candidate.firstName || '',
        candidate.lastName || '',
        candidate.email || '',
        candidate.phone || '',
        candidate.location || '',
        candidate.jobTitle || '',
        candidate.currentCompany || '',
        candidate.yearsOfExperience?.toString() || '',
        candidate.skills?.join(';') || '',
        candidate.linkedinUrl || '',
        candidate.githubUrl || '',
        candidate.summary?.replace(/[\n\r,]/g, ' ') || '',
        candidate.status || '',
        candidate.tier || '',
        candidate.createdAt?.toISOString() || '',
      ]
        .map((field) => {
          // Escape fields containing commas or quotes
          if (field.includes(',') || field.includes('"') || field.includes('\n')) {
            return `"${field.replace(/"/g, '""')}"`;
          }
          return field;
        })
        .join(',');
    });

    // Combine headers and rows
    return [headers.join(','), ...rows].join('\n');
  }
}
