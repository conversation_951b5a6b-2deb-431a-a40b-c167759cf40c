import { Test, TestingModule } from '@nestjs/testing';
import { CsvParserService } from '../csv-parser.service';
import { Readable } from 'stream';

describe('CsvParserService', () => {
  let service: CsvParserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CsvParserService],
    }).compile();

    service = module.get<CsvParserService>(CsvParserService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('parseCandidatesCsv', () => {
    it('should parse valid CSV data', async () => {
      const csvContent = `fullName,firstName,lastName,email,phone,location,jobTitle,currentCompany,yearsOfExperience,skills
<PERSON>,<PERSON>,<PERSON>,<EMAIL>,+**********,"New York, NY",Software Engineer,Tech Corp,5,"JavaScript,React,Node.js"
<PERSON>,<PERSON>,<PERSON>,<EMAIL>,+**********,"San Francisco, CA",Product Manager,StartupCo,7,"Product Management,Agile,Scrum"`;

      const mockFile = {
        buffer: Buffer.from(csvContent),
        originalname: 'candidates.csv',
        mimetype: 'text/csv',
        size: csvContent.length,
      } as Express.Multer.File;

      const result = await service.parseCandidatesCsv(mockFile);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        fullName: 'John Doe',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********',
        location: 'New York, NY',
        jobTitle: 'Software Engineer',
        currentCompany: 'Tech Corp',
        yearsOfExperience: 5,
        summary: undefined,
        skills: ['JavaScript', 'React', 'Node.js'],
        linkedinUrl: undefined,
        githubUrl: undefined,
        preferredLocation: undefined,
        isRemoteOnly: false,
        salary: undefined,
      });
    });

    it('should handle missing optional fields', async () => {
      const csvContent = `fullName,email
John Doe,<EMAIL>
Jane Smith,<EMAIL>`;

      const mockFile = {
        buffer: Buffer.from(csvContent),
        originalname: 'minimal.csv',
        mimetype: 'text/csv',
        size: csvContent.length,
      } as Express.Multer.File;

      const result = await service.parseCandidatesCsv(mockFile);

      expect(result).toHaveLength(2);
      expect(result[0].fullName).toBe('John Doe');
      expect(result[0].email).toBe('<EMAIL>');
      expect(result[0].firstName).toBe('John');
      expect(result[0].lastName).toBe('Doe');
    });

    it('should skip rows with missing required fields', async () => {
      const csvContent = `fullName,email
John Doe,<EMAIL>
,<EMAIL>
No Email,
Valid User,<EMAIL>`;

      const mockFile = {
        buffer: Buffer.from(csvContent),
        originalname: 'mixed.csv',
        mimetype: 'text/csv',
        size: csvContent.length,
      } as Express.Multer.File;

      const result = await service.parseCandidatesCsv(mockFile);

      expect(result).toHaveLength(2);
      expect(result[0].fullName).toBe('John Doe');
      expect(result[1].fullName).toBe('Valid User');
    });

    it('should validate email format', async () => {
      const csvContent = `fullName,email
John Doe,invalid-email
Jane Smith,<EMAIL>`;

      const mockFile = {
        buffer: Buffer.from(csvContent),
        originalname: 'invalid-email.csv',
        mimetype: 'text/csv',
        size: csvContent.length,
      } as Express.Multer.File;

      const result = await service.parseCandidatesCsv(mockFile);

      // Only valid email should be processed
      expect(result).toHaveLength(1);
      expect(result[0].email).toBe('<EMAIL>');
    });

    it('should parse salary information correctly', async () => {
      const csvContent = `fullName,email,salaryMin,salaryMax,currency
John Doe,<EMAIL>,100000,150000,USD
Jane Smith,<EMAIL>,80000,120000,EUR`;

      const mockFile = {
        buffer: Buffer.from(csvContent),
        originalname: 'salary.csv',
        mimetype: 'text/csv',
        size: csvContent.length,
      } as Express.Multer.File;

      const result = await service.parseCandidatesCsv(mockFile);

      expect(result).toHaveLength(2);
      expect(result[0].salary).toEqual({
        min: 100000,
        max: 150000,
        currency: 'USD',
      });
      expect(result[1].salary).toEqual({
        min: 80000,
        max: 120000,
        currency: 'EUR',
      });
    });

    it('should parse boolean isRemoteOnly field correctly', async () => {
      const csvContent = `fullName,email,isRemoteOnly
John Doe,<EMAIL>,true
Jane Smith,<EMAIL>,false
Bob Johnson,<EMAIL>,yes
Alice Brown,<EMAIL>,1`;

      const mockFile = {
        buffer: Buffer.from(csvContent),
        originalname: 'remote.csv',
        mimetype: 'text/csv',
        size: csvContent.length,
      } as Express.Multer.File;

      const result = await service.parseCandidatesCsv(mockFile);

      expect(result).toHaveLength(4);
      expect(result[0].isRemoteOnly).toBe(true);
      expect(result[1].isRemoteOnly).toBe(false);
      expect(result[2].isRemoteOnly).toBe(true);
      expect(result[3].isRemoteOnly).toBe(true);
    });

    it('should normalize URLs correctly', async () => {
      const csvContent = `fullName,email,linkedinUrl,githubUrl
John Doe,<EMAIL>,linkedin.com/in/johndoe,github.com/johndoe
Jane Smith,<EMAIL>,https://linkedin.com/in/janesmith,https://github.com/janesmith`;

      const mockFile = {
        buffer: Buffer.from(csvContent),
        originalname: 'urls.csv',
        mimetype: 'text/csv',
        size: csvContent.length,
      } as Express.Multer.File;

      const result = await service.parseCandidatesCsv(mockFile);

      expect(result).toHaveLength(2);
      expect(result[0].linkedinUrl).toBe('https://linkedin.com/in/johndoe');
      expect(result[0].githubUrl).toBe('https://github.com/johndoe');
      expect(result[1].linkedinUrl).toBe('https://linkedin.com/in/janesmith');
      expect(result[1].githubUrl).toBe('https://github.com/janesmith');
    });

    it('should handle empty CSV file', async () => {
      const csvContent = '';

      const mockFile = {
        buffer: Buffer.from(csvContent),
        originalname: 'empty.csv',
        mimetype: 'text/csv',
        size: csvContent.length,
      } as Express.Multer.File;

      const result = await service.parseCandidatesCsv(mockFile);

      expect(result).toHaveLength(0);
    });

    it('should handle CSV with only headers', async () => {
      const csvContent = 'fullName,email,phone,location';

      const mockFile = {
        buffer: Buffer.from(csvContent),
        originalname: 'headers-only.csv',
        mimetype: 'text/csv',
        size: csvContent.length,
      } as Express.Multer.File;

      const result = await service.parseCandidatesCsv(mockFile);

      expect(result).toHaveLength(0);
    });
  });

  describe('generateCsvTemplate', () => {
    it('should generate a valid CSV template', () => {
      const template = service.generateCsvTemplate();

      expect(template).toContain('fullName,firstName,lastName,email');
      expect(template).toContain('John Doe,John,Doe,<EMAIL>');
      expect(template).toContain('JavaScript,React,Node.js,TypeScript');

      const lines = template.split('\n');
      expect(lines).toHaveLength(2); // Header + example row

      const headers = lines[0].split(',');
      expect(headers).toContain('fullName');
      expect(headers).toContain('email');
      expect(headers).toContain('skills');
      expect(headers).toContain('linkedinUrl');
      expect(headers).toContain('isRemoteOnly');
      expect(headers).toContain('salaryMin');
      expect(headers).toContain('salaryMax');
    });

    it('should have matching headers and example values', () => {
      const template = service.generateCsvTemplate();
      const lines = template.split('\n');
      const headers = lines[0].split(',');
      // The example row contains values with commas (e.g., "New York, NY" and skills list)
      // So we can't simply split by comma. Just verify we have 2 lines
      expect(lines.length).toBe(2);
      expect(headers.length).toBe(18); // 18 header fields
    });
  });
});
