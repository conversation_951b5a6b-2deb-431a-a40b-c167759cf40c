import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { ModuleRef } from '@nestjs/core';
import { ResumeParserService } from '../resume-parser.service';
import { EnhancedResumeParserService } from '@/shared/services/enhanced-resume-parser.service';
import { JobService } from '../../../job/job.service';
import OpenAI from 'openai';

// Mock OpenAI
const mockOpenAICreate = jest.fn();
jest.mock('openai', () => {
  return {
    default: jest.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: mockOpenAICreate,
        },
      },
    })),
    OpenAI: jest.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: mockOpenAICreate,
        },
      },
    })),
  };
});

// Mock file extraction utility
jest.mock('@/utils/file-extractor.util', () => ({
  extractFileContents: jest.fn().mockResolvedValue({
    contents: [
      {
        fileName: 'test.pdf',
        content:
          'This is a resume with more than 50 characters of extracted text content to pass validation',
      },
    ],
    duplicateFiles: [],
  }),
}));

describe('ResumeParserService Enhanced Fallback', () => {
  let service: ResumeParserService;
  let enhancedResumeParser: EnhancedResumeParserService;
  let jobService: JobService;

  const mockEnhancedResponse = {
    fullName: 'John Doe',
    email: '<EMAIL>',
    phone: '******-0123',
    jobTitle: 'Senior Software Engineer',
    location: 'San Francisco, CA',
    preferredLocation: 'San Francisco, CA',
    currentCompany: 'TechCorp',
    yearsOfExperience: 8,
    summary: 'Experienced engineer with 8 years building scalable applications',
    skills: ['JavaScript', 'TypeScript', 'React', 'Node.js', 'AWS'],
    experience: [
      {
        title: 'Senior Software Engineer',
        company: 'TechCorp',
        location: 'San Francisco, CA',
        duration: '2 years',
        startDate: '2022-01-01',
        endDate: null,
        responsibilities: ['Led team of 5', 'Designed architecture'],
        achievements: ['Improved performance by 40%'],
      },
      {
        title: 'Software Engineer',
        company: 'StartupXYZ',
        location: 'San Francisco, CA',
        duration: '4 years',
        startDate: '2018-01-01',
        endDate: '2021-12-31',
        responsibilities: ['Built APIs', 'Implemented features'],
        achievements: ['Reduced latency by 60%'],
      },
    ],
    education: [
      {
        degree: 'BS Computer Science',
        field: 'Computer Science',
        institution: 'Stanford University',
        location: 'Stanford, CA',
        startDate: '2012-09-01',
        endDate: '2016-05-31',
        gpa: 3.8,
      },
    ],
    certifications: [
      {
        name: 'AWS Solutions Architect',
        issuer: 'Amazon',
        issueDate: '2023-01-01',
        expiryDate: '2026-01-01',
      },
    ],
    languages: ['English', 'Spanish'],
    linkedinUrl: 'https://linkedin.com/in/johndoe',
    githubUrl: 'https://github.com/johndoe',
    portfolioUrl: 'https://johndoe.dev',
    isRemoteOnly: false,
    availableFrom: '2024-02-01',
    expectedSalary: {
      min: 180000,
      max: 220000,
      currency: 'USD',
    },
  };

  beforeEach(async () => {
    mockOpenAICreate.mockClear();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ResumeParserService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key) => {
              if (key === 'OPENAI_API_KEY') return 'test-api-key';
              return null;
            }),
          },
        },
        {
          provide: ModuleRef,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: EnhancedResumeParserService,
          useValue: {
            parseResumeData: jest.fn(),
          },
        },
        {
          provide: JobService,
          useValue: {
            findOne: jest.fn(),
            getCandidatesCount: jest.fn().mockResolvedValue(5),
            jobRepository: {
              update: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<ResumeParserService>(ResumeParserService);
    enhancedResumeParser = module.get<EnhancedResumeParserService>(EnhancedResumeParserService);
    jobService = module.get<JobService>(JobService);
  });

  describe('fallbackToOpenAI with Enhanced Prompt', () => {
    it('should use enhanced prompt with intelligent inference rules', async () => {
      const resumeContent = `John Doe
Senior Software Engineer
<EMAIL>

EXPERIENCE
Senior Software Engineer - TechCorp (2022-Present)
- Led development team
- Improved performance

Software Engineer - StartupXYZ (2018-2021)
- Built APIs
- Reduced latency`;

      mockOpenAICreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(mockEnhancedResponse),
            },
          },
        ],
      });

      const result = await service['fallbackToOpenAI'](resumeContent, 'job-id');

      // Verify enhanced prompt was used
      expect(mockOpenAICreate).toHaveBeenCalledWith(
        expect.objectContaining({
          messages: expect.arrayContaining([
            expect.objectContaining({
              role: 'system',
              content: expect.stringContaining(
                'Extract information accurately and infer missing fields intelligently',
              ),
            }),
            expect.objectContaining({
              role: 'user',
              content: expect.stringContaining('ALWAYS INFER these fields'),
            }),
          ]),
        }),
      );

      expect(result).toMatchObject({
        fullName: 'John Doe',
        jobTitle: 'Senior Software Engineer',
        currentCompany: 'TechCorp',
      });
    });

    it('should infer languages when not explicitly mentioned', async () => {
      const resumeContent = 'English resume content without languages section';

      mockOpenAICreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify({
                ...mockEnhancedResponse,
                languages: ['English'], // Should always include this
              }),
            },
          },
        ],
      });

      const result = await service['fallbackToOpenAI'](resumeContent, 'job-id');

      // Verify the prompt includes language inference rule
      expect(mockOpenAICreate).toHaveBeenCalledWith(
        expect.objectContaining({
          messages: expect.arrayContaining([
            expect.objectContaining({
              role: 'user',
              content: expect.stringContaining('languages'),
            }),
          ]),
        }),
      );
    });

    it('should calculate availability based on employment status', async () => {
      const resumeContent = 'Resume with current job (no end date)';

      const responseWithAvailability = {
        ...mockEnhancedResponse,
        availableFrom: '2024-02-01', // Future date for employed
        experience: [
          {
            ...mockEnhancedResponse.experience[0],
            endDate: null, // Currently employed
          },
        ],
      };

      mockOpenAICreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(responseWithAvailability),
            },
          },
        ],
      });

      const result = await service['fallbackToOpenAI'](resumeContent, 'job-id');

      // Verify inference rules in prompt
      expect(mockOpenAICreate).toHaveBeenCalledWith(
        expect.objectContaining({
          messages: expect.arrayContaining([
            expect.objectContaining({
              role: 'user',
              content: expect.stringContaining(
                'If currently employed (job without end date): availableFrom = today + 2-4 weeks',
              ),
            }),
          ]),
        }),
      );
    });

    it('should estimate salary when not provided', async () => {
      const resumeContent = 'Resume without salary information';

      mockOpenAICreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(mockEnhancedResponse),
            },
          },
        ],
      });

      await service['fallbackToOpenAI'](resumeContent, 'job-id');

      // Verify salary estimation in prompt
      expect(mockOpenAICreate).toHaveBeenCalledWith(
        expect.objectContaining({
          messages: expect.arrayContaining([
            expect.objectContaining({
              role: 'user',
              content: expect.stringContaining(
                'Estimate salary if not mentioned based on title/location/experience',
              ),
            }),
          ]),
        }),
      );
    });

    it('should detect remote preference from job history', async () => {
      const resumeContent = `EXPERIENCE
Remote Senior Engineer - Company A
Remote Software Engineer - Company B`;

      const responseWithRemote = {
        ...mockEnhancedResponse,
        isRemoteOnly: true,
      };

      mockOpenAICreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(responseWithRemote),
            },
          },
        ],
      });

      await service['fallbackToOpenAI'](resumeContent, 'job-id');

      // Verify remote inference rule
      expect(mockOpenAICreate).toHaveBeenCalledWith(
        expect.objectContaining({
          messages: expect.arrayContaining([
            expect.objectContaining({
              role: 'user',
              content: expect.stringContaining(
                'Infer remote preference from job location patterns',
              ),
            }),
          ]),
        }),
      );
    });

    it('should generate summary when not provided', async () => {
      const resumeContent = 'Resume without summary section';

      const responseWithGeneratedSummary = {
        ...mockEnhancedResponse,
        summary:
          'Senior Software Engineer with 8 years of experience. Most recently at TechCorp. Skilled in JavaScript, TypeScript, React.',
      };

      mockOpenAICreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(responseWithGeneratedSummary),
            },
          },
        ],
      });

      const result = await service['fallbackToOpenAI'](resumeContent, 'job-id');

      expect(result.summary).toBeTruthy();
      expect(result.summary).toContain('experience');

      // Verify summary generation rule
      expect(mockOpenAICreate).toHaveBeenCalledWith(
        expect.objectContaining({
          messages: expect.arrayContaining([
            expect.objectContaining({
              role: 'user',
              content: expect.stringContaining(
                'If no summary, generate one from most recent experience',
              ),
            }),
          ]),
        }),
      );
    });

    it('should use preferred location or default to current location', async () => {
      const resumeContent = 'Resume with location but no preferred location';

      mockOpenAICreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(mockEnhancedResponse),
            },
          },
        ],
      });

      await service['fallbackToOpenAI'](resumeContent, 'job-id');

      // Verify location inference
      expect(mockOpenAICreate).toHaveBeenCalledWith(
        expect.objectContaining({
          messages: expect.arrayContaining([
            expect.objectContaining({
              role: 'user',
              content: expect.stringContaining(
                'preferredLocation": "extract preferred location if mentioned, otherwise use current location',
              ),
            }),
          ]),
        }),
      );
    });

    it('should extract all enhanced fields from the response', async () => {
      mockOpenAICreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(mockEnhancedResponse),
            },
          },
        ],
      });

      const result = await service['fallbackToOpenAI']('resume content', 'job-id');

      // Verify all enhanced fields are requested
      expect(mockOpenAICreate).toHaveBeenCalledWith(
        expect.objectContaining({
          messages: expect.arrayContaining([
            expect.objectContaining({
              role: 'user',
              content: expect.stringMatching(
                /education.*certifications.*languages.*portfolioUrl.*isRemoteOnly.*availableFrom.*expectedSalary/is,
              ),
            }),
          ]),
        }),
      );
    });

    it('should handle parsing errors gracefully', async () => {
      mockOpenAICreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: 'Invalid JSON response',
            },
          },
        ],
      });

      await expect(service['fallbackToOpenAI']('resume content', 'job-id')).rejects.toThrow(
        'Failed to process resume with both SLM and OpenAI',
      );
    });

    it('should use most recent job for fallback data', async () => {
      const responseWithoutJobTitle = {
        ...mockEnhancedResponse,
        jobTitle: null,
      };

      mockOpenAICreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(responseWithoutJobTitle),
            },
          },
        ],
      });

      const result = await service['fallbackToOpenAI']('resume content', 'job-id');

      // Should use title from most recent experience
      expect(result.jobTitle).toBe('Senior Software Engineer');
    });

    it('should generate summary when not provided in response', async () => {
      const responseWithoutSummary = {
        ...mockEnhancedResponse,
        summary: null,
      };

      mockOpenAICreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(responseWithoutSummary),
            },
          },
        ],
      });

      const result = await service['fallbackToOpenAI']('resume content', 'job-id');

      // Should generate summary from experience
      expect(result.summary).toContain('Professional with');
      expect(result.summary).toContain('years of experience');
    });
  });

  describe('parseResumeData with enhanced fallback', () => {
    it('should use enhanced parser first, then fallback to OpenAI', async () => {
      const mockFile = {
        mimetype: 'application/pdf',
        buffer: Buffer.from('test content'),
        originalname: 'resume.pdf',
      };

      // Make enhanced parser fail
      jest
        .spyOn(enhancedResumeParser, 'parseResumeData')
        .mockRejectedValue(new Error('Enhanced parser failed'));

      // Mock OpenAI fallback
      mockOpenAICreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(mockEnhancedResponse),
            },
          },
        ],
      });

      const result = await service.parseResumeData(mockFile, 'job-id');

      expect(result).toBeTruthy();
      expect(mockOpenAICreate).toHaveBeenCalled();
    });
  });

  describe('calculateYearsOfExperience', () => {
    it('should correctly calculate years from experience array', () => {
      const experience = [
        {
          startDate: '2020-01-01',
          endDate: '2022-01-01',
        },
        {
          startDate: '2018-01-01',
          endDate: '2019-12-31',
        },
      ];

      const years = service['calculateYearsOfExperience'](experience);
      expect(years).toBe(4);
    });
  });
});
