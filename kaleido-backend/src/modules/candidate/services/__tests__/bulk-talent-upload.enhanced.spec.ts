import { Test, TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BulkTalentUploadService } from '../bulk-talent-upload.service';
import { CsvParserService } from '../csv-parser.service';
import { GroqService } from '../../../../shared/services/groq.service';
import { CandidateService } from '../../candidate.service';
import { Candidate } from '../../entities/candidate.entity';
import { Queue } from 'bull';

jest.mock('@/utils/file-extractor.util', () => ({
  extractFileContents: jest.fn().mockResolvedValue({
    contents: [
      {
        content:
          'This is a long extracted resume text content with more than 50 characters to pass the validation. It contains information about <PERSON>, a senior software engineer with 8 years of experience.',
      },
    ],
  }),
}));

describe('BulkTalentUploadService Enhanced Integration', () => {
  let service: BulkTalentUploadService;
  let groqService: GroqService;
  let csvParser: CsvParserService;
  let candidateService: CandidateService;
  let candidateRepository: Repository<Candidate>;
  let fileUploadQueue: Queue;

  const mockEnhancedExtractedData = {
    personalInfo: {
      fullName: 'John Doe',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '******-0123',
      location: 'San Francisco, CA',
    },
    professional: {
      currentJobTitle: 'Senior Software Engineer',
      currentCompany: 'TechCorp Inc.',
      summary: 'Experienced software engineer with 8 years building scalable web applications.',
      yearsOfExperience: 8,
      isCurrentlyEmployed: true,
    },
    skills: ['JavaScript', 'TypeScript', 'React', 'Node.js', 'Python', 'AWS'],
    experience: [
      {
        title: 'Senior Software Engineer',
        company: 'TechCorp Inc.',
        location: 'San Francisco, CA',
        startDate: '2022-01-01',
        endDate: null,
        description: 'Led development',
        responsibilities: ['Team leadership', 'Architecture design'],
        achievements: ['40% performance improvement'],
      },
    ],
    education: [
      {
        degree: 'BS Computer Science',
        institution: 'Stanford University',
        field: 'Computer Science',
        gpa: 3.8,
      },
    ],
    certifications: [
      {
        name: 'AWS Solutions Architect',
        issuer: 'Amazon',
        issueDate: '2023-01-01',
      },
    ],
    languages: ['English', 'Spanish'],
    urls: {
      linkedin: 'https://linkedin.com/in/johndoe',
      github: 'https://github.com/johndoe',
      portfolio: 'https://johndoe.dev',
    },
    preferences: {
      locations: ['San Francisco, CA', 'Remote'],
      remoteOnly: false,
      jobTypes: ['FULL_TIME'],
      industries: ['Technology'],
    },
    availability: {
      availableFrom: '2024-02-01',
      noticePeriod: '4 weeks',
    },
    compensation: {
      expectedMin: 200000,
      expectedMax: 250000,
      currency: 'USD',
    },
  };

  const mockTransformedData = {
    fullName: 'John Doe',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '******-0123',
    location: 'San Francisco, CA',
    jobTitle: 'Senior Software Engineer',
    currentCompany: 'TechCorp Inc.',
    yearsOfExperience: 8,
    summary: 'Experienced software engineer with 8 years building scalable web applications.',
    skills: ['JavaScript', 'TypeScript', 'React', 'Node.js', 'Python', 'AWS'],
    experience: mockEnhancedExtractedData.experience,
    linkedinUrl: 'https://linkedin.com/in/johndoe',
    githubUrl: 'https://github.com/johndoe',
    portfolioUrl: 'https://johndoe.dev',
    languages: ['English', 'Spanish'],
    certifications: mockEnhancedExtractedData.certifications,
    preferredLocation: 'San Francisco, CA',
    isRemoteOnly: false,
    availableFrom: '2024-02-01',
    isCurrentlyEmployed: true,
    salary: mockEnhancedExtractedData.compensation,
    projects: [],
    _rawExtractedData: mockEnhancedExtractedData,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BulkTalentUploadService,
        {
          provide: CsvParserService,
          useValue: {
            parseCandidatesCsv: jest.fn(),
          },
        },
        {
          provide: GroqService,
          useValue: {
            extractResumeData: jest.fn(),
            extractMultipleResumes: jest.fn(),
            isAvailable: jest.fn().mockReturnValue(true),
          },
        },
        {
          provide: CandidateService,
          useValue: {
            create: jest.fn(),
            findByEmail: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Candidate),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
          },
        },
        {
          provide: 'BullQueue_file-upload',
          useValue: {
            add: jest.fn().mockResolvedValue({ id: 'queue-job-id' }),
          },
        },
      ],
    }).compile();

    service = module.get<BulkTalentUploadService>(BulkTalentUploadService);
    groqService = module.get<GroqService>(GroqService);
    csvParser = module.get<CsvParserService>(CsvParserService);
    candidateService = module.get<CandidateService>(CandidateService);
    candidateRepository = module.get<Repository<Candidate>>(getRepositoryToken(Candidate));
    fileUploadQueue = module.get<Queue>('BullQueue_file-upload');
  });

  describe('processResumeFiles with Enhanced Extraction', () => {
    it('should extract enhanced data from resumes using Groq service', async () => {
      const mockFiles = [
        {
          originalname: 'resume1.pdf',
          mimetype: 'application/pdf',
          buffer: Buffer.from('resume content 1'),
        },
        {
          originalname: 'resume2.pdf',
          mimetype: 'application/pdf',
          buffer: Buffer.from('resume content 2'),
        },
      ] as Express.Multer.File[];

      jest.spyOn(groqService, 'extractResumeData').mockResolvedValue(mockTransformedData);

      jest.spyOn(service as any, 'checkDuplicate').mockResolvedValue(false);
      jest.spyOn(service as any, 'createCandidate').mockImplementation(async (data) => ({
        id: 'candidate-id',
        fullName: (data as any).fullName,
        email: (data as any).email,
      }));

      const results = {
        successCount: 0,
        errorCount: 0,
        errors: [],
        processedCandidates: [],
        duplicatesSkipped: 0,
      };

      await service['processResumeFiles'](mockFiles, 'client-id', 'job-id', results);

      expect(groqService.extractResumeData).toHaveBeenCalledTimes(2);
      expect(results.successCount).toBe(2);
      expect(results.processedCandidates).toHaveLength(2);
    });

    it('should properly map enhanced extracted data to ExtractedCandidateData', async () => {
      const mockFile = {
        originalname: 'resume.pdf',
        mimetype: 'application/pdf',
        buffer: Buffer.from('resume content'),
      } as Express.Multer.File;

      jest.spyOn(groqService, 'extractResumeData').mockResolvedValue(mockTransformedData);

      const extractedData = await service['processResumeFile'](mockFile);

      expect(extractedData).toMatchObject({
        fullName: 'John Doe',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '******-0123',
        location: 'San Francisco, CA',
        jobTitle: 'Senior Software Engineer',
        currentCompany: 'TechCorp Inc.',
        yearsOfExperience: 8,
        languages: ['English', 'Spanish'],
        preferredLocation: 'San Francisco, CA',
        isRemoteOnly: false,
        availableFrom: '2024-02-01',
      });

      // Verify salary mapping - the service returns the compensation as-is
      expect(extractedData.salary).toEqual({
        expectedMin: 200000,
        expectedMax: 250000,
        currency: 'USD',
      });
    });

    it('should handle languages array from enhanced extraction', async () => {
      const mockFile = {
        originalname: 'resume.pdf',
        mimetype: 'application/pdf',
        buffer: Buffer.from('resume content'),
      } as Express.Multer.File;

      const dataWithLanguages = {
        ...mockTransformedData,
        languages: ['English', 'Spanish', 'French'],
      };

      jest.spyOn(groqService, 'extractResumeData').mockResolvedValue(dataWithLanguages);

      const extractedData = await service['processResumeFile'](mockFile);

      expect(extractedData.languages).toEqual(['English', 'Spanish', 'French']);
    });

    it('should handle certifications from enhanced extraction', async () => {
      const mockFile = {
        originalname: 'resume.pdf',
        mimetype: 'application/pdf',
        buffer: Buffer.from('resume content'),
      } as Express.Multer.File;

      jest.spyOn(groqService, 'extractResumeData').mockResolvedValue(mockTransformedData);

      const extractedData = await service['processResumeFile'](mockFile);

      expect(extractedData.certifications).toBeDefined();
      // Note: certifications might be stored as strings if the DTO expects string[]
    });

    it('should handle availability data from enhanced extraction', async () => {
      const mockFile = {
        originalname: 'resume.pdf',
        mimetype: 'application/pdf',
        buffer: Buffer.from('resume content'),
      } as Express.Multer.File;

      jest.spyOn(groqService, 'extractResumeData').mockResolvedValue(mockTransformedData);

      const extractedData = await service['processResumeFile'](mockFile);

      expect(extractedData.availableFrom).toBe('2024-02-01');
    });

    it('should handle remote preference from enhanced extraction', async () => {
      const mockFile = {
        originalname: 'resume.pdf',
        mimetype: 'application/pdf',
        buffer: Buffer.from('resume content'),
      } as Express.Multer.File;

      const dataWithRemote = {
        ...mockTransformedData,
        isRemoteOnly: true,
      };

      jest.spyOn(groqService, 'extractResumeData').mockResolvedValue(dataWithRemote);

      const extractedData = await service['processResumeFile'](mockFile);

      expect(extractedData.isRemoteOnly).toBe(true);
    });

    it('should validate required fields from enhanced extraction', async () => {
      const mockFile = {
        originalname: 'resume.pdf',
        mimetype: 'application/pdf',
        buffer: Buffer.from('resume content'),
      } as Express.Multer.File;

      const incompleteData = {
        ...mockTransformedData,
        email: null,
        fullName: null,
      };

      jest.spyOn(groqService, 'extractResumeData').mockResolvedValue(incompleteData);

      await expect(service['processResumeFile'](mockFile)).rejects.toThrow(
        'Could not extract required information (email and name) from resume',
      );
    });

    it('should handle extraction failure gracefully', async () => {
      const mockFiles = [
        {
          originalname: 'resume.pdf',
          mimetype: 'application/pdf',
          buffer: Buffer.from('resume content'),
        },
      ] as Express.Multer.File[];

      jest
        .spyOn(groqService, 'extractResumeData')
        .mockRejectedValue(new Error('Groq extraction failed'));

      const results = {
        successCount: 0,
        errorCount: 0,
        errors: [],
        processedCandidates: [],
        duplicatesSkipped: 0,
      };

      await service['processResumeFiles'](mockFiles, 'client-id', 'job-id', results);

      expect(results.errorCount).toBe(1);
      expect(results.errors).toHaveLength(1);
      expect((results.errors[0] as any).error).toContain('Resume parsing failed');
    });
  });

  describe('processCsvFiles with Enhanced Data', () => {
    it('should process CSV candidates with enhanced fields', async () => {
      const mockCsvFile = {
        originalname: 'candidates.csv',
        mimetype: 'text/csv',
        buffer: Buffer.from('csv content'),
      } as Express.Multer.File;

      const mockCsvData = [
        {
          fullName: 'Jane Smith',
          email: '<EMAIL>',
          phone: '555-0987',
          jobTitle: 'Product Manager',
          location: 'New York, NY',
          skills: ['Product Management', 'Agile'],
          languages: ['English'],
          preferredLocation: 'New York, NY',
          isRemoteOnly: false,
          availableFrom: '2024-01-15',
        },
      ];

      jest.spyOn(csvParser, 'parseCandidatesCsv').mockResolvedValue(mockCsvData);
      jest.spyOn(service as any, 'checkDuplicate').mockResolvedValue(false);
      jest.spyOn(service as any, 'createCandidate').mockResolvedValue({
        id: 'candidate-id',
        fullName: 'Jane Smith',
        email: '<EMAIL>',
      });

      const results = {
        successCount: 0,
        errorCount: 0,
        errors: [],
        processedCandidates: [],
        duplicatesSkipped: 0,
      };

      await service['processCsvFiles']([mockCsvFile], 'client-id', 'job-id', results);

      expect(results.successCount).toBe(1);
      expect((results.processedCandidates[0] as any).fullName).toBe('Jane Smith');
    });
  });

  describe('createCandidate with Enhanced Data', () => {
    it('should create candidate with all enhanced fields', async () => {
      const enhancedData = {
        fullName: 'John Doe',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '******-0123',
        location: 'San Francisco, CA',
        jobTitle: 'Senior Software Engineer',
        currentCompany: 'TechCorp',
        yearsOfExperience: 8,
        summary: 'Experienced engineer',
        skills: ['JavaScript', 'React'],
        experience: [],
        education: [],
        linkedinUrl: 'https://linkedin.com/in/johndoe',
        githubUrl: 'https://github.com/johndoe',
        preferredLocation: 'San Francisco, CA',
        isRemoteOnly: false,
        salary: {
          min: 180000,
          max: 220000,
          currency: 'USD',
        },
        availableFrom: '2024-02-01',
        certifications: ['AWS Certified'],
        languages: ['English', 'Spanish'],
        projects: [],
      };

      const mockCandidate = {
        id: 'new-candidate-id',
        ...enhancedData,
      };

      jest.spyOn(candidateRepository, 'create').mockReturnValue(mockCandidate as any);
      jest.spyOn(candidateRepository, 'save').mockResolvedValue(mockCandidate as any);

      const result = await service['createCandidate'](
        enhancedData,
        'resume.pdf',
        'client-id',
        'job-id',
        'RESUME_UPLOAD',
      );

      expect(candidateRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          fullName: 'John Doe',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          jobTitle: 'Senior Software Engineer',
        }),
      );

      expect(result).toMatchObject({
        id: 'new-candidate-id',
        fullName: 'John Doe',
      });
    });
  });

  describe('processFilesDirectly with Enhanced Extraction', () => {
    it('should process mixed file types with enhanced extraction', async () => {
      const mockFiles = [
        {
          originalname: 'resume.pdf',
          mimetype: 'application/pdf',
          buffer: Buffer.from('resume content'),
        },
        {
          originalname: 'candidates.csv',
          mimetype: 'text/csv',
          buffer: Buffer.from('csv content'),
        },
      ] as Express.Multer.File[];

      jest.spyOn(groqService, 'extractResumeData').mockResolvedValue(mockTransformedData);
      jest.spyOn(csvParser, 'parseCandidatesCsv').mockResolvedValue([
        {
          fullName: 'CSV Candidate',
          email: '<EMAIL>',
        },
      ]);
      jest.spyOn(service as any, 'checkDuplicate').mockResolvedValue(false);
      jest.spyOn(service as any, 'createCandidate').mockImplementation(async (data) => ({
        id: 'candidate-id',
        fullName: (data as any).fullName,
        email: (data as any).email,
      }));

      const result = await service.processFilesDirectly(mockFiles, 'client-id', 'job-id');

      expect(result.successCount).toBe(2);
      expect(result.processedCandidates).toHaveLength(2);
      expect(groqService.extractResumeData).toHaveBeenCalled();
      expect(csvParser.parseCandidatesCsv).toHaveBeenCalled();
    });

    it('should skip duplicates based on email', async () => {
      const mockFiles = [
        {
          originalname: 'resume.pdf',
          mimetype: 'application/pdf',
          buffer: Buffer.from('resume content'),
        },
      ] as Express.Multer.File[];

      jest.spyOn(groqService, 'extractResumeData').mockResolvedValue(mockTransformedData);
      jest.spyOn(service as any, 'checkDuplicate').mockResolvedValue(true);

      const result = await service.processFilesDirectly(mockFiles, 'client-id', 'job-id');

      expect(result.duplicatesSkipped).toBe(1);
      expect(result.successCount).toBe(0);
    });
  });
});
