import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BulkTalentUploadService } from '../bulk-talent-upload.service';
import { CsvParserService } from '../csv-parser.service';
import { GroqService } from '../../../../shared/services/groq.service';
import { CandidateService } from '../../candidate.service';
import { Candidate, CandidateTier } from '../../entities/candidate.entity';
import { CandidateStatus } from '@/shared/types/candidate.types';
import { BadRequestException } from '@nestjs/common';
import { getQueueToken } from '@nestjs/bull';
import { QUEUE_NAMES } from '@/shared/constants/queue.constants';

// Mock the file extractor utility
jest.mock('@/utils/file-extractor.util', () => ({
  extractFileContents: jest.fn().mockImplementation(async (files) => ({
    contents: files.map((file: any) => ({
      content: 'Mock resume content <NAME_EMAIL> and name John Doe',
      filename: file.originalname,
    })),
    errors: [],
  })),
}));

describe('BulkTalentUploadService', () => {
  let service: BulkTalentUploadService;
  let candidateRepository: Repository<Candidate>;
  let csvParserService: CsvParserService;
  let groqService: GroqService;
  let candidateQueue: any;

  const mockCandidateRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockCsvParserService = {
    parseCandidatesCsv: jest.fn(),
    generateCsvTemplate: jest.fn(),
  };

  const mockGroqService = {
    extractResumeData: jest.fn(),
  };

  const mockCandidateService = {};

  const mockQueue = {
    add: jest.fn().mockResolvedValue({ id: 'queue-job-123' }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BulkTalentUploadService,
        {
          provide: getRepositoryToken(Candidate),
          useValue: mockCandidateRepository,
        },
        {
          provide: CsvParserService,
          useValue: mockCsvParserService,
        },
        {
          provide: GroqService,
          useValue: mockGroqService,
        },
        {
          provide: CandidateService,
          useValue: mockCandidateService,
        },
        {
          provide: getQueueToken(QUEUE_NAMES.FILE_UPLOAD),
          useValue: mockQueue,
        },
      ],
    }).compile();

    service = module.get<BulkTalentUploadService>(BulkTalentUploadService);
    candidateRepository = module.get<Repository<Candidate>>(getRepositoryToken(Candidate));
    csvParserService = module.get<CsvParserService>(CsvParserService);
    groqService = module.get<GroqService>(GroqService);
    candidateQueue = module.get(getQueueToken(QUEUE_NAMES.FILE_UPLOAD));

    // Reset all mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('processBulkUpload', () => {
    const mockClientId = 'client123';
    const mockJobId = 'job456';

    it('should process CSV files successfully', async () => {
      const mockCsvFile = {
        buffer: Buffer.from('csv content'),
        originalname: 'candidates.csv',
        mimetype: 'text/csv',
        size: 1000,
      } as Express.Multer.File;

      const mockCandidatesData = [
        {
          fullName: 'John Doe',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
        },
        {
          fullName: 'Jane Smith',
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Smith',
        },
      ];

      mockCsvParserService.parseCandidatesCsv.mockResolvedValue(mockCandidatesData);
      mockCandidateRepository.findOne.mockResolvedValue(null); // No duplicates
      mockCandidateRepository.create.mockImplementation((data) => ({
        ...data,
        id: `candidate-${Math.random()}`,
      }));
      mockCandidateRepository.save.mockImplementation((candidate) => Promise.resolve(candidate));

      const result = await service.processBulkUpload([mockCsvFile], mockClientId, mockJobId);

      // processBulkUpload now queues files instead of processing directly
      expect(result.success).toBe(true);
      expect(result.status).toBe('queued');
      expect(result.filesQueued).toBe(1);
      expect(mockQueue.add).toHaveBeenCalled();
    });

    it('should process resume files successfully', async () => {
      const mockResumeFile = {
        buffer: Buffer.from('resume content'),
        originalname: 'resume.pdf',
        mimetype: 'application/pdf',
        size: 2000,
      } as Express.Multer.File;

      const mockExtractedData = {
        fullName: 'John Doe',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        skills: ['JavaScript', 'React'],
        yearsOfExperience: 5,
      };

      mockGroqService.extractResumeData.mockResolvedValue(mockExtractedData);
      mockCandidateRepository.findOne.mockResolvedValue(null); // No duplicates
      mockCandidateRepository.create.mockImplementation((data) => ({
        ...data,
        id: 'candidate-123',
      }));
      mockCandidateRepository.save.mockImplementation((candidate) => Promise.resolve(candidate));

      const result = await service.processBulkUpload([mockResumeFile], mockClientId, mockJobId);

      // processBulkUpload now queues files instead of processing directly
      expect(result.success).toBe(true);
      expect(result.status).toBe('queued');
      expect(result.filesQueued).toBe(1);
      expect(result.queueJobId).toBe('queue-job-123');
      expect(mockQueue.add).toHaveBeenCalled();
    });

    it('should skip duplicate candidates', async () => {
      const mockCsvFile = {
        buffer: Buffer.from('csv content'),
        originalname: 'candidates.csv',
        mimetype: 'text/csv',
        size: 1000,
      } as Express.Multer.File;

      const mockCandidatesData = [
        {
          fullName: 'John Doe',
          email: '<EMAIL>',
        },
      ];

      mockCsvParserService.parseCandidatesCsv.mockResolvedValue(mockCandidatesData);
      mockCandidateRepository.findOne.mockResolvedValue({ id: 'existing-candidate' }); // Duplicate found

      const result = await service.processBulkUpload([mockCsvFile], mockClientId, mockJobId);

      // processBulkUpload now queues files instead of processing directly
      expect(result.success).toBe(true);
      expect(result.status).toBe('queued');
      expect(mockQueue.add).toHaveBeenCalled();
    });

    it('should queue large batches of resume files', async () => {
      const mockResumeFiles = Array.from({ length: 10 }, (_, i) => ({
        buffer: Buffer.from(`resume content ${i}`),
        originalname: `resume${i}.pdf`,
        mimetype: 'application/pdf',
        size: 2000,
      })) as Express.Multer.File[];

      mockQueue.add.mockResolvedValue({ id: 'job-id' });

      const result = await service.processBulkUpload(mockResumeFiles, mockClientId, mockJobId);

      expect(result.success).toBe(true);
      expect(result.status).toBe('queued');
      expect(result.filesQueued).toBe(10);
      expect(mockQueue.add).toHaveBeenCalled();
    });

    it('should handle mixed file types', async () => {
      const mockCsvFile = {
        buffer: Buffer.from('csv content'),
        originalname: 'candidates.csv',
        mimetype: 'text/csv',
        size: 1000,
      } as Express.Multer.File;

      const mockResumeFile = {
        buffer: Buffer.from('resume content'),
        originalname: 'resume.pdf',
        mimetype: 'application/pdf',
        size: 2000,
      } as Express.Multer.File;

      mockCsvParserService.parseCandidatesCsv.mockResolvedValue([
        { fullName: 'CSV Candidate', email: '<EMAIL>' },
      ]);

      mockGroqService.extractResumeData.mockResolvedValue({
        fullName: 'Resume Candidate',
        email: '<EMAIL>',
      });

      mockCandidateRepository.findOne.mockResolvedValue(null);
      mockCandidateRepository.create.mockImplementation((data) => ({ ...data, id: 'id' }));
      mockCandidateRepository.save.mockImplementation((candidate) => Promise.resolve(candidate));

      const result = await service.processBulkUpload(
        [mockCsvFile, mockResumeFile],
        mockClientId,
        mockJobId,
      );

      expect(result.success).toBe(true);
      expect(result.status).toBe('queued');
      expect(result.filesQueued).toBe(2);
      expect(mockQueue.add).toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      const mockCsvFile = {
        buffer: Buffer.from('csv content'),
        originalname: 'bad.csv',
        mimetype: 'text/csv',
        size: 1000,
      } as Express.Multer.File;

      mockCsvParserService.parseCandidatesCsv.mockRejectedValue(new Error('CSV parsing failed'));

      // Even with errors, files are queued for processing
      const result = await service.processBulkUpload([mockCsvFile], mockClientId, mockJobId);

      expect(result.success).toBe(true);
      expect(result.status).toBe('queued');
      expect(mockQueue.add).toHaveBeenCalled();
    });

    it('should reject uploads with more than 50 files', async () => {
      const mockFiles = Array.from({ length: 51 }, (_, i) => ({
        buffer: Buffer.from('content'),
        originalname: `file${i}.csv`,
        mimetype: 'text/csv',
        size: 1000,
      })) as Express.Multer.File[];

      await expect(service.processBulkUpload(mockFiles, mockClientId, mockJobId)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('exportCandidatesToCsv', () => {
    it('should export candidates to CSV format', async () => {
      const mockCandidates = [
        {
          fullName: 'John Doe',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          location: 'New York',
          jobTitle: 'Software Engineer',
          currentCompany: 'Tech Corp',
          yearsOfExperience: 5,
          skills: ['JavaScript', 'React'],
          linkedinUrl: 'https://linkedin.com/in/johndoe',
          githubUrl: 'https://github.com/johndoe',
          summary: 'Experienced developer',
          status: CandidateStatus.NEW,
          tier: CandidateTier.TOP,
          createdAt: new Date('2024-01-01'),
        },
      ];

      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(mockCandidates),
      };

      mockCandidateRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const csv = await service.exportCandidatesToCsv('client123', 'job456');

      expect(csv).toContain('fullName,firstName,lastName,email');
      expect(csv).toContain('John Doe,John,Doe,<EMAIL>');
      expect(csv).toContain('JavaScript;React');
      expect(csv).toContain(CandidateStatus.NEW);
      expect(csv).toContain(CandidateTier.TOP);
    });

    it('should handle empty results', async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
      };

      mockCandidateRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      await expect(service.exportCandidatesToCsv('client123')).rejects.toThrow(BadRequestException);
    });

    it('should escape CSV fields with special characters', async () => {
      const mockCandidates = [
        {
          fullName: 'John, Doe',
          email: '<EMAIL>',
          summary: 'Summary with "quotes" and\nnewlines',
          skills: ['Skill,1', 'Skill"2'],
        },
      ];

      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(mockCandidates),
      };

      mockCandidateRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const csv = await service.exportCandidatesToCsv('client123');

      expect(csv).toContain('"John, Doe"');
      expect(csv).toContain('"Summary with ""quotes"" and newlines"');
    });
  });

  describe('getFileType', () => {
    it('should identify CSV files correctly', () => {
      expect(service['getFileType']('data.csv')).toBe('csv');
      expect(service['getFileType']('data.xlsx')).toBe('csv');
      expect(service['getFileType']('data.xls')).toBe('csv');
      expect(service['getFileType']('DATA.CSV')).toBe('csv');
    });

    it('should identify resume files correctly', () => {
      expect(service['getFileType']('resume.pdf')).toBe('resume');
      expect(service['getFileType']('resume.doc')).toBe('resume');
      expect(service['getFileType']('resume.docx')).toBe('resume');
      expect(service['getFileType']('RESUME.PDF')).toBe('resume');
    });

    it('should throw error for unsupported file types', () => {
      expect(() => service['getFileType']('image.jpg')).toThrow('Unsupported file extension: jpg');
      expect(() => service['getFileType']('data.txt')).toThrow('Unsupported file extension: txt');
      expect(() => service['getFileType']('noextension')).toThrow(
        'Unsupported file extension: noextension',
      );
    });
  });
});
