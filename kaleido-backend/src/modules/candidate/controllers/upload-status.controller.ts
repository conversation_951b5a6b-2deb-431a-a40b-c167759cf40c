import { Controller, Get, Param, UseGuards, NotFoundException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { Auth0Guard } from '@/shared/guards/auth0.guard';
import { GetUser } from '@/shared/decorators/get-user.decorator';
import { QUEUE_NAMES } from '@/shared/constants/queue.constants';

interface JobStatusResponse {
  id: string;
  status: 'waiting' | 'active' | 'completed' | 'failed' | 'delayed' | 'paused';
  progress: number;
  result?: any;
  error?: string;
  processedOn?: number;
  finishedOn?: number;
  attemptsMade?: number;
}

interface BatchStatusResponse {
  batchId: string;
  jobs: JobStatusResponse[];
  totalJobs: number;
  completedJobs: number;
  failedJobs: number;
  activeJobs: number;
  overallProgress: number;
}

@ApiTags('Talent Upload Status')
@Controller('talent/upload-status')
@UseGuards(Auth0Guard)
@ApiBearerAuth()
export class UploadStatusController {
  constructor(
    @InjectQueue(QUEUE_NAMES.FILE_UPLOAD)
    private readonly fileUploadQueue: Queue,
  ) {}

  @Get(':jobId')
  @ApiOperation({ summary: 'Get upload job status' })
  @ApiResponse({ status: 200, description: 'Job status retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Job not found' })
  async getJobStatus(
    @Param('jobId') jobId: string,
    @GetUser() user: any,
  ): Promise<JobStatusResponse> {
    try {
      const job = await this.fileUploadQueue.getJob(jobId);

      if (!job) {
        throw new NotFoundException(`Job ${jobId} not found`);
      }

      // Verify the job belongs to the user
      if (job.data.clientId !== user.userId) {
        throw new NotFoundException(`Job ${jobId} not found`);
      }

      const state = await job.getState();
      const progress = job.progress();

      return {
        id: job.id.toString(),
        status: state as any,
        progress: typeof progress === 'number' ? progress : 0,
        result: job.returnvalue,
        error: job.failedReason,
        processedOn: job.processedOn,
        finishedOn: job.finishedOn,
        attemptsMade: job.attemptsMade,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException(`Failed to retrieve job status: ${error.message}`);
    }
  }

  @Get('batch/:batchId')
  @ApiOperation({ summary: 'Get batch upload status' })
  @ApiResponse({ status: 200, description: 'Batch status retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Batch not found' })
  async getBatchStatus(
    @Param('batchId') batchId: string,
    @GetUser() user: any,
  ): Promise<BatchStatusResponse> {
    try {
      // Get all jobs with the batch ID in their data
      const allJobs = await this.fileUploadQueue.getJobs([
        'waiting',
        'active',
        'completed',
        'failed',
        'delayed',
        'paused',
      ]);

      // Filter jobs belonging to this batch and user
      const batchJobs = allJobs.filter(
        (job) => job.data.batchId === batchId && job.data.clientId === user.userId,
      );

      if (batchJobs.length === 0) {
        throw new NotFoundException(`Batch ${batchId} not found`);
      }

      // Get status for each job
      const jobStatuses: JobStatusResponse[] = await Promise.all(
        batchJobs.map(async (job) => {
          const state = await job.getState();
          const progress = job.progress();

          return {
            id: job.id.toString(),
            status: state as any,
            progress: typeof progress === 'number' ? progress : 0,
            result: job.returnvalue,
            error: job.failedReason,
            processedOn: job.processedOn,
            finishedOn: job.finishedOn,
            attemptsMade: job.attemptsMade,
          };
        }),
      );

      // Calculate batch statistics
      const completedJobs = jobStatuses.filter((j) => j.status === 'completed').length;
      const failedJobs = jobStatuses.filter((j) => j.status === 'failed').length;
      const activeJobs = jobStatuses.filter((j) => j.status === 'active').length;

      // Calculate overall progress
      const totalProgress = jobStatuses.reduce((sum, job) => sum + job.progress, 0);
      const overallProgress = Math.round(totalProgress / jobStatuses.length);

      return {
        batchId,
        jobs: jobStatuses,
        totalJobs: jobStatuses.length,
        completedJobs,
        failedJobs,
        activeJobs,
        overallProgress,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException(`Failed to retrieve batch status: ${error.message}`);
    }
  }

  @Get('active')
  @ApiOperation({ summary: 'Get all active upload jobs for user' })
  @ApiResponse({ status: 200, description: 'Active jobs retrieved successfully' })
  async getActiveJobs(@GetUser() user: any): Promise<JobStatusResponse[]> {
    try {
      // Get all active and waiting jobs
      const jobs = await this.fileUploadQueue.getJobs(['waiting', 'active']);

      // Filter jobs belonging to the user
      const userJobs = jobs.filter((job) => job.data.clientId === user.userId);

      // Get status for each job
      const jobStatuses: JobStatusResponse[] = await Promise.all(
        userJobs.map(async (job) => {
          const state = await job.getState();
          const progress = job.progress();

          return {
            id: job.id.toString(),
            status: state as any,
            progress: typeof progress === 'number' ? progress : 0,
            result: job.returnvalue,
            error: job.failedReason,
            processedOn: job.processedOn,
            finishedOn: job.finishedOn,
            attemptsMade: job.attemptsMade,
          };
        }),
      );

      return jobStatuses;
    } catch (error) {
      return [];
    }
  }
}
