import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, ExecutionContext } from '@nestjs/common';
import { BulkUploadController } from '../bulk-upload.controller';
import { BulkTalentUploadService } from '../../services/bulk-talent-upload.service';
import { CsvParserService } from '../../services/csv-parser.service';
import { Auth0Guard } from '@/auth/auth.guard';

// Mock the Auth0Guard
jest.mock('@/auth/auth.guard', () => ({
  Auth0Guard: jest.fn().mockImplementation(() => ({
    canActivate: (context: ExecutionContext) => true,
  })),
}));

describe('BulkUploadController', () => {
  let controller: BulkUploadController;
  let bulkUploadService: BulkTalentUploadService;
  let csvParserService: CsvParserService;

  const mockBulkUploadService = {
    processBulkUpload: jest.fn(),
    exportCandidatesToCsv: jest.fn(),
  };

  const mockCsvParserService = {
    generateCsvTemplate: jest.fn(),
  };

  const mockRequest = {
    user: {
      id: 'user123',
      userId: 'user123',
      clientId: 'client456',
    },
    body: {},
  };

  const mockResponse = {
    header: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BulkUploadController],
      providers: [
        {
          provide: BulkTalentUploadService,
          useValue: mockBulkUploadService,
        },
        {
          provide: CsvParserService,
          useValue: mockCsvParserService,
        },
      ],
    }).compile();

    controller = module.get<BulkUploadController>(BulkUploadController);
    bulkUploadService = module.get<BulkTalentUploadService>(BulkTalentUploadService);
    csvParserService = module.get<CsvParserService>(CsvParserService);

    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('uploadTalent', () => {
    it('should process valid file uploads successfully', async () => {
      const mockFiles = [
        {
          buffer: Buffer.from('content'),
          originalname: 'candidates.csv',
          mimetype: 'text/csv',
          size: 1000,
        },
        {
          buffer: Buffer.from('resume'),
          originalname: 'resume.pdf',
          mimetype: 'application/pdf',
          size: 2000,
        },
      ] as Express.Multer.File[];

      const mockResult = {
        success: true,
        message: 'Queued 2 files for processing',
        queueJobId: 'queue-job-123',
        jobId: 'queue-job-123',
        status: 'queued',
        filesQueued: 2,
      };

      mockBulkUploadService.processBulkUpload.mockResolvedValue(mockResult);

      const result = await controller.uploadTalent(mockFiles, {
        ...mockRequest,
        body: { jobId: 'job789' },
      });

      expect(result).toEqual(mockResult);

      expect(bulkUploadService.processBulkUpload).toHaveBeenCalledWith(
        mockFiles,
        'user123', // The controller now uses userId first
        'job789',
      );
    });

    it('should throw error when no files are provided', async () => {
      await expect(controller.uploadTalent([], mockRequest)).rejects.toThrow(
        new BadRequestException('No files provided'),
      );
    });

    it('should throw error for invalid file extensions', async () => {
      const mockFiles = [
        {
          buffer: Buffer.from('content'),
          originalname: 'image.jpg',
          mimetype: 'image/jpeg',
          size: 1000,
        },
      ] as Express.Multer.File[];

      await expect(controller.uploadTalent(mockFiles, mockRequest)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw error for oversized files', async () => {
      const mockFiles = [
        {
          buffer: Buffer.from('content'),
          originalname: 'huge.pdf',
          mimetype: 'application/pdf',
          size: 25 * 1024 * 1024, // 25MB
        },
      ] as Express.Multer.File[];

      await expect(controller.uploadTalent(mockFiles, mockRequest)).rejects.toThrow(
        new BadRequestException('File huge.pdf exceeds maximum size of 20MB'),
      );
    });

    it('should handle processing errors gracefully', async () => {
      const mockFiles = [
        {
          buffer: Buffer.from('content'),
          originalname: 'candidates.csv',
          mimetype: 'text/csv',
          size: 1000,
        },
      ] as Express.Multer.File[];

      mockBulkUploadService.processBulkUpload.mockRejectedValue(new Error('Processing failed'));

      await expect(controller.uploadTalent(mockFiles, mockRequest)).rejects.toThrow(
        new BadRequestException('Upload processing failed: Processing failed'),
      );
    });

    it('should use user id as clientId when clientId is not available', async () => {
      const mockFiles = [
        {
          buffer: Buffer.from('content'),
          originalname: 'candidates.csv',
          mimetype: 'text/csv',
          size: 1000,
        },
      ] as Express.Multer.File[];

      const requestWithoutClientId = {
        user: { id: 'user123' },
        body: {},
      };

      mockBulkUploadService.processBulkUpload.mockResolvedValue({
        successCount: 1,
        errorCount: 0,
        errors: [],
        processedCandidates: [],
        duplicatesSkipped: 0,
      });

      await controller.uploadTalent(mockFiles, requestWithoutClientId);

      expect(bulkUploadService.processBulkUpload).toHaveBeenCalledWith(
        mockFiles,
        'user123',
        undefined,
      );
    });
  });

  describe('exportCandidates', () => {
    it('should export candidates to CSV successfully', async () => {
      const csvContent = `fullName,email
John Doe,<EMAIL>
Jane Smith,<EMAIL>`;

      mockBulkUploadService.exportCandidatesToCsv.mockResolvedValue(csvContent);

      const result = await controller.exportCandidates('job123', mockRequest, mockResponse as any);

      expect(mockResponse.header).toHaveBeenCalledWith('Content-Type', 'text/csv');
      expect(mockResponse.header).toHaveBeenCalledWith(
        'Content-Disposition',
        expect.stringContaining('attachment; filename="candidates_job123_'),
      );
      expect(mockResponse.send).toHaveBeenCalledWith(csvContent);
      expect(bulkUploadService.exportCandidatesToCsv).toHaveBeenCalledWith('user123', 'job123');
    });

    it('should export all candidates when no jobId is provided', async () => {
      const csvContent = 'fullName,email\nJohn Doe,<EMAIL>';

      mockBulkUploadService.exportCandidatesToCsv.mockResolvedValue(csvContent);

      await controller.exportCandidates(undefined as any, mockRequest, mockResponse as any);

      expect(mockResponse.header).toHaveBeenCalledWith(
        'Content-Disposition',
        expect.stringContaining('attachment; filename="candidates_all_'),
      );
      expect(bulkUploadService.exportCandidatesToCsv).toHaveBeenCalledWith('user123', undefined);
    });

    it('should handle export errors gracefully', async () => {
      mockBulkUploadService.exportCandidatesToCsv.mockRejectedValue(new Error('Export failed'));

      await expect(
        controller.exportCandidates('job123', mockRequest, mockResponse as any),
      ).rejects.toThrow(new BadRequestException('Export failed: Export failed'));
    });
  });

  describe('downloadCsvTemplate', () => {
    it('should return CSV template successfully', async () => {
      const templateContent = `fullName,firstName,lastName,email,phone
John Doe,John,Doe,<EMAIL>,+1234567890`;

      mockCsvParserService.generateCsvTemplate.mockReturnValue(templateContent);

      const result = await controller.downloadCsvTemplate(mockResponse as any);

      expect(mockResponse.header).toHaveBeenCalledWith('Content-Type', 'text/csv');
      expect(mockResponse.header).toHaveBeenCalledWith(
        'Content-Disposition',
        'attachment; filename="candidate_upload_template.csv"',
      );
      expect(mockResponse.send).toHaveBeenCalledWith(templateContent);
      expect(csvParserService.generateCsvTemplate).toHaveBeenCalled();
    });
  });
});
