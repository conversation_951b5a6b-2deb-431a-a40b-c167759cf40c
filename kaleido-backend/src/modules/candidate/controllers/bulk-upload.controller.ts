import {
  Controller,
  Post,
  Get,
  UseInterceptors,
  UploadedFiles,
  BadRequestException,
  Res,
  Query,
  UseGuards,
  Req,
} from '@nestjs/common';
import { FastifyReply } from 'fastify';
import { BulkTalentUploadService } from '../services/bulk-talent-upload.service';
import { CsvParserService } from '../services/csv-parser.service';
import { Auth0Guard } from '@/auth/auth.guard';
import { FastifyFilesInterceptor } from '@/shared/interceptors/fastify-files.interceptor';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBody } from '@nestjs/swagger';

@ApiTags('Talent Bulk Upload')
@Controller('talent')
@UseGuards(Auth0Guard)
export class BulkUploadController {
  constructor(
    private readonly bulkUploadService: BulkTalentUploadService,
    private readonly csvParserService: CsvParserService,
  ) {}

  @Post('bulk-upload')
  @UseInterceptors(new FastifyFilesInterceptor('files', 50))
  @ApiOperation({ summary: 'Upload multiple candidate files (CSV, PDF, DOCX)' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
        jobId: {
          type: 'string',
          description: 'Optional job ID to associate candidates with',
        },
      },
    },
  })
  @ApiResponse({ status: 200, description: 'Files processed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid file format or processing error' })
  async uploadTalent(@UploadedFiles() files: Express.Multer.File[], @Req() req: any) {
    if (!files || files.length === 0) {
      throw new BadRequestException('No files provided');
    }

    // Get clientId from authenticated user - use userId for consistency with status endpoint
    const clientId = req.user.userId || req.user.sub || req.user.id;
    const jobId = req.body?.jobId;

    // Validate file types
    const allowedExtensions = ['pdf', 'doc', 'docx', 'csv', 'xlsx', 'xls'];
    for (const file of files) {
      const ext = file.originalname.toLowerCase().split('.').pop() || '';
      if (!ext || !allowedExtensions.includes(ext)) {
        throw new BadRequestException(
          `File ${file.originalname} has unsupported format. Allowed: ${allowedExtensions.join(', ')}`,
        );
      }

      // Check file size (max 20MB per file)
      if (file.size > 20 * 1024 * 1024) {
        throw new BadRequestException(`File ${file.originalname} exceeds maximum size of 20MB`);
      }
    }

    try {
      const result = await this.bulkUploadService.processBulkUpload(files, clientId, jobId);

      // Return the result directly as it already has the correct format
      return result;
    } catch (error) {
      throw new BadRequestException(`Upload processing failed: ${error.message}`);
    }
  }

  @Get('export-csv')
  @ApiOperation({ summary: 'Export candidates to CSV format' })
  @ApiResponse({
    status: 200,
    description: 'CSV file with candidate data',
    content: {
      'text/csv': {
        schema: {
          type: 'string',
        },
      },
    },
  })
  async exportCandidates(@Query('jobId') jobId: string, @Req() req: any, @Res() res: FastifyReply) {
    const clientId = req.user.userId || req.user.sub || req.user.id;

    try {
      const csvContent = await this.bulkUploadService.exportCandidatesToCsv(clientId, jobId);

      const filename = `candidates_${jobId || 'all'}_${new Date().toISOString().split('T')[0]}.csv`;

      res.header('Content-Type', 'text/csv');
      res.header('Content-Disposition', `attachment; filename="${filename}"`);

      return res.send(csvContent);
    } catch (error) {
      throw new BadRequestException(`Export failed: ${error.message}`);
    }
  }

  @Get('csv-template')
  @ApiOperation({ summary: 'Download CSV template for bulk upload' })
  @ApiResponse({
    status: 200,
    description: 'CSV template file',
    content: {
      'text/csv': {
        schema: {
          type: 'string',
        },
      },
    },
  })
  async downloadCsvTemplate(@Res() res: FastifyReply) {
    const template = this.csvParserService.generateCsvTemplate();

    res.header('Content-Type', 'text/csv');
    res.header('Content-Disposition', 'attachment; filename="candidate_upload_template.csv"');

    return res.send(template);
  }
}
