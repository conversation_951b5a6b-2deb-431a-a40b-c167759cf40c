import { Auth0Guard } from '@/auth/auth.guard';
import { UserRole } from '@/common/enums/role.enum';
import { Job } from '@/modules/job/entities/job.entity';
import { GetUser, User } from '@/shared/decorators/get-user.decorator';
import { Roles } from '@/shared/decorators/roles.decorator';
import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateReferralDto, UpdateReferralStatusDto } from '../dto';
import { Referral, ReferralStatus } from '../entities/referral.entity';
import { ReferralService } from '../services/referral.service';

@ApiTags('Referrals')
@Controller('referrals')
export class ReferralController {
  constructor(
    private readonly referralService: ReferralService,
    @InjectRepository(Job)
    private jobRepository: Repository<Job>,
  ) {}

  @Post('track')
  @ApiOperation({ summary: 'Track a referral (public endpoint)' })
  @ApiResponse({ status: HttpStatus.CREATED, type: Referral })
  async trackReferral(@Body() createDto: CreateReferralDto): Promise<Referral> {
    return this.referralService.create(createDto);
  }

  @Get()
  @UseGuards(Auth0Guard)
  @Roles(UserRole.ADMIN, UserRole.EMPLOYER, UserRole.REFERRAL_PARTNER)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all referrals with filters' })
  @ApiQuery({ name: 'referralPartnerId', required: false })
  @ApiQuery({ name: 'candidateId', required: false })
  @ApiQuery({ name: 'jobId', required: false })
  @ApiQuery({ name: 'status', required: false, enum: ReferralStatus })
  @ApiResponse({ status: HttpStatus.OK, type: [Referral] })
  async findAll(
    @GetUser() user: User,
    @Query('referralPartnerId') referralPartnerId?: string,
    @Query('candidateId') candidateId?: string,
    @Query('jobId') jobId?: string,
    @Query('status') status?: ReferralStatus,
  ): Promise<Referral[]> {
    // If referralPartnerId is provided, check if it's a clientId that needs conversion
    let actualPartnerId = referralPartnerId;

    if (referralPartnerId) {
      // Check if this looks like a clientId (not a UUID format)
      // Try to find the partner by clientId first
      const partner =
        await this.referralService.referralPartnerService.findByClientId(referralPartnerId);
      if (partner) {
        actualPartnerId = partner.id;
      }
      // Otherwise assume it's already a partner UUID
    }

    return this.referralService.findAll({
      referralPartnerId: actualPartnerId,
      candidateId,
      jobId,
      status,
    });
  }

  @Get(':id')
  @UseGuards(Auth0Guard)
  @Roles(UserRole.ADMIN, UserRole.EMPLOYER, UserRole.REFERRAL_PARTNER)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a referral by ID' })
  @ApiResponse({ status: HttpStatus.OK, type: Referral })
  async findOne(@Param('id') id: string): Promise<Referral> {
    return this.referralService.findOne(id);
  }

  @Put(':id/status')
  @UseGuards(Auth0Guard)
  @Roles(UserRole.ADMIN, UserRole.EMPLOYER)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update referral status' })
  @ApiResponse({ status: HttpStatus.OK, type: Referral })
  async updateStatus(
    @Param('id') id: string,
    @Body() updateDto: UpdateReferralStatusDto,
  ): Promise<Referral> {
    return this.referralService.updateStatus(id, updateDto);
  }

  @Get('jobs/available')
  @UseGuards(Auth0Guard)
  @Roles(UserRole.REFERRAL_PARTNER, UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get jobs available for referral' })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'offset', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'sort', required: false, type: String })
  @ApiQuery({ name: 'order', required: false, enum: ['ASC', 'DESC'] })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Returns only published jobs that accept referrals',
  })
  async getAvailableJobs(
    @GetUser() user: User,
    @Query('limit') limit: number = 10,
    @Query('offset') offset: number = 0,
    @Query('search') search?: string,
    @Query('sort') sort: string = 'createdAt',
    @Query('order') order: 'ASC' | 'DESC' = 'DESC',
  ): Promise<any> {
    // Use query builder for better performance with selective fields
    const queryBuilder = this.jobRepository
      .createQueryBuilder('job')
      .leftJoin('job.company', 'company')
      .addSelect(['company.id', 'company.companyName', 'company.logo'])
      .where('job.isPublished = :isPublished', { isPublished: true });

    // Add search functionality if search parameter is provided
    if (search) {
      queryBuilder.andWhere(
        '(LOWER(job.jobType) LIKE LOWER(:search) OR LOWER(job.department) LIKE LOWER(:search) OR LOWER(job.companyName) LIKE LOWER(:search) OR EXISTS (SELECT 1 FROM unnest(job.location) AS loc WHERE LOWER(loc) LIKE LOWER(:search)))',
        { search: `%${search}%` },
      );
    }

    // Get total count before applying pagination
    const total = await queryBuilder.getCount();

    // Apply sorting and pagination
    queryBuilder.orderBy(`job.${sort}`, order).skip(Number(offset)).take(Number(limit));

    const jobs = await queryBuilder.getMany();

    // Transform jobs to include only necessary fields for referral partners
    const transformedJobs = jobs.map((job) => ({
      id: job.id,
      jobType: job.jobType,
      department: job.department,
      companyName: job.company?.companyName || 'Unknown Company',
      location: job.location,
      salaryRange: job.salaryRange,
      experienceLevel: job.experienceLevel,
      typeOfJob: job.typeOfJob,
      isPublished: job.isPublished,
      slug: job.slug,
      createdAt: job.createdAt,
      referralSettings: job.referralSettings || {
        acceptsReferrals: true,
        bountyConfiguration: {
          type: 'PERCENTAGE',
          value: 10,
        },
      },
      metrics: job.metrics || { views: 0, applications: 0 },
      company: job.company
        ? {
            id: job.company.id,
            companyName: job.company.companyName,
            logo: job.company.logo,
          }
        : null,
    }));

    return {
      jobs: transformedJobs,
      total,
      limit: Number(limit),
      offset: Number(offset),
    };
  }
}
