import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  UseGuards,
  Query,
  HttpCode,
  HttpStatus,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Auth0Guard } from '@/auth/auth.guard';
import { Roles } from '@/shared/decorators/roles.decorator';
import { GetUser, User } from '@/shared/decorators/get-user.decorator';
import { UserRole } from '@/common/enums/role.enum';
import { ReferralPartnerService } from '../services/referral-partner.service';
import { ReferralService } from '../services/referral.service';
import { CreateReferralPartnerDto, UpdateReferralPartnerDto } from '../dto';
import { ReferralPartner } from '../entities/referral-partner.entity';
import { Referral } from '../entities/referral.entity';

@ApiTags('Referral Partners')
@Controller('referral-partners')
@UseGuards(Auth0Guard)
@ApiBearerAuth()
export class ReferralPartnerController {
  constructor(
    private readonly referralPartnerService: ReferralPartnerService,
    @Inject(forwardRef(() => ReferralService))
    private readonly referralService: ReferralService,
  ) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.EMPLOYER)
  @ApiOperation({ summary: 'Create a new referral partner' })
  @ApiResponse({ status: HttpStatus.CREATED, type: ReferralPartner })
  async create(
    @Body() createDto: CreateReferralPartnerDto,
    @GetUser() user: User,
  ): Promise<ReferralPartner> {
    return this.referralPartnerService.create(createDto, user.companyId || user.userId);
  }

  @Get()
  @Roles(UserRole.ADMIN, UserRole.EMPLOYER)
  @ApiOperation({ summary: 'Get all referral partners' })
  @ApiResponse({ status: HttpStatus.OK, type: [ReferralPartner] })
  async findAll(@Query('companyId') companyId?: string): Promise<ReferralPartner[]> {
    return this.referralPartnerService.findAll(companyId);
  }

  @Get('me')
  @Roles(UserRole.REFERRAL_PARTNER, UserRole.ADMIN, UserRole.EMPLOYER)
  @ApiOperation({ summary: 'Get current user referral partner profile' })
  @ApiResponse({ status: HttpStatus.OK, type: ReferralPartner })
  async getMyProfile(@GetUser() user: User): Promise<ReferralPartner | null> {
    const clientId = user.sub || user.userId;
    let partner = await this.referralPartnerService.findByClientId(clientId);

    // Auto-create referral partner if doesn't exist
    if (!partner && user.roles?.includes(UserRole.REFERRAL_PARTNER)) {
      const createDto: CreateReferralPartnerDto = {
        partnerName:
          user.name ||
          user.fullName ||
          `${user.given_name || ''} ${user.family_name || ''}`.trim() ||
          user.email,
        contactEmail: user.email,
        contactPhone: user.phoneNumber,
        companyId: user.companyId,
      };
      partner = await this.referralPartnerService.create(createDto, clientId);
    }

    return partner;
  }

  @Get('check-status')
  @ApiOperation({ summary: 'Check if current user is a referral partner' })
  @ApiResponse({
    status: HttpStatus.OK,
    schema: {
      properties: {
        isActive: { type: 'boolean' },
        partner: { type: 'object', nullable: true },
      },
    },
  })
  async checkStatus(
    @GetUser() user: User,
  ): Promise<{ isActive: boolean; partner?: ReferralPartner | null }> {
    const clientId = user.sub || user.userId;
    let partner = await this.referralPartnerService.findByClientId(clientId);

    // Auto-create referral partner if user has the role but no partner record exists
    if (!partner && user.roles?.includes(UserRole.REFERRAL_PARTNER)) {
      const createDto: CreateReferralPartnerDto = {
        partnerName:
          user.name ||
          user.fullName ||
          `${user.given_name || ''} ${user.family_name || ''}`.trim() ||
          user.email,
        contactEmail: user.email,
        contactPhone: user.phoneNumber,
        companyId: user.companyId,
      };
      try {
        partner = await this.referralPartnerService.create(createDto, clientId);
      } catch (error) {
        console.error('Failed to auto-create referral partner:', error);
      }
    }

    return {
      isActive: !!partner && partner.isActive,
      partner: partner,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a referral partner by ID' })
  @ApiResponse({ status: HttpStatus.OK, type: ReferralPartner })
  async findOne(@Param('id') id: string): Promise<ReferralPartner> {
    return this.referralPartnerService.findOne(id);
  }

  @Put(':id')
  @Roles(UserRole.ADMIN, UserRole.EMPLOYER, UserRole.REFERRAL_PARTNER)
  @ApiOperation({ summary: 'Update a referral partner' })
  @ApiResponse({ status: HttpStatus.OK, type: ReferralPartner })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateReferralPartnerDto,
  ): Promise<ReferralPartner> {
    return this.referralPartnerService.update(id, updateDto);
  }

  @Put(':id/deactivate')
  @Roles(UserRole.ADMIN, UserRole.EMPLOYER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Deactivate a referral partner' })
  async deactivate(@Param('id') id: string): Promise<void> {
    return this.referralPartnerService.deactivate(id);
  }

  @Put(':id/activate')
  @Roles(UserRole.ADMIN, UserRole.EMPLOYER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Activate a referral partner' })
  async activate(@Param('id') id: string): Promise<void> {
    return this.referralPartnerService.activate(id);
  }

  @Get('me/referrals')
  @Roles(UserRole.REFERRAL_PARTNER, UserRole.ADMIN)
  @ApiOperation({ summary: "Get current partner's referrals" })
  @ApiResponse({ status: HttpStatus.OK, type: [Referral] })
  async getMyReferrals(@GetUser() user: User): Promise<Referral[]> {
    const clientId = user.sub || user.userId;
    const partner = await this.referralPartnerService.findByClientId(clientId);

    if (!partner) {
      return [];
    }

    return this.referralService.findAll({
      referralPartnerId: partner.id, // Use the UUID id, not clientId
    });
  }

  @Get(':id/dashboard')
  @Roles(UserRole.ADMIN, UserRole.EMPLOYER, UserRole.REFERRAL_PARTNER)
  @ApiOperation({ summary: 'Get referral partner dashboard data' })
  async getDashboard(@Param('id') id: string): Promise<any> {
    const partner = await this.referralPartnerService.findOne(id);
    return {
      partner,
      metrics: partner.dashboardMetrics,
      earnings: {
        total: partner.totalEarnings,
        pending: partner.pendingEarnings,
        paid: partner.paidEarnings,
      },
    };
  }
}
