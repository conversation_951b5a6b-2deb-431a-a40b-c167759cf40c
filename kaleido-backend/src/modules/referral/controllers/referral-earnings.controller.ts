import {
  Controller,
  Get,
  Post,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
  Body,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Auth0Guard } from '@/auth/auth.guard';
import { Roles } from '@/shared/decorators/roles.decorator';
import { GetUser, User } from '@/shared/decorators/get-user.decorator';
import { UserRole } from '@/common/enums/role.enum';
import { ReferralService } from '../services/referral.service';
import { ReferralPartnerService } from '../services/referral-partner.service';

@ApiTags('Referral Earnings')
@Controller('referral-partners')
@UseGuards(Auth0Guard)
@ApiBearerAuth()
export class ReferralEarningsController {
  constructor(
    private readonly referralService: ReferralService,
    private readonly referralPartnerService: ReferralPartnerService,
  ) {}

  @Get('me/earnings')
  @Roles(UserRole.REFERRAL_PARTNER, UserRole.ADMIN)
  @ApiOperation({ summary: 'Get current partner earnings summary' })
  async getMyEarnings(@GetUser() user: User): Promise<any> {
    const clientId = user.sub || user.userId;
    const partner = await this.referralPartnerService.findByClientId(clientId);

    if (!partner) {
      return {
        totalEarnings: 0,
        pendingEarnings: 0,
        paidEarnings: 0,
        referrals: [],
      };
    }

    return this.referralService.getPartnerEarnings(partner.id);
  }

  @Get(':partnerId/earnings')
  @Roles(UserRole.ADMIN, UserRole.EMPLOYER, UserRole.REFERRAL_PARTNER)
  @ApiOperation({ summary: 'Get partner earnings summary' })
  async getEarnings(@Param('partnerId') partnerId: string, @GetUser() user: User): Promise<any> {
    // TODO: Add authorization check to ensure user can access this partner's data
    return this.referralService.getPartnerEarnings(partnerId);
  }

  @Get(':partnerId/payments')
  @Roles(UserRole.ADMIN, UserRole.EMPLOYER, UserRole.REFERRAL_PARTNER)
  @ApiOperation({ summary: 'Get payment history' })
  async getPaymentHistory(@Param('partnerId') partnerId: string): Promise<any> {
    // TODO: Implement payment history tracking
    return {
      payments: [],
      totalPaid: 0,
    };
  }

  @Post(':partnerId/request-payment')
  @Roles(UserRole.ADMIN, UserRole.EMPLOYER, UserRole.REFERRAL_PARTNER)
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({ summary: 'Request payment of pending earnings' })
  async requestPayment(
    @Param('partnerId') partnerId: string,
    @GetUser() user: User,
    @Body() body: { amount: number; paymentMethod?: string },
  ): Promise<any> {
    // TODO: Implement payment request logic
    // This would typically create a payment request record and trigger approval workflow
    return {
      message: 'Payment request submitted',
      requestId: 'req_' + Date.now(),
      amount: body.amount,
      status: 'PENDING_APPROVAL',
    };
  }

  @Get(':partnerId/reports/summary')
  @Roles(UserRole.ADMIN, UserRole.EMPLOYER, UserRole.REFERRAL_PARTNER)
  @ApiOperation({ summary: 'Get earnings summary report' })
  async getSummaryReport(@Param('partnerId') partnerId: string): Promise<any> {
    const partner = await this.referralPartnerService.findOne(partnerId);
    const earnings = await this.referralService.getPartnerEarnings(partnerId);

    return {
      partner: {
        id: partner.id,
        name: partner.partnerName,
        referralCode: partner.referralCode,
      },
      metrics: partner.dashboardMetrics,
      earnings: {
        total: earnings.totalEarnings,
        pending: earnings.pendingEarnings,
        paid: earnings.paidEarnings,
      },
      referralsSummary: {
        total: earnings.referrals.length,
        byStatus: earnings.referrals.reduce(
          (acc, ref) => {
            acc[ref.status] = (acc[ref.status] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>,
        ),
      },
    };
  }

  @Get(':partnerId/reports/detailed')
  @Roles(UserRole.ADMIN, UserRole.EMPLOYER, UserRole.REFERRAL_PARTNER)
  @ApiOperation({ summary: 'Get detailed earnings report' })
  async getDetailedReport(@Param('partnerId') partnerId: string): Promise<any> {
    const earnings = await this.referralService.getPartnerEarnings(partnerId);

    return {
      referrals: earnings.referrals,
      earnings: {
        total: earnings.totalEarnings,
        pending: earnings.pendingEarnings,
        paid: earnings.paidEarnings,
      },
    };
  }
}
