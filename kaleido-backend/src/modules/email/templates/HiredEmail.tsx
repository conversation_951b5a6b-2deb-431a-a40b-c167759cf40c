import * as React from 'react';
import { EnhancedEmailLayout } from './components/EnhancedEmailLayout';
import {
  EmailHeading,
  EmailText,
  Button,
  Card,
  Divider,
  InfoRow,
  CTASection,
  IconText,
  ContactInfo,
  ContactInfoProps,
} from './components/EmailComponents';

interface HiredEmailProps {
  candidateName: string;
  jobTitle: string;
  companyName: string;
  startDate?: string;
  onboardingLink?: string;
  contactInfo?: ContactInfoProps;
}

export const HiredEmail: React.FC<HiredEmailProps> = ({
  candidateName,
  jobTitle,
  companyName,
  startDate,
  onboardingLink,
  contactInfo,
}) => {
  const preview = `Welcome to ${companyName}!`;

  return (
    <EnhancedEmailLayout preview={preview}>
      <EmailHeading level={1} emoji="🎉">
        Welcome to {companyName}!
      </EmailHeading>
      
      <EmailText variant="large">Dear {candidateName},</EmailText>
      
      <EmailText>
        Congratulations! We are thrilled to officially welcome you to <strong>{companyName}</strong> as our new <strong>{jobTitle}</strong>.
        We believe your skills and experience will be a valuable addition to our team.
      </EmailText>
      
      {startDate && (
        <Card variant="highlight">
          <InfoRow icon="📅" label="Your start date" value={startDate} />
        </Card>
      )}
      
      {onboardingLink && (
        <CTASection
          description="To help you get started, please complete our onboarding process using the link below:"
          buttonText="Start Onboarding"
          buttonHref={onboardingLink}
        />
      )}
      
      <EmailText>
        In the coming days, you will receive additional information about your first day,
        including orientation details and necessary paperwork.
      </EmailText>
      
      <Divider />
      
      <Card variant="info">
        <IconText 
          icon="💬" 
          text="If you have any questions before your start date, please don't hesitate to reach out to our HR team."
        />
      </Card>
      
      <EmailText>
        We're looking forward to having you on board!
      </EmailText>
      
      <ContactInfo {...contactInfo} companyName={companyName} />
    </EnhancedEmailLayout>
  );
};

export default HiredEmail;