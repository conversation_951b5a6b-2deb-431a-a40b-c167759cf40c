import * as React from 'react';
import { EnhancedEmailLayout } from './components/EnhancedEmailLayout';
import {
  EmailHeading,
  EmailText,
  Card,
  Button,
  Divider,
  InfoRow,
  CTASection,
  TwoColumn,
  ContactInfo,
  ContactInfoProps,
} from './components/EmailComponents';
import { colors } from './components';

interface OfferEmailProps {
  candidateName: string;
  jobTitle: string;
  companyName?: string;
  offerLetterUrl?: string;
  startDate?: string;
  salary?: {
    amount: number;
    currency: string;
    period: string;
  };
  offerExpirationDate?: string;
  responseLink?: string;
  contactInfo?: ContactInfoProps;
}

export const OfferEmail: React.FC<OfferEmailProps> = ({
  candidateName,
  jobTitle,
  companyName = 'Kaleido Talent',
  offerLetterUrl,
  startDate,
  salary,
  offerExpirationDate,
  responseLink,
  contactInfo,
}) => {
  const preview = `Job Offer for ${jobTitle} position at ${companyName}`;
  
  // Format salary if provided
  const formattedSalary = salary 
    ? new Intl.NumberFormat('en-US', { 
        style: 'currency', 
        currency: salary.currency || 'USD',
        maximumFractionDigits: 0 
      }).format(salary.amount) + (salary.period ? ` ${salary.period}` : '')
    : null;
  
  return (
    <EnhancedEmailLayout preview={preview}>
      <EmailHeading level={1} emoji="🎉">
        Congratulations! Your Job Offer from {companyName}
      </EmailHeading>
      
      <EmailText>
        Dear {candidateName},
      </EmailText>
      
      <Card variant="success">
        <EmailText>
          We are delighted to offer you the position of <strong>{jobTitle}</strong> at {companyName}.
        </EmailText>
      </Card>
      
      <Card variant="info">
        <EmailHeading level={3}>Offer Details</EmailHeading>
        {formattedSalary && (
          <InfoRow icon="💰" label="Compensation" value={formattedSalary} />
        )}
        {startDate && (
          <InfoRow icon="📅" label="Start Date" value={startDate} />
        )}
        {offerExpirationDate && (
          <InfoRow icon="⏰" label="Offer Valid Until" value={offerExpirationDate} />
        )}
      </Card>
      
      {offerLetterUrl && (
        <CTASection
          description="Please review the attached offer letter for full details about your compensation package, benefits, and terms of employment."
          buttonText="View Offer Letter"
          buttonHref={offerLetterUrl}
        />
      )}
      
      {responseLink && (
        <>
          <EmailText>
            After reviewing the offer details, please let us know your decision by clicking one of the buttons below:
          </EmailText>
          
          <TwoColumn
            leftContent={
              <Button 
                href={`${responseLink}?response=accept`}
                fullWidth
                style={{ backgroundColor: colors.success[600] }}
              >
                Accept Offer
              </Button>
            }
            rightContent={
              <Button 
                href={`${responseLink}?response=decline`}
                fullWidth
                style={{ backgroundColor: colors.error[600] }}
              >
                Decline Offer
              </Button>
            }
          />
        </>
      )}
      
      <Divider spacing="large" />
      
      <EmailText>
        We are excited about the possibility of you joining our team and look forward to your response.
      </EmailText>
      
      <ContactInfo {...contactInfo} companyName={companyName} />
      
      <EmailText variant="small" color="muted">
        If you have any questions about the offer, please don't hesitate to contact us.
      </EmailText>
    </EnhancedEmailLayout>
  );
};

export default OfferEmail;