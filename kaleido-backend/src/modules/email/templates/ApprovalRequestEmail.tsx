import * as React from 'react';
import { EnhancedEmailLayout } from './components/EnhancedEmailLayout';
import {
  EmailHeading,
  EmailText,
  Card,
  CTASection,
  InfoRow,
  IconText,
  Divider,
  ContactInfo,
  ContactInfoProps,
} from './components/EmailComponents';

interface ApprovalRequestEmailProps {
  approverName: string;
  candidateName: string;
  jobTitle: string;
  companyName?: string;
  approvalType: string;
  approvalLink?: string;
  dueDate?: string;
  contactInfo?: ContactInfoProps;
}

export const ApprovalRequestEmail: React.FC<ApprovalRequestEmailProps> = ({
  approverName,
  candidateName,
  jobTitle,
  companyName = 'Kaleido Talent',
  approvalType,
  approvalLink,
  dueDate,
  contactInfo,
}) => {
  const preview = `Approval Request for ${candidateName} - ${jobTitle}`;
  
  return (
    <EnhancedEmailLayout preview={preview}>
      <EmailHeading level={1} emoji="⚡">
        Action Required: Your Approval is Needed
      </EmailHeading>
      
      <EmailText>
        Hello {approverName},
      </EmailText>
      
      <Card variant="info">
        <EmailText>
          Your approval is requested for a <strong>{approvalType.toLowerCase()}</strong> for candidate <strong>{candidateName}</strong> for the <strong>{jobTitle}</strong> position at {companyName}.
        </EmailText>
        {dueDate && (
          <InfoRow icon="📅" label="Please respond by" value={dueDate} />
        )}
      </Card>
      
      {approvalLink && (
        <CTASection
          description="Please review and approve or reject using the link below:"
          buttonText="Review & Respond"
          buttonHref={approvalLink}
        />
      )}
      
      <Divider />
      
      <ContactInfo {...contactInfo} companyName={companyName} />
      
      <EmailText variant="small" color="muted">
        If you have any questions, please contact the hiring team.
      </EmailText>
    </EnhancedEmailLayout>
  );
};

export default ApprovalRequestEmail;