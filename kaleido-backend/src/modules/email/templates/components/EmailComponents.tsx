import * as React from 'react';
import {
  Button as ReactEmailButton,
  Heading,
  Text,
  Section,
  Row,
  Column,
  Img,
  Link,
  Hr,
} from '@react-email/components';
import { baseStyles, colors, spacing, borderRadius, typography, mergeStyles } from '../shared/styles';

// Button Components
interface ButtonProps {
  href: string;
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  fullWidth?: boolean;
  style?: React.CSSProperties;
}

export const Button: React.FC<ButtonProps> = ({ 
  href, 
  children, 
  variant = 'primary',
  fullWidth = false,
  style = {} 
}) => {
  const buttonStyles = {
    primary: baseStyles.buttonPrimary,
    secondary: baseStyles.buttonSecondary,
    outline: baseStyles.buttonOutline,
  };

  const baseStyle = buttonStyles[variant];
  const fullWidthStyle = fullWidth ? { display: 'block', width: '100%' } : {};

  return (
    <ReactEmailButton 
      href={href} 
      style={mergeStyles(baseStyle, fullWidthStyle, style)}
    >
      {children}
    </ReactEmailButton>
  );
};

// Typography Components
interface HeadingProps {
  level?: 1 | 2 | 3;
  children: React.ReactNode;
  style?: React.CSSProperties;
  emoji?: string;
}

export const EmailHeading: React.FC<HeadingProps> = ({ 
  level = 1, 
  children, 
  style = {},
  emoji 
}) => {
  const headingStyles = {
    1: baseStyles.h1,
    2: baseStyles.h2,
    3: baseStyles.h3,
  };

  return (
    <Heading style={mergeStyles(headingStyles[level], style)}>
      {emoji && <span style={baseStyles.emoji}>{emoji}</span>}
      {children}
    </Heading>
  );
};

interface TextProps {
  children: React.ReactNode;
  variant?: 'normal' | 'small' | 'large';
  style?: React.CSSProperties;
  color?: 'primary' | 'secondary' | 'muted';
}

export const EmailText: React.FC<TextProps> = ({ 
  children, 
  variant = 'normal',
  style = {},
  color 
}) => {
  const textStyles = {
    normal: baseStyles.text,
    small: baseStyles.textSmall,
    large: baseStyles.textLarge,
  };

  const colorStyles = {
    primary: { color: colors.neutral[900] },
    secondary: { color: colors.neutral[700] },
    muted: { color: colors.neutral[500] },
  };

  const colorStyle = color ? colorStyles[color] : {};

  return (
    <Text style={mergeStyles(textStyles[variant], colorStyle, style)}>
      {children}
    </Text>
  );
};

// Card/Box Components
interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'highlight' | 'info' | 'success' | 'warning' | 'error';
  style?: React.CSSProperties;
}

export const Card: React.FC<CardProps> = ({ 
  children, 
  variant = 'default',
  style = {} 
}) => {
  const cardStyles = {
    default: baseStyles.card,
    highlight: baseStyles.highlightBox,
    info: {
      ...baseStyles.infoBox,
      backgroundColor: colors.info[50],
      borderColor: colors.info[200],
    },
    success: {
      ...baseStyles.card,
      backgroundColor: colors.success[50],
      borderColor: colors.success[200],
    },
    warning: {
      ...baseStyles.highlightBox,
      backgroundColor: colors.warning[50],
      borderColor: colors.warning[200],
    },
    error: {
      ...baseStyles.card,
      backgroundColor: colors.error[50],
      borderColor: colors.error[200],
    },
  };

  return (
    <Section style={mergeStyles(cardStyles[variant], style)}>
      {children}
    </Section>
  );
};

// List Component
interface ListProps {
  items: (string | React.ReactNode)[];
  style?: React.CSSProperties;
  icon?: string;
}

export const List: React.FC<ListProps> = ({ items, style = {}, icon = '•' }) => {
  return (
    <Section style={mergeStyles(baseStyles.bulletList, style)}>
      {items.map((item, index) => (
        <Text key={index} style={{ margin: `0 0 ${spacing[2]} 0` }}>
          {icon} {item}
        </Text>
      ))}
    </Section>
  );
};

// Divider Component
interface DividerProps {
  style?: React.CSSProperties;
  spacing?: 'small' | 'medium' | 'large';
}

export const Divider: React.FC<DividerProps> = ({ 
  style = {}, 
  spacing: spacingSize = 'medium' 
}) => {
  const spacingStyles = {
    small: { margin: `${spacing[4]} 0` },
    medium: { margin: `${spacing[8]} 0` },
    large: { margin: `${spacing[12]} 0` },
  };

  return (
    <Hr style={mergeStyles(baseStyles.divider, spacingStyles[spacingSize], style)} />
  );
};

// Two Column Layout
interface TwoColumnProps {
  leftContent: React.ReactNode;
  rightContent: React.ReactNode;
  leftWidth?: string;
  rightWidth?: string;
  gap?: number;
}

export const TwoColumn: React.FC<TwoColumnProps> = ({ 
  leftContent, 
  rightContent,
  leftWidth = '50%',
  rightWidth = '50%',
  gap = 16
}) => {
  return (
    <Row>
      <Column style={{ width: leftWidth, paddingRight: `${gap/2}px` }}>
        {leftContent}
      </Column>
      <Column style={{ width: rightWidth, paddingLeft: `${gap/2}px` }}>
        {rightContent}
      </Column>
    </Row>
  );
};

// Icon with Text Component
interface IconTextProps {
  icon: string;
  text: string | React.ReactNode;
  style?: React.CSSProperties;
}

export const IconText: React.FC<IconTextProps> = ({ icon, text, style = {} }) => {
  return (
    <Text style={mergeStyles(baseStyles.text, { margin: `0 0 ${spacing[2]} 0` }, style)}>
      <span style={{ fontSize: '20px', marginRight: spacing[2], verticalAlign: 'middle' }}>
        {icon}
      </span>
      {text}
    </Text>
  );
};

// CTA Section Component
interface CTASectionProps {
  title?: string;
  description?: string;
  buttonText: string;
  buttonHref: string;
  buttonVariant?: 'primary' | 'secondary' | 'outline';
  style?: React.CSSProperties;
}

export const CTASection: React.FC<CTASectionProps> = ({
  title,
  description,
  buttonText,
  buttonHref,
  buttonVariant = 'primary',
  style = {}
}) => {
  return (
    <Section style={mergeStyles({ textAlign: 'center' as const, padding: `${spacing[8]} 0` }, style)}>
      {title && <EmailHeading level={2}>{title}</EmailHeading>}
      {description && <EmailText>{description}</EmailText>}
      <Button href={buttonHref} variant={buttonVariant}>
        {buttonText}
      </Button>
    </Section>
  );
};

// Info Row Component (for structured data like date, time, location)
interface InfoRowProps {
  label: string;
  value: string | React.ReactNode;
  icon?: string;
}

export const InfoRow: React.FC<InfoRowProps> = ({ label, value, icon }) => {
  return (
    <Text style={{ margin: `0 0 ${spacing[2]} 0`, color: colors.neutral[700] }}>
      {icon && <span style={{ marginRight: spacing[2] }}>{icon}</span>}
      <strong>{label}:</strong> {value}
    </Text>
  );
};

// Signature Component
interface SignatureProps {
  name: string;
  title?: string;
  company?: string;
  imageUrl?: string;
}

export const Signature: React.FC<SignatureProps> = ({ 
  name, 
  title, 
  company, 
  imageUrl 
}) => {
  return (
    <Section style={{ marginTop: spacing[8] }}>
      <Row>
        {imageUrl && (
          <Column style={{ width: '80px', paddingRight: spacing[4] }}>
            <Img
              src={imageUrl}
              alt={name}
              style={{
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                border: `2px solid ${colors.neutral[200]}`,
              }}
            />
          </Column>
        )}
        <Column>
          <Text style={{ 
            margin: 0, 
            fontSize: typography.fontSize.base,
            fontWeight: typography.fontWeight.semibold,
            color: colors.neutral[900] 
          }}>
            {name}
          </Text>
          {title && (
            <Text style={{ 
              margin: 0, 
              fontSize: typography.fontSize.sm,
              color: colors.neutral[600] 
            }}>
              {title}
            </Text>
          )}
          {company && (
            <Text style={{ 
              margin: 0, 
              fontSize: typography.fontSize.sm,
              color: colors.neutral[600] 
            }}>
              {company}
            </Text>
          )}
        </Column>
      </Row>
    </Section>
  );
};

// Progress Indicator Component
interface ProgressStep {
  label: string;
  completed: boolean;
  current?: boolean;
}

interface ProgressIndicatorProps {
  steps: ProgressStep[];
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({ steps }) => {
  return (
    <Section style={{ padding: `${spacing[6]} 0` }}>
      <Row>
        {steps.map((step, index) => (
          <Column key={index} style={{ textAlign: 'center' as const }}>
            <div style={{
              width: '24px',
              height: '24px',
              borderRadius: '50%',
              backgroundColor: step.completed ? colors.primary[600] : step.current ? colors.primary[400] : colors.neutral[300],
              color: '#ffffff',
              display: 'inline-block',
              lineHeight: '24px',
              fontSize: '12px',
              fontWeight: '600',
              marginBottom: spacing[2],
            }}>
              {step.completed ? '✓' : index + 1}
            </div>
            <Text style={{ 
              fontSize: typography.fontSize.xs,
              color: step.current ? colors.primary[600] : colors.neutral[600],
              margin: 0,
            }}>
              {step.label}
            </Text>
          </Column>
        ))}
      </Row>
    </Section>
  );
};
// Export ContactInfo component
export { ContactInfo, type ContactInfoProps } from './ContactInfo';
