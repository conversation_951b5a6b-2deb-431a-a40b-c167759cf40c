import * as React from 'react';
import { Section, Text, Link, Row, Column } from '@react-email/components';
import { baseStyles, colors, spacing } from '../shared/styles';

export interface ContactInfoProps {
  companyName?: string;
  email?: string;
  phone?: string;
  address?: string;
  website?: string;
  showHeader?: boolean;
}

export const ContactInfo: React.FC<ContactInfoProps> = ({
  companyName = 'Kaleido Talent',
  email = '<EMAIL>',
  phone = '+****************',
  address = '123 Business Avenue, Suite 100, New York, NY 10001',
  website = 'https://kaleidotalent.com',
  showHeader = true,
}) => {
  return (
    <Section style={contactSection}>
      {showHeader && (
        <Text style={contactHeader}>Contact Information</Text>
      )}
      
      <Row>
        <Column style={{ width: '50%' }}>
          <Text style={contactText}>
            <strong style={contactLabel}>Email:</strong>{' '}
            <Link href={`mailto:${email}`} style={contactLink}>
              {email}
            </Link>
          </Text>
          
          {phone && (
            <Text style={contactText}>
              <strong style={contactLabel}>Phone:</strong>{' '}
              <Link href={`tel:${phone.replace(/\D/g, '')}`} style={contactLink}>
                {phone}
              </Link>
            </Text>
          )}
        </Column>
        
        <Column style={{ width: '50%' }}>
          {website && (
            <Text style={contactText}>
              <strong style={contactLabel}>Website:</strong>{' '}
              <Link href={website} style={contactLink}>
                {website.replace(/^https?:\/\//, '')}
              </Link>
            </Text>
          )}
          
          {address && (
            <Text style={contactText}>
              <strong style={contactLabel}>Address:</strong>{' '}
              <span style={contactValue}>{address}</span>
            </Text>
          )}
        </Column>
      </Row>
      
      <Text style={contactFooter}>
        For any questions or assistance, please don't hesitate to reach out to us using the contact information above.
      </Text>
    </Section>
  );
};

const contactSection: React.CSSProperties = {
  backgroundColor: colors.neutral[50],
  padding: spacing[5],
  borderRadius: '8px',
  marginTop: spacing[6],
  marginBottom: spacing[5],
  border: `1px solid ${colors.neutral[200]}`,
};

const contactHeader: React.CSSProperties = {
  ...baseStyles.h3,
  marginBottom: spacing[4],
  color: colors.neutral[800],
  fontSize: '18px',
  fontWeight: '600',
};

const contactText: React.CSSProperties = {
  ...baseStyles.textSmall,
  marginBottom: spacing[3],
  lineHeight: '1.6',
};

const contactLabel: React.CSSProperties = {
  color: colors.neutral[700],
  fontWeight: '600',
};

const contactLink: React.CSSProperties = {
  color: colors.primary[600],
  textDecoration: 'none',
};

const contactValue: React.CSSProperties = {
  color: colors.neutral[600],
};

const contactFooter: React.CSSProperties = {
  ...baseStyles.textSmall,
  marginTop: spacing[4],
  paddingTop: spacing[4],
  borderTop: `1px solid ${colors.neutral[200]}`,
  color: colors.neutral[600],
  fontStyle: 'italic',
};

export default ContactInfo;