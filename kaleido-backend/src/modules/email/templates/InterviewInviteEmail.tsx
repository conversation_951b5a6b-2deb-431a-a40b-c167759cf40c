import * as React from 'react';

import {
  Button,
  Heading,
  Hr,
  Text,
} from '@react-email/components';

import { EmailLayout } from './components/EmailLayout';
import { ContactInfo, ContactInfoProps } from './components/EmailComponents';

interface InterviewInviteEmailProps {
  candidateName: string;
  jobTitle: string;
  companyName?: string;
  interviewDate?: string;
  interviewType?: string;
  interviewLocation?: string;
  meetingLink?: string;
  contactInfo?: ContactInfoProps;
}

export const InterviewInviteEmail: React.FC<InterviewInviteEmailProps> = ({
  candidateName,
  jobTitle,
  companyName = 'Kaleido Talent',
  interviewDate,
  interviewType = 'initial',
  interviewLocation,
  meetingLink,
  contactInfo,
}) => {
  const preview = `Interview Invitation for ${jobTitle} position`;
  
  return (
    <EmailLayout preview={preview}>
      <Heading style={h1}>Interview Invitation</Heading>
      <Text style={text}>
        Dear {candidateName},
      </Text>
      <Text style={text}>
        We are pleased to invite you for {interviewType} interview for the {jobTitle} position
        at {companyName}.
      </Text>
      {interviewDate && (
        <Text style={text}>
          The interview is scheduled for: {interviewDate}
        </Text>
      )}
      {interviewLocation && (
        <Text style={text}>
          Location: {interviewLocation}
        </Text>
      )}
      {meetingLink && (
        <>
          <Text style={text}>
            Please join the interview using the link below:
          </Text>
          <Button style={button} href={meetingLink}>
            Join Interview
          </Button>
        </>
      )}
      <Hr style={hr} />
      
      <ContactInfo {...contactInfo} companyName={companyName} />
      
      <Text style={footer}>
        If you need to reschedule or have any questions, please don't hesitate to contact us.
      </Text>
    </EmailLayout>
  );
};

const h1 = {
  color: '#1d1c1d',
  fontSize: '36px',
  fontWeight: '700',
  margin: '30px 0',
  padding: '0',
  lineHeight: '42px',
};

const text = {
  color: '#1d1c1d',
  fontSize: '18px',
  lineHeight: '28px',
  marginBottom: '20px',
};

const button = {
  backgroundColor: '#2563eb',
  borderRadius: '4px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  padding: '12px',
  margin: '20px 0',
};

const hr = {
  borderColor: '#e5e5e5',
  margin: '20px 0',
};

const footer = {
  color: '#9ca3af',
  fontSize: '14px',
  marginTop: '20px',
};

export default InterviewInviteEmail; 