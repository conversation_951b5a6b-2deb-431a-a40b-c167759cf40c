import * as React from 'react';
import { EnhancedEmailLayout } from './components/EnhancedEmailLayout';
import {
  EmailHeading,
  EmailText,
  Card,
  Divider,
  List,
  CTASection,
  InfoRow,
  Signature,
  IconText,
  ContactInfo,
  ContactInfoProps,
} from './components/EmailComponents';
import { Link } from '@react-email/components';
import { baseStyles } from './components';

interface VideoIntroductionPassiveProps {
  candidateName: string;
  jobTitle: string;
  roleName: string;
  companyName: string;
  roleDetailsUrl?: string;
  companyProfileUrl?: string;
  loginUrl?: string;
  recordingUrl: string;
  deadline: string;
  deadlineTime: string;
  yourName: string;
  yourTitle: string;
  contactInfo?: ContactInfoProps;
}

export const VideoIntroductionPassive: React.FC<VideoIntroductionPassiveProps> = ({
  candidateName,
  jobTitle,
  roleName,
  companyName,
  roleDetailsUrl,
  companyProfileUrl,
  loginUrl,
  recordingUrl,
  deadline,
  deadlineTime,
  yourName,
  yourTitle,
  contactInfo,
}) => {
  const preview = `You've been scouted for a ${jobTitle} role at ${companyName} 🚀`;

  return (
    <EnhancedEmailLayout preview={preview}>
      <EmailHeading level={1} emoji="🚀">
        You've been scouted for a {jobTitle} role at {companyName}
      </EmailHeading>
      
      <EmailText>Hi {candidateName},</EmailText>
      
      <EmailText>
        Hope this message finds you well!
      </EmailText>
      
      <EmailText>
        We're reaching out because your profile stood out through Kaleido Talent — a platform that's rethinking how exceptional people like you and great opportunities find each other.
      </EmailText>
      
      <Card variant="highlight">
        <IconText 
          icon="⭐" 
          text={<><strong>You didn't apply, and that's what makes this exciting:</strong></>}
        />
        <EmailText>
          Based on your skills, experience, and potential, we <strong>{companyName}</strong> think you could be a strong match for the open role of <strong>{roleName}</strong> — and we'd love to learn more about you, beyond just a CV.
        </EmailText>
      </Card>
      
      {(roleDetailsUrl || companyProfileUrl || loginUrl) && (
        <Card>
          {roleDetailsUrl && (
            <EmailText>
              <Link href={roleDetailsUrl} style={baseStyles.link}>Link to role details</Link>
            </EmailText>
          )}
          {companyProfileUrl && (
            <EmailText>
              <Link href={companyProfileUrl} style={baseStyles.link}>Link to company profile</Link>
            </EmailText>
          )}
          {loginUrl && (
            <EmailText>
              <Link href={loginUrl} style={baseStyles.link}>Link to LogIn / Quick Profile Creation</Link>
            </EmailText>
          )}
        </Card>
      )}
      
      <EmailHeading level={2}>What's next?</EmailHeading>
      
      <EmailText>
        If you're open to exploring this opportunity, we'd love to invite you to record a quick video introduction. It's a chance to show who you are, in your own words — not just what's written on paper.
      </EmailText>
      
      <CTASection
        buttonText="Link to record video"
        buttonHref={recordingUrl}
      />
      
      <Card variant="warning">
        <InfoRow 
          icon="⏰" 
          label="Please submit your video by" 
          value={<><strong>{deadline}</strong> at <strong>{deadlineTime}</strong></>} 
        />
      </Card>
      
      <EmailText>
        Here are a few prompts you might be asked about:
      </EmailText>
      
      <List 
        icon="→"
        items={[
          'What motivates or excites you right now',
          'How you like to work or grow professionally',
          'What kind of environment or role brings out your best',
          'Anything that helps us understand who you are'
        ]}
      />
      
      <EmailText>
        Even if you're not actively job hunting, this could be a great chance to explore something meaningful — no strings attached. Have a question? Just reply to this email.
      </EmailText>
      
      <EmailText>
        Looking forward to hearing from you!
      </EmailText>
      
      <Divider spacing="large" />
      
      <ContactInfo {...contactInfo} companyName={companyName} />
      
      <Signature
        name={yourName}
        title={yourTitle}
        company={companyName}
      />
    </EnhancedEmailLayout>
  );
};

export default VideoIntroductionPassive;