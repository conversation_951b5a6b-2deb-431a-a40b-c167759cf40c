import * as React from 'react';

import { Button, Heading, Hr, Text } from '@react-email/components';

import { EmailLayout } from './components/EmailLayout';
import { ContactInfo, ContactInfoProps } from './components/EmailComponents';

interface CulturalFitCompletedEmailProps {
  candidateName: string;
  jobTitle: string;
  companyName?: string;
  dashboardUrl?: string;
  contactInfo?: ContactInfoProps;
}

export const CulturalFitCompletedEmail: React.FC<CulturalFitCompletedEmailProps> = ({
  candidateName,
  jobTitle,
  companyName = 'Kaleido Talent',
  dashboardUrl = `${process.env.APP_URL}/dashboard`,
  contactInfo,
}) => {
  const preview = `Video Introduction Assessment Completed for ${jobTitle}`;

  return (
    <EmailLayout preview={preview}>
      <Heading style={h1}>Video Introduction Assessment Completed 🎯</Heading>
      <Text style={text}>Dear {candidateName},</Text>
      <Text style={text}>
        Thank you for completing the Video Intro Assessment for the {jobTitle} position at{' '}
        {companyName}. We appreciate the time and effort you've put into sharing your responses with
        us.
      </Text>
      <Text style={text}>
        Our team will carefully review your responses to better understand how you align with our
        company culture and values. This is an important step in our selection process.
      </Text>
      <Text style={text}>
        You can track the status of your application at any time through our candidate portal:
      </Text>
      <Button style={button} href={dashboardUrl}>
        Go to my dashboard
      </Button>
      <Hr style={hr} />
      
      <ContactInfo {...contactInfo} companyName={companyName} />
      
      <Text style={footer}>
        If you have any questions about the next steps in the process, please don't hesitate to
        reach out to our recruitment team.
      </Text>
    </EmailLayout>
  );
};

const h1 = {
  color: '#1d1c1d',
  fontSize: '36px',
  fontWeight: '700',
  margin: '30px 0',
  padding: '0',
  lineHeight: '42px',
};

const text = {
  color: '#1d1c1d',
  fontSize: '18px',
  lineHeight: '28px',
  marginBottom: '20px',
};

const button = {
  backgroundColor: '#2563eb',
  borderRadius: '4px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  padding: '12px',
  margin: '20px 0',
};

const hr = {
  borderColor: '#e5e5e5',
  margin: '20px 0',
};

const footer = {
  color: '#9ca3af',
  fontSize: '14px',
  marginTop: '20px',
};

export default CulturalFitCompletedEmail;
