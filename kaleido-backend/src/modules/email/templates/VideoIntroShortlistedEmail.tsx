import * as React from 'react';

import {
  Button,
  Heading,
  Hr,
  Section,
  Text,
} from '@react-email/components';

import { EmailLayout } from './components/EmailLayout';
import { ContactInfo, ContactInfoProps } from './components/EmailComponents';

interface VideoIntroShortlistedEmailProps {
  candidateName: string;
  jobTitle: string;
  companyName?: string;
  videoCaptureUrl: string;
  contactInfo?: ContactInfoProps;
}

export const VideoIntroShortlistedEmail: React.FC<VideoIntroShortlistedEmailProps> = ({
  candidateName,
  jobTitle,
  companyName = 'Kaleido Talent',
  videoCaptureUrl,
  contactInfo,
}) => {
  const preview = `Congratulations! You've been shortlisted for ${jobTitle} at ${companyName}`;

  return (
    <EmailLayout preview={preview}>
      <Heading style={h1}>🌟 Fantastic News, {candidateName}! 🌟</Heading>
      
      <Text style={text}>
        We're thrilled to inform you that your application for the <strong>{jobTitle}</strong> position
        at {companyName} has caught our attention in the best possible way!
      </Text>

      <Text style={text}>
        Your unique blend of skills, experience, and potential has truly impressed our team.
        We believe you could be an exceptional fit for this role and our company culture.
      </Text>

      <Section style={highlightSection}>
        <Heading style={h2}>🎯 Next Steps</Heading>
        <Text style={highlightText}>
          To help us get to know you better, we'd love to hear more about your experience and aspirations.
          Please take a moment to record a brief video introduction using the link below:
        </Text>
      </Section>

      <Button style={button} href={videoCaptureUrl}>
        Record Your Video Introduction
      </Button>

      <Text style={text}>
        This is an exciting opportunity for us to learn more about your:
      </Text>
      
      <Text style={listText}>
        • Professional journey and achievements<br />
        • Approach to problem-solving<br />
        • Vision for your role at {companyName}
      </Text>

      <Section style={securitySection}>
        <Text style={securityText}>
          🔒 Don't worry - this is a secure, private link created just for you.
        </Text>
      </Section>

      <Hr style={hr} />
      
      <ContactInfo {...contactInfo} companyName={companyName} />
      
      <Text style={footer}>
        Best regards,<br />
        The {companyName} Team
      </Text>
    </EmailLayout>
  );
};

const h1 = {
  color: '#1f2937',
  fontSize: '28px',
  fontWeight: '700',
  lineHeight: '36px',
  margin: '0 0 24px 0',
  textAlign: 'center' as const,
};

const h2 = {
  color: '#1f2937',
  fontSize: '20px',
  fontWeight: '600',
  lineHeight: '28px',
  margin: '0 0 12px 0',
};

const text = {
  color: '#4b5563',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 16px 0',
};

const listText = {
  color: '#4b5563',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 24px 0',
  paddingLeft: '16px',
};

const highlightSection = {
  backgroundColor: '#f8fafc',
  padding: '20px',
  borderRadius: '8px',
  margin: '24px 0',
  border: '1px solid #e2e8f0',
};

const highlightText = {
  color: '#4b5563',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0',
};

const button = {
  backgroundColor: '#8b5cf6',
  borderRadius: '6px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  padding: '14px 28px',
  margin: '24px 0',
};

const securitySection = {
  backgroundColor: '#f0fdf4',
  padding: '16px',
  borderRadius: '6px',
  margin: '24px 0',
  border: '1px solid #bbf7d0',
};

const securityText = {
  color: '#166534',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '0',
  textAlign: 'center' as const,
};

const hr = {
  borderColor: '#e5e7eb',
  margin: '32px 0',
};

const footer = {
  color: '#6b7280',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '0',
};

export default VideoIntroShortlistedEmail;
