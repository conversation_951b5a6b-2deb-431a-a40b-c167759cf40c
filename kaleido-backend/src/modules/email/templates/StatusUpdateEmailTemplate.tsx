import * as React from 'react';

import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Section,
  Text,
} from '@react-email/components';
import { ContactInfo, ContactInfoProps } from './components/EmailComponents';

interface StatusUpdateEmailProps {
  candidateName: string;
  jobTitle: string;
  companyName?: string;
  message?: string;
  statusType: 'interview' | 'hired' | 'offer' | 'offerAccepted' | 'status';
  interviewDate?: string;
  meetingLink?: string;
  startDate?: string;
  onboardingLink?: string;
  expirationDate?: string;
  responseLink?: string;
  contactInfo?: ContactInfoProps;
}

export const StatusUpdateEmailTemplate: React.FC<StatusUpdateEmailProps> = ({
  candidateName = 'Candidate',
  jobTitle = 'the position',
  companyName,
  message = '',
  statusType = 'status',
  interviewDate,
  meetingLink,
  startDate,
  onboardingLink,
  expirationDate,
  responseLink,
  contactInfo,
}) => {
  // Format date string
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';

    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      timeZoneName: 'short',
    }).format(date);
  };

  // Get title and icon based on status type
  const getTitle = () => {
    switch (statusType) {
      case 'interview':
        return `Interview Invitation: ${jobTitle} Position`;
      case 'hired':
        return `Welcome to ${companyName}!`;
      case 'offer':
        return `Job Offer for ${jobTitle} Position`;
      case 'offerAccepted':
        return 'Offer Acceptance Confirmation';
      default:
        return `Status Update: ${jobTitle} Position`;
    }
  };

  // Get icon URL based on status type
  const getIconUrl = () => {
    switch (statusType) {
      case 'interview':
        return 'https://img.icons8.com/fluency/96/calendar.png';
      case 'hired':
        return 'https://img.icons8.com/fluency/96/confetti.png';
      case 'offer':
        return 'https://img.icons8.com/fluency/96/contract.png';
      case 'offerAccepted':
        return 'https://img.icons8.com/fluency/96/checkmark.png';
      default:
        return 'https://img.icons8.com/fluency/96/bell.png';
    }
  };

  // Status-specific colors are now handled directly in the gradient

  return (
    <Html>
      <Head>
        <title>{getTitle()}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>
      <Body style={main}>
        {/* Header with modern gradient background */}
        <Section style={{
          background: `linear-gradient(135deg, #8b5cf6 0%, #d946ef 50%, #ec4899 100%)`,
          padding: '40px 0',
          borderRadius: '12px 12px 0 0',
          marginBottom: '0'
        }}>
          <Container style={container}>
            <Section style={{ textAlign: 'center' as const }}>
              <Img
                src="https://kaleido-prod-space.lon1.cdn.digitaloceanspaces.com/assets/kaleido-logo-full-white.png"
                width="180"
                height="auto"
                alt="Kaleido Logo"
                style={{
                  margin: '0 auto 16px',
                }}
              />
              <Img
                src={getIconUrl()}
                width="48"
                height="48"
                alt="Status Icon"
                style={{
                  margin: '0 auto 16px',
                }}
              />
              <Heading style={{
                ...h1,
                color: 'white',
                margin: '0 0 8px',
                textAlign: 'center' as const,
              }}>
                {getTitle()}
              </Heading>
            </Section>
          </Container>
        </Section>

        {/* Main content */}
        <Container style={{
          ...container,
          backgroundColor: 'white',
          borderRadius: '0 0 8px 8px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          padding: '30px',
          marginTop: '0',
        }}>
          <Section>
            <Text style={greeting}>
              Dear {candidateName},
            </Text>

            {/* Custom message */}
            {!message && (
              <Text style={text}>
                This is an update regarding your application for the {jobTitle} position{companyName ? ` at ${companyName}` : ''}.
                Your application status has been updated. Please let us know if you have any questions.
              </Text>
            )}
            {message && message.split('\n').map((paragraph, i) => (
              <Text key={i} style={text}>{paragraph}</Text>
            ))}

            {/* Status-specific content */}
            {statusType === 'interview' && interviewDate && (
              <>
                <Section style={infoBox}>
                  <Section style={infoBoxHeader}>
                    <Img
                      src="https://img.icons8.com/fluency/96/calendar-week.png"
                      width="20"
                      height="20"
                      alt="Interview Details"
                      style={{
                        marginRight: '8px',
                        verticalAlign: 'middle',
                      }}
                    />
                    <Text style={infoBoxTitle}>Interview Details</Text>
                  </Section>
                  <Section style={infoBoxContent}>
                    <Text style={infoText}>
                      <Img
                        src="https://img.ihttps://kaleido-prod-space.lon1.cdn.digitaloceanspaces.com/assets/kaleido-logo-full-white.pngcons8.com/fluency/96/clock.png"
                        width="16"
                        height="16"
                        alt="Date and Time"
                        style={{
                          verticalAlign: 'middle',
                          marginRight: '8px',
                        }}
                      />
                      <strong>Date and Time:</strong> {formatDate(interviewDate)}
                    </Text>
                    {meetingLink && (
                      <Button href={meetingLink} style={primaryButton}>
                        Join Interview
                      </Button>
                    )}
                  </Section>
                </Section>
                <Text style={noteText}>
                  <Img
                    src="https://img.icons8.com/fluency/96/info.png"
                    width="16"
                    height="16"
                    alt="Info"
                    style={{
                      verticalAlign: 'middle',
                      marginRight: '8px',
                    }}
                  />
                  If you need to reschedule or have any questions, please don't hesitate to contact us.
                </Text>
              </>
            )}

            {statusType === 'hired' && startDate && (
              <>
                <Section style={infoBox}>
                  <Section style={infoBoxHeader}>
                    <Img
                      src="https://img.icons8.com/fluency/96/briefcase.png"
                      width="20"
                      height="20"
                      alt="Start Date Information"
                      style={{
                        marginRight: '8px',
                        verticalAlign: 'middle',
                      }}
                    />
                    <Text style={infoBoxTitle}>Start Date Information</Text>
                  </Section>
                  <Section style={infoBoxContent}>
                    <Text style={infoText}>
                      <Img
                        src="https://img.icons8.com/fluency/96/calendar.png"
                        width="16"
                        height="16"
                        alt="Start Date"
                        style={{
                          verticalAlign: 'middle',
                          marginRight: '8px',
                        }}
                      />
                      <strong>Start Date:</strong> {formatDate(startDate)}
                    </Text>
                    {onboardingLink && (
                      <Button href={onboardingLink} style={primaryButton}>
                        Start Onboarding
                      </Button>
                    )}
                  </Section>
                </Section>
                <Text style={noteText}>
                  <Img
                    src="https://img.icons8.com/fluency/96/info.png"
                    width="16"
                    height="16"
                    alt="Info"
                    style={{
                      verticalAlign: 'middle',
                      marginRight: '8px',
                    }}
                  />
                  In the coming days, you will receive additional information about your first day,
                  including orientation details and necessary paperwork.
                </Text>
              </>
            )}

            {statusType === 'offer' && (
              <>
                <Section style={infoBox}>
                  <Section style={infoBoxHeader}>
                    <Img
                      src="https://img.icons8.com/fluency/96/document.png"
                      width="20"
                      height="20"
                      alt="Offer Details"
                      style={{
                        marginRight: '8px',
                        verticalAlign: 'middle',
                      }}
                    />
                    <Text style={infoBoxTitle}>Offer Details</Text>
                  </Section>
                  <Section style={infoBoxContent}>
                    {expirationDate && (
                      <Text style={infoText}>
                        <Img
                          src="https://img.icons8.com/fluency/96/hourglass.png"
                          width="16"
                          height="16"
                          alt="Response Deadline"
                          style={{
                            verticalAlign: 'middle',
                            marginRight: '8px',
                          }}
                        />
                        <strong>Please respond by:</strong> {formatDate(expirationDate)}
                      </Text>
                    )}
                    {responseLink && (
                      <Button href={responseLink} style={primaryButton}>
                        View Offer Details
                      </Button>
                    )}
                  </Section>
                </Section>
                <Text style={noteText}>
                  <Img
                    src="https://img.icons8.com/fluency/96/info.png"
                    width="16"
                    height="16"
                    alt="Info"
                    style={{
                      verticalAlign: 'middle',
                      marginRight: '8px',
                    }}
                  />
                  Please review the offer details carefully and feel free to reach out if you have any questions.
                </Text>
              </>
            )}

            <Hr style={hr} />

            {/* Contact Information */}
            <ContactInfo {...contactInfo} companyName={companyName} />

            {/* Modern simplified footer */}
            <Section style={footerSection}>
              <Img
                src="https://kaleido-prod-space.lon1.cdn.digitaloceanspaces.com/assets/kaleido-logo-full.png"
                width="150"
                height="auto"
                alt="Kaleido"
                style={{
                  margin: '0 auto 16px',
                }}
              />

              {/* Social media links */}
              <Section style={socialLinks}>
                <Link href="#" style={socialLink}>
                  <Img
                    src="https://img.icons8.com/color/48/linkedin.png"
                    width="24"
                    height="24"
                    alt="LinkedIn"
                  />
                </Link>
                <Link href="#" style={socialLink}>
                  <Img
                    src="https://img.icons8.com/color/48/twitter.png"
                    width="24"
                    height="24"
                    alt="Twitter"
                  />
                </Link>
                <Link href="#" style={socialLink}>
                  <Img
                    src="https://img.icons8.com/color/48/facebook.png"
                    width="24"
                    height="24"
                    alt="Facebook"
                  />
                </Link>
              </Section>

              <Text style={disclaimer}>
                © Kaleido Talent | <Link href="mailto:<EMAIL>" style={{ color: '#8b5cf6', textDecoration: 'none' }}><EMAIL></Link>
              </Text>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

const main = {
  backgroundColor: '#f3f4f6', // bg-gray-100
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
  padding: '20px 0',
};

const container = {
  margin: '0 auto',
  maxWidth: '700px',
};

const h1 = {
  color: '#1d1c1d',
  fontSize: '28px',
  fontWeight: '700',
  lineHeight: '32px',
};

const greeting = {
  color: '#1d1c1d',
  fontSize: '20px',
  lineHeight: '28px',
  marginBottom: '16px',
  fontWeight: '600',
};

const text = {
  color: '#4b5563', // text-gray-600
  fontSize: '16px',
  lineHeight: '24px',
  marginBottom: '16px',
};

const infoText = {
  color: '#4b5563', // text-gray-600
  fontSize: '16px',
  lineHeight: '24px',
  marginBottom: '16px',
  display: 'flex',
  alignItems: 'center',
};

const noteText = {
  color: '#6b7280', // text-gray-500
  fontSize: '14px',
  lineHeight: '20px',
  marginTop: '16px',
  marginBottom: '20px',
  fontStyle: 'italic',
  display: 'flex',
  alignItems: 'center',
};

const primaryButton = {
  backgroundColor: '#8b5cf6', // bg-purple-600
  borderRadius: '6px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  padding: '12px 24px',
  margin: '20px 0 10px',
  boxShadow: '0 4px 6px rgba(139, 92, 246, 0.25)',
  border: 'none',
  cursor: 'pointer',
  transition: 'all 0.2s ease-in-out',
};

const hr = {
  borderColor: '#e5e7eb', // border-gray-200
  margin: '30px 0 20px',
};

// Footer styles are now applied inline

const footerSection = {
  textAlign: 'center' as const,
};

const socialLinks = {
  display: 'flex',
  justifyContent: 'center',
  margin: '20px 0',
};

const socialLink = {
  display: 'inline-block',
  margin: '0 8px',
};

const disclaimer = {
  color: '#9ca3af', // text-gray-400
  fontSize: '12px',
  lineHeight: '16px',
  marginTop: '20px',
};

const infoBox = {
  borderRadius: '8px',
  overflow: 'hidden',
  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
  marginTop: '24px',
  marginBottom: '24px',
  border: '1px solid #e5e7eb',
};

const infoBoxHeader = {
  backgroundColor: '#8b5cf6', // bg-purple-600
  padding: '12px 16px',
  display: 'flex',
  alignItems: 'center',
};

const infoBoxTitle = {
  color: 'white',
  fontSize: '16px',
  fontWeight: '600',
  margin: '0',
};

const infoBoxContent = {
  backgroundColor: 'white',
  padding: '16px',
};

export default StatusUpdateEmailTemplate;
