import * as React from 'react';
import { EnhancedEmailLayout } from './components/EnhancedEmailLayout';
import {
  EmailHeading,
  EmailText,
  Card,
  Divider,
  List,
  CTASection,
  InfoRow,
  ContactInfo,
  ContactInfoProps,
} from './components/EmailComponents';
import { Link } from '@react-email/components';
import { baseStyles } from './components';

interface VideoIntroductionActiveProps {
  candidateName: string;
  roleName: string;
  companyName: string;
  jobDescriptionUrl?: string;
  companyProfileUrl?: string;
  loginUrl?: string;
  recordingUrl: string;
  deadline: string;
  deadlineTime: string;
  contactInfo?: ContactInfoProps;
}

export const VideoIntroductionActive: React.FC<VideoIntroductionActiveProps> = ({
  candidateName,
  roleName,
  companyName,
  jobDescriptionUrl,
  companyProfileUrl,
  loginUrl,
  recordingUrl,
  deadline,
  deadlineTime,
  contactInfo,
}) => {
  const preview = `Let's take the next step in your application for ${roleName} at ${companyName}`;

  return (
    <EnhancedEmailLayout preview={preview}>
      <EmailHeading level={1}>
        Let's take the next step in your application for {roleName} at {companyName}
      </EmailHeading>
      
      <EmailText>Hi {candidateName},</EmailText>
      
      <EmailText>
        We're thrilled to let you know that your application for the <strong>{roleName}</strong> role at our company <strong>{companyName}</strong> really caught attention. Your profile jumped out in the best possible way — there's clearly something special here.
      </EmailText>
      
      {(jobDescriptionUrl || companyProfileUrl || loginUrl) && (
        <Card>
          {jobDescriptionUrl && (
            <EmailText>
              <Link href={jobDescriptionUrl} style={baseStyles.link}>Link to the JD</Link>
            </EmailText>
          )}
          {companyProfileUrl && (
            <EmailText>
              <Link href={companyProfileUrl} style={baseStyles.link}>Link to the company profile</Link>
            </EmailText>
          )}
          {loginUrl && (
            <EmailText>
              <Link href={loginUrl} style={baseStyles.link}>Link to LogIn</Link>
            </EmailText>
          )}
        </Card>
      )}
      
      <EmailText>
        Your unique blend of skills, experience, and potential made a strong impression. We believe you could be a great fit — not just for the role, but for the culture too. Now's your chance to go beyond the bullet points and show who you are. Just be yourself!
      </EmailText>
      
      <EmailHeading level={2}>Next Steps</EmailHeading>
      
      <EmailText>
        To help us get to know you better, we'd love to hear more about your experience and aspirations. Please take a moment to record a brief video introduction using the link below:
      </EmailText>
      
      <CTASection
        buttonText="Link to recording"
        buttonHref={recordingUrl}
      />
      
      <Card variant="warning">
        <InfoRow 
          icon="⏰" 
          label="Deadline" 
          value={<><strong>{deadline}</strong> at <strong>{deadlineTime}</strong></>} 
        />
      </Card>
      
      <EmailText>
        This is an exciting opportunity for us to learn more about you. You might be asked about:
      </EmailText>
      
      <List 
        icon="•"
        items={[
          'What drives and motivates you',
          'How you approach challenges or teamwork',
          'What excites you about this role',
          'Your goals, values, or working style',
          'Anything else that helps you stand out'
        ]}
      />
      
      <EmailText>
        Just speak your truth — we're here to get to know you.
      </EmailText>
      
      <EmailText>
        Have any questions? Just send a message via the help chat — the Kaleido Team will help you.
      </EmailText>
      
      <Divider spacing="large" />
      
      <ContactInfo {...contactInfo} companyName={companyName} />
      
      <EmailText variant="small" color="muted">
        Best regards,<br />
        The {companyName} Team
      </EmailText>
    </EnhancedEmailLayout>
  );
};

export default VideoIntroductionActive;