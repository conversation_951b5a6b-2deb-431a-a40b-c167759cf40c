import * as React from 'react';
import { render } from '@react-email/render';
import { getVideoCaptureUrl, getDashboardUrl } from './email.utils';
import { ContactInfoProps } from './templates/components/EmailComponents';

import VideoIntroShortlistedEmail from './templates/VideoIntroShortlistedEmail';
import { VideoIntroductionActive } from './templates/VideoIntroductionActive';
import { VideoIntroductionPassive } from './templates/VideoIntroductionPassive';
import { InterestEmail } from './templates/InterestEmail';
import { InterviewInviteEmail } from './templates/InterviewInviteEmail';
import { HiredEmail } from './templates/HiredEmail';
import { CulturalFitCompletedEmail } from './templates/CulturalFitCompletedEmail';
import { CulturalFitEmployerNotificationEmail } from './templates/CulturalFitEmployerNotificationEmail';
import { ApprovalRequestEmail } from './templates/ApprovalRequestEmail';
import { OfferEmail } from './templates/OfferEmail';
import StatusUpdateEmailTemplate from './templates/StatusUpdateEmailTemplate';
import { ContactFormEmail } from './templates/ContactFormEmail';
import { UserRegistrationNotificationEmail } from './templates/UserRegistrationNotificationEmail';

/**
 * Default contact information for emails
 */
const getDefaultContactInfo = (companyName?: string): ContactInfoProps => ({
  companyName: companyName || 'Kaleido Talent',
  email: '<EMAIL>',
  phone: '+****************',
  address: '123 Business Avenue, Suite 100, New York, NY 10001',
  website: 'https://kaleidotalent.com',
});

/**
 * Email template HTML generators
 */
export class EmailTemplates {
  static async getVideoIntroShortlistedEmailHtml(
    candidateName: string,
    jobTitle: string,
    companyName = 'Kaleido Talent',
    jobId: string,
    candidateId: string,
    contactInfo?: ContactInfoProps,
  ): Promise<string> {
    const videoCaptureUrl = getVideoCaptureUrl(jobId, candidateId);

    return await render(
      React.createElement(VideoIntroShortlistedEmail, {
        candidateName,
        jobTitle,
        companyName,
        videoCaptureUrl,
        contactInfo: contactInfo || getDefaultContactInfo(companyName),
      }),
    );
  }

  static async getVideoIntroActiveEmailHtml(
    candidateName: string,
    jobTitle: string,
    companyName = 'Kaleido Talent',
    jobId: string,
    candidateId: string,
    contactInfo?: ContactInfoProps,
  ): Promise<string> {
    const recordingUrl = getVideoCaptureUrl(jobId, candidateId);
    const deadline = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
    const deadlineTime = '11:59 PM';

    return await render(
      React.createElement(VideoIntroductionActive, {
        candidateName,
        roleName: jobTitle,
        companyName,
        recordingUrl,
        deadline,
        deadlineTime,
        contactInfo: contactInfo || getDefaultContactInfo(companyName),
      }),
    );
  }

  static async getVideoIntroPassiveEmailHtml(
    candidateName: string,
    jobTitle: string,
    companyName = 'Kaleido Talent',
    jobId: string,
    candidateId: string,
    contactInfo?: ContactInfoProps,
  ): Promise<string> {
    const recordingUrl = getVideoCaptureUrl(jobId, candidateId);
    const deadline = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
    const deadlineTime = '11:59 PM';

    return await render(
      React.createElement(VideoIntroductionPassive, {
        candidateName,
        jobTitle,
        roleName: jobTitle,
        companyName,
        recordingUrl,
        deadline,
        deadlineTime,
        yourName: 'Hiring Team',
        yourTitle: `${companyName} Recruitment`,
        contactInfo: contactInfo || getDefaultContactInfo(companyName),
      }),
    );
  }

  static async getInterestEmailHtml(
    candidateName: string,
    jobTitle: string,
    companyName = 'Kaleido Talent',
    jobId?: string,
    candidateId?: string,
    contactInfo?: ContactInfoProps,
  ): Promise<string> {
    // For interest emails, we don't include the video recording URL
    // Just express interest and ask for a response
    return await render(
      React.createElement(InterestEmail, {
        candidateName,
        jobTitle,
        roleName: jobTitle,
        companyName,
        yourName: 'Hiring Team',
        yourTitle: `${companyName} Talent Acquisition`,
      }),
    );
  }

  static async getInterviewInviteHtml(
    candidateName: string,
    jobTitle: string,
    interviewDetails: {
      date: string;
      type?: string;
      location?: string;
      meetingLink?: string;
    },
    companyName = 'Kaleido Talent',
    contactInfo?: ContactInfoProps,
  ): Promise<string> {
    return await render(
      React.createElement(InterviewInviteEmail, {
        candidateName,
        jobTitle,
        companyName,
        interviewDate: interviewDetails.date,
        interviewType: interviewDetails.type,
        interviewLocation: interviewDetails.location,
        meetingLink: interviewDetails.meetingLink,
        contactInfo: contactInfo || getDefaultContactInfo(companyName),
      }),
    );
  }

  static async getHiredEmailHtml(
    candidateName: string,
    jobTitle: string,
    companyName: string,
    startDate?: string,
    onboardingLink?: string,
    contactInfo?: ContactInfoProps,
  ): Promise<string> {
    return await render(
      React.createElement(HiredEmail, {
        candidateName,
        jobTitle,
        companyName,
        startDate,
        onboardingLink,
        contactInfo: contactInfo || getDefaultContactInfo(companyName),
      }),
    );
  }

  static async getCulturalFitCompletedHtml(
    candidateName: string,
    jobTitle: string,
    companyName = 'Kaleido Talent',
    dashboardUrl?: string,
    contactInfo?: ContactInfoProps,
  ): Promise<string> {
    return await render(
      React.createElement(CulturalFitCompletedEmail, {
        candidateName,
        jobTitle,
        companyName,
        dashboardUrl: getDashboardUrl(dashboardUrl),
        contactInfo: contactInfo || getDefaultContactInfo(companyName),
      }),
    );
  }

  static async getCulturalFitEmployerNotificationHtml(
    employerName: string,
    candidateName: string,
    jobTitle: string,
    companyName?: string,
    dashboardUrl?: string,
  ): Promise<string> {
    return await render(
      React.createElement(CulturalFitEmployerNotificationEmail, {
        employerName,
        candidateName,
        jobTitle,
        companyName,
        dashboardUrl,
      }),
    );
  }

  static async getApprovalRequestHtml(
    approverName: string,
    candidateName: string,
    jobTitle: string,
    approvalType: string,
    companyName = 'Kaleido Talent',
    approvalLink?: string,
    dueDate?: string,
    contactInfo?: ContactInfoProps,
  ): Promise<string> {
    return await render(
      React.createElement(ApprovalRequestEmail, {
        approverName,
        candidateName,
        jobTitle,
        companyName,
        approvalType,
        approvalLink,
        dueDate,
        contactInfo: contactInfo || getDefaultContactInfo(companyName),
      }),
    );
  }

  static async getOfferEmailHtml(
    candidateName: string,
    jobTitle: string,
    companyName = 'Kaleido Talent',
    offerLetterUrl?: string,
    startDate?: string,
    salary?: {
      amount: number;
      currency: string;
      period: string;
    },
    offerExpirationDate?: string,
    responseLink?: string,
    contactInfo?: ContactInfoProps,
  ): Promise<string> {
    return await render(
      React.createElement(OfferEmail, {
        candidateName,
        jobTitle,
        companyName,
        offerLetterUrl,
        startDate,
        salary,
        offerExpirationDate,
        responseLink,
        contactInfo: contactInfo || getDefaultContactInfo(companyName),
      }),
    );
  }

  static async getStatusUpdateEmailHtml(
    candidateName: string,
    jobTitle: string,
    statusType: 'interview' | 'hired' | 'offer' | 'offerAccepted' | 'status',
    companyName?: string,
    message?: string,
    additionalData?: any,
    contactInfo?: ContactInfoProps,
  ): Promise<string> {
    return await render(
      React.createElement(StatusUpdateEmailTemplate, {
        candidateName,
        jobTitle,
        companyName,
        message,
        statusType,
        contactInfo: contactInfo || getDefaultContactInfo(companyName),
        ...additionalData,
      }),
    );
  }

  static async getContactFormHtml(
    name: string,
    email: string,
    phone?: string,
    message?: string,
  ): Promise<string> {
    return await render(
      React.createElement(ContactFormEmail, {
        name,
        email,
        phone,
        message: message || '',
      }),
    );
  }

  static async getUserRegistrationNotificationHtml(
    userType: 'Company' | 'Job Seeker' | 'Graduate',
    userName: string,
    userEmail: string,
    registrationDate: string,
    additionalInfo?: any,
  ): Promise<string> {
    return await render(
      React.createElement(UserRegistrationNotificationEmail, {
        userType,
        userName,
        userEmail,
        registrationDate,
        additionalInfo,
      }),
    );
  }
}
