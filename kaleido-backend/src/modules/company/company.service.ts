import { In, Repository } from 'typeorm';

import { Job } from '@modules/job/entities/job.entity';
import { VideoJD } from '@modules/video-jd/entities/video-jd.entity';
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Auth0ManagementService } from '@/auth/auth0-management.service';
import { ATS_CONFIGURATION } from '../ats/ats.config';
import { EmailService } from '../email/email.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { Company } from './entities/company.entity';

@Injectable()
export class CompanyService {
  constructor(
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    @InjectRepository(VideoJD)
    private readonly videoJDRepository: Repository<VideoJD>,
    @Inject(forwardRef(() => EmailService))
    private readonly emailService: EmailService,
    private readonly auth0ManagementService: Auth0ManagementService,
  ) {}

  async createOrUpdate(createCompanyDto: CreateCompanyDto): Promise<Company> {
    // First check if company exists by contact email
    if (createCompanyDto.contactEmail) {
      const existingCompany = await this.companyRepository.findOne({
        where: { contactEmail: createCompanyDto.contactEmail },
      });

      if (existingCompany) {
        // Update existing company
        throw new BadRequestException('Email has already been used. Please use another email.');
      }
    }

    // If no existing company found, create new one
    const company = this.companyRepository.create(createCompanyDto);
    const savedCompany = await this.companyRepository.save(company);

    // Send registration notification email to support team
    try {
      const userName = `${createCompanyDto.companyName || ''}`.trim() || 'Unknown User';
      const userEmail = createCompanyDto.contactEmail || 'No email provided';

      await this.emailService.sendUserRegistrationNotification('Company', userName, userEmail, {
        companyName: createCompanyDto.companyName,
        companyWebsite: createCompanyDto.companyWebsite,
        location: createCompanyDto.location,
      });
    } catch (emailError) {
      // Log the error but don't fail the company creation
      console.error('Failed to send company registration notification email:', emailError);
    }

    return savedCompany;
  }

  async findAll(): Promise<Company[]> {
    return await this.companyRepository
      .createQueryBuilder('companies')
      .where('companies.companyName IS NOT NULL AND companies.companyName != :emptyString', {
        emptyString: '',
      })
      .orderBy('companies.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Get companies grouped by approval status and ordered by registration date
   */
  async findAllGroupedByStatus(): Promise<{
    approved: any[];
    pending: any[];
  }> {
    // Query companies with companyName filter and order by createdAt
    // Select only the fields needed for the registration table
    const companies = await this.companyRepository
      .createQueryBuilder('companies')
      .select([
        'companies.id',
        'companies.companyName',
        'companies.industry',
        'companies.contactEmail',
        'companies.createdAt',
        'companies.isApproved',
        'companies.declineReason',
      ])
      .where('companies.companyName IS NOT NULL AND companies.companyName != :emptyString', {
        emptyString: '',
      })
      .orderBy('companies.createdAt', 'DESC')
      .getMany();

    const approved = companies.filter((company) => company.isApproved === true);
    const pending = companies.filter((company) => company.isApproved !== true);

    return {
      approved,
      pending,
    };
  }

  async findByUserId(userId: string): Promise<Company> {
    const company = await this.companyRepository.findOne({
      where: { userId } as any,
    });
    if (!company) {
      throw new NotFoundException(`Company with clientId ${userId} not found`);
    }
    return company;
  }

  async findByClientId(clientId: string, skipSubscriptionFields = false): Promise<Company> {
    let company: Company | any;

    if (skipSubscriptionFields) {
      // Use a raw query to get the company without the subscription fields
      // This is used when the subscription columns don't exist in the database yet
      try {
        const result = await this.companyRepository.query(
          `SELECT
            id, "clientId", "companyName", "companyWebsite", department, industry,
            size, location, "contactEmail", "phoneNumber", logo, description,
            preferences, "createdAt", "updatedAt", "userId", "layoutPreference",
            "primaryColor", "secondaryColor", "accentColor", "heroImage",
            "featuredImages", "customCss", "atsConfig", "isPublished",
            "jobCount", "candidateUploadCount", "companyValues", "isDemoMode",
            "isApproved", "declineReason"
          FROM companies
          WHERE "clientId" = $1`,
          [clientId],
        );

        if (result.length === 0) {
          throw new NotFoundException(`Company with clientId ${clientId} not found`);
        }

        company = result[0];
      } catch (error) {
        console.error('Error executing raw query:', error);
        throw error;
      }
    } else {
      // Use the standard repository method
      company = await this.companyRepository.findOne({
        where: { clientId },
      });

      if (!company) {
        throw new NotFoundException(`Company with clientId ${clientId} not found`);
      }
    }

    // Add extended ATS settings if available
    const extendedSettings: any =
      ATS_CONFIGURATION.find((x) => x.name === (company as Company).atsConfig?.provider) ?? {};
    if (company.atsConfig) {
      company.atsConfig = {
        ...company.atsConfig,
        extendedSettings: {
          rateLimit: extendedSettings.rateLimit,
          filtering: extendedSettings.filtering,
        },
      };
    }

    return company;
  }

  // Removed createBasicCompany method - no more auto-creation

  async incrementCandidateUploadCount(clientId: string, count: number = 1): Promise<Company> {
    const company = await this.findByClientId(clientId);
    company.candidateUploadCount += count;
    return await this.companyRepository.save(company);
  }

  async incrementJobCount(clientId: string): Promise<Company> {
    const company = await this.findByClientId(clientId);
    company.jobCount += 1;
    return await this.companyRepository.save(company);
  }

  async getUploadStats(clientId: string): Promise<{ used: number; limit: number }> {
    const company = await this.findByClientId(clientId);
    // This would typically come from configuration or environment variables
    const limit = parseInt(process.env.MAX_CANDIDATE_UPLOADS || '100', 10);
    return {
      used: company.candidateUploadCount,
      limit,
    };
  }

  async findOne(id: string): Promise<Company> {
    const company = await this.companyRepository.findOneByOrFail({ id });
    return company;
  }

  async update(id: string, updateCompanyDto: UpdateCompanyDto): Promise<Company> {
    const company = await this.findByClientId(id);

    // Check if contactName is being updated
    const isContactNameUpdated =
      updateCompanyDto.contactName && updateCompanyDto.contactName !== company.contactName;

    Object.assign(company, updateCompanyDto);
    const savedCompany = await this.companyRepository.save(company);

    // Update Auth0 profile if contact name was updated
    if (isContactNameUpdated && company.userId && updateCompanyDto.contactName) {
      await this.auth0ManagementService.updateCompanyContactName(
        company.userId,
        updateCompanyDto.contactName,
      );
    }

    return savedCompany;
  }

  async updateByClientId(clientId: string, updateCompanyDto: UpdateCompanyDto): Promise<Company> {
    const company = await this.findByClientId(clientId);

    // Check if contactName is being updated
    const isContactNameUpdated =
      updateCompanyDto.contactName && updateCompanyDto.contactName !== company.contactName;

    Object.assign(company, updateCompanyDto);
    const savedCompany = await this.companyRepository.save(company);

    // Update Auth0 profile if contact name was updated
    if (isContactNameUpdated && company.userId && updateCompanyDto.contactName) {
      await this.auth0ManagementService.updateCompanyContactName(
        company.userId,
        updateCompanyDto.contactName,
      );
    }

    return savedCompany;
  }

  async remove(id: string): Promise<void> {
    const result = await this.companyRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Company with ID ${id} not found`);
    }
  }

  async updateDemoMode(clientId: string, isDemoMode: boolean): Promise<Company> {
    const company = await this.findByClientId(clientId);
    company.isDemoMode = isDemoMode;
    return await this.companyRepository.save(company);
  }

  // Add a new method to find company by slug
  async findBySlug(slug: string): Promise<Company | null> {
    if (!slug) return null;

    // Get all companies with valid names
    const companies = await this.companyRepository
      .createQueryBuilder('companies')
      .where('companies.companyName IS NOT NULL AND companies.companyName != :emptyString', {
        emptyString: '',
      })
      .getMany();

    // Convert each company name to slug and compare
    const company = companies.find((c) => {
      // Simple slug generation - convert to lowercase and replace spaces with dashes
      const companySlug = c.companyName
        ?.toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^\w-]+/g, '')
        .replace(/--+/g, '-')
        .replace(/^-+/, '')
        .replace(/-+$/, '');

      return companySlug === slug;
    });

    return company || null;
  }

  /**
   * Find all unique industry values across companies
   */
  async findAllIndustries(): Promise<string[]> {
    const results = await this.companyRepository
      .createQueryBuilder('companies')
      .select('DISTINCT companies.industry', 'industry')
      .where('companies.industry IS NOT NULL')
      .andWhere('companies.companyName IS NOT NULL AND companies.companyName != :emptyString', {
        emptyString: '',
      })
      .getRawMany();

    return results.map((result) => result.industry);
  }

  /**
   * Find all unique company sizes across companies
   */
  async findAllSizes(): Promise<string[]> {
    const results = await this.companyRepository
      .createQueryBuilder('companies')
      .select('DISTINCT companies.size', 'size')
      .where('companies.size IS NOT NULL')
      .andWhere('companies.companyName IS NOT NULL AND companies.companyName != :emptyString', {
        emptyString: '',
      })
      .getRawMany();

    return results.map((result) => result.size);
  }

  /**
   * Approve a company registration
   */
  async approve(id: string): Promise<Company> {
    const company = await this.findOne(id);

    // Update company with approval status
    company.isApproved = true;

    return await this.companyRepository.save(company);
  }

  /**
   * Decline a company registration with a reason
   */
  async decline(id: string, reason: string): Promise<Company> {
    const company = await this.findOne(id);

    // Update company with decline status and reason
    company.isApproved = false;
    company.declineReason = reason;

    return await this.companyRepository.save(company);
  }

  /**
   * Get detailed company information including usage statistics
   */
  async getCompanyDetails(id: string): Promise<any> {
    // Get the company
    const company = await this.findOne(id);

    // Get jobs for this company
    const jobs = await this.jobRepository.find({
      where: { company: { id: company.id } },
      order: { createdAt: 'DESC' },
      take: 5,
    });

    // Get video JDs for this company's jobs
    const jobIds = jobs.map((job) => job.id);
    const videoJDs = await this.videoJDRepository.find({
      where: { jobId: In(jobIds) },
      order: { createdAt: 'DESC' },
      take: 5,
    });

    // Calculate monthly metrics
    const now = new Date();
    const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);

    // Get all jobs for metrics calculation
    const allJobs = await this.jobRepository.find({
      where: { company: { id: company.id } },
    });

    // Get all video JDs for metrics calculation
    const allVideoJDs = await this.videoJDRepository
      .createQueryBuilder('videoJD')
      .innerJoin('videoJD.job', 'job')
      .innerJoin('job.company', 'companies')
      .where('companies.id = :companyId', { companyId: company.id })
      .getMany();

    // Calculate jobs created this month and last month
    const jobsCreatedThisMonth = allJobs.filter((job) => job.createdAt >= thisMonthStart).length;
    const jobsCreatedLastMonth = allJobs.filter(
      (job) => job.createdAt >= lastMonthStart && job.createdAt < thisMonthStart,
    ).length;

    // Calculate video JDs created this month and last month
    const videoJDsCreatedThisMonth = allVideoJDs.filter(
      (videoJD) => videoJD.createdAt >= thisMonthStart,
    ).length;
    const videoJDsCreatedLastMonth = allVideoJDs.filter(
      (videoJD) => videoJD.createdAt >= lastMonthStart && videoJD.createdAt < thisMonthStart,
    ).length;

    // Calculate candidates added this month and last month
    const candidatesAddedThisMonth = allJobs.reduce((sum, job) => {
      const candidates = job.candidates || [];
      return sum + candidates.filter((candidate) => candidate.createdAt >= thisMonthStart).length;
    }, 0);

    const candidatesAddedLastMonth = allJobs.reduce((sum, job) => {
      const candidates = job.candidates || [];
      return (
        sum +
        candidates.filter(
          (candidate) =>
            candidate.createdAt >= lastMonthStart && candidate.createdAt < thisMonthStart,
        ).length
      );
    }, 0);

    // Define activity timeline item interface
    interface ActivityTimelineItem {
      date: Date;
      action: string;
      details: string;
    }

    // Generate activity timeline with explicit typing
    const activityTimeline: ActivityTimelineItem[] = [];

    // Add job creation events
    allJobs.slice(0, 10).forEach((job) => {
      activityTimeline.push({
        date: job.createdAt,
        action: 'Created job',
        details: `Created ${job.jobType} position`,
      });
    });

    // Add video JD creation events
    allVideoJDs.slice(0, 10).forEach((videoJD) => {
      const job = allJobs.find((j) => j.id === videoJD.jobId);
      if (job) {
        activityTimeline.push({
          date: videoJD.createdAt,
          action: 'Created video JD',
          details: `Created video JD for ${job.jobType} position`,
        });
      }
    });

    // Sort timeline by date (newest first)
    activityTimeline.sort((a, b) => b.date.getTime() - a.date.getTime());

    // Format jobs for response
    const recentJobs = jobs.map((job) => ({
      id: job.id,
      jobType: job.jobType,
      createdAt: job.createdAt,
      status: job.status,
      candidateCount: job.candidates?.length || 0,
    }));

    // Format video JDs for response
    const recentVideoJDs = videoJDs.map((videoJD) => ({
      id: videoJD.id,
      jobType: allJobs.find((job) => job.id === videoJD.jobId)?.jobType || 'Unknown',
      createdAt: videoJD.createdAt,
      status: videoJD.status,
    }));

    // Return detailed company information
    return {
      ...company,
      stats: {
        jobCount: allJobs.length,
        videoJDCount: allVideoJDs.length,
        candidateCount: allJobs.reduce((sum, job) => sum + (job.candidates?.length || 0), 0),
        lastActivity: company.updatedAt,
        recentJobs,
        recentVideoJDs,
        activityTimeline,
        usageMetrics: {
          jobsCreatedThisMonth,
          jobsCreatedLastMonth,
          candidatesAddedThisMonth,
          candidatesAddedLastMonth,
          videoJDsCreatedThisMonth,
          videoJDsCreatedLastMonth,
        },
      },
    };
  }

  /**
   * Get companies with pagination
   */
  async getCompaniesWithPagination(page = 1, limit = 10, search?: string) {
    const skip = (page - 1) * limit;

    // Create query builder for companies
    let companyQueryBuilder = this.companyRepository.createQueryBuilder('companies');

    // Filter out companies without a name (dirty data)
    companyQueryBuilder = companyQueryBuilder.where(
      'companies.companyName IS NOT NULL AND companies.companyName != :emptyString',
      { emptyString: '' },
    );

    // Apply search filter if provided
    if (search) {
      companyQueryBuilder = companyQueryBuilder.andWhere(
        '(companies.companyName ILIKE :search OR companies.industry ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Get total count for pagination
    const total = await companyQueryBuilder.getCount();

    // Get paginated companies with just the fields we need
    const companies = await companyQueryBuilder
      .orderBy('companies.updatedAt', 'DESC')
      .skip(skip)
      .take(limit)
      .getMany();

    // Return paginated results
    return {
      data: companies,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get company usage statistics with pagination
   * Optimized version to prevent memory leaks
   */
  async getCompanyUsageStats(page = 1, limit = 10, search?: string) {
    const skip = (page - 1) * limit;

    // Create query builder for companies
    let companyQueryBuilder = this.companyRepository.createQueryBuilder('companies');

    // Filter out companies without a name (dirty data)
    companyQueryBuilder = companyQueryBuilder.where(
      'companies.companyName IS NOT NULL AND companies.companyName != :emptyString',
      { emptyString: '' },
    );

    // Apply search filter if provided
    if (search) {
      companyQueryBuilder = companyQueryBuilder.andWhere(
        '(companies.companyName ILIKE :search OR companies.industry ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Get total count for pagination
    const total = await companyQueryBuilder.getCount();

    // Get ALL companies (without pagination) to calculate stats and sort properly
    const allCompanies = await companyQueryBuilder.orderBy('companies.updatedAt', 'DESC').getMany();

    // Get company IDs for batch processing
    const companyIds = allCompanies.map((company) => company.id);
    const companyClientIds = allCompanies.map((company) => company.clientId).filter((id) => id); // Filter out null/undefined clientIds

    // Batch query for job counts by company
    // Direct query on job table using clientId field
    let jobCountsQuery = [];
    if (companyClientIds.length > 0) {
      jobCountsQuery = await this.jobRepository
        .createQueryBuilder('job')
        .select('job."clientId"', 'clientId')
        .addSelect('COUNT(job.id)', 'count')
        .where('job."clientId" IN (:...clientIds)', { clientIds: companyClientIds })
        .groupBy('job."clientId"')
        .getRawMany();
    }

    // Create a map of client ID to job count
    const jobCountMap = new Map<string, number>();
    jobCountsQuery.forEach((item) => {
      jobCountMap.set(item.clientId, parseInt(item.count, 10));
    });

    // Batch query for video JD counts by company
    let videoJDCountsQuery = [];
    if (companyClientIds.length > 0) {
      videoJDCountsQuery = await this.videoJDRepository
        .createQueryBuilder('videoJD')
        .innerJoin('videoJD.job', 'job')
        .select('job."clientId"', 'clientId')
        .addSelect('COUNT(videoJD.id)', 'count')
        .where('job."clientId" IN (:...clientIds)', { clientIds: companyClientIds })
        .groupBy('job."clientId"')
        .getRawMany();
    }

    // Create a map of client ID to video JD count
    const videoJDCountMap = new Map<string, number>();
    videoJDCountsQuery.forEach((item) => {
      videoJDCountMap.set(item.clientId, parseInt(item.count, 10));
    });

    // Simplified approach for candidate counts
    // Get candidate counts by joining through jobs
    let candidateCountsQuery = [];
    if (companyClientIds.length > 0) {
      candidateCountsQuery = await this.jobRepository
        .createQueryBuilder('job')
        .leftJoin('job.candidates', 'candidate')
        .select('job."clientId"', 'clientId')
        .addSelect('COUNT(candidate.id)', 'count')
        .where('job."clientId" IN (:...clientIds)', { clientIds: companyClientIds })
        .groupBy('job."clientId"')
        .getRawMany();
    }

    // Create a map of client ID to candidate count
    const candidateCountMap = new Map<string, number>();
    candidateCountsQuery.forEach((item) => {
      candidateCountMap.set(item.clientId, parseInt(item.count, 10) || 0);
    });

    // Batch query for last job created date by company
    // Use a subquery to get the most recent job for each company
    let lastJobCreatedQuery = [];
    if (companyClientIds.length > 0) {
      lastJobCreatedQuery = await this.jobRepository
        .createQueryBuilder('job')
        .select('job."clientId"', 'clientId')
        .addSelect('MAX(job."createdAt")', 'createdAt')
        .where('job."clientId" IN (:...clientIds)', { clientIds: companyClientIds })
        .groupBy('job."clientId"')
        .getRawMany();
    }

    // Create a map of client ID to last job created date
    const lastJobCreatedMap = new Map<string, Date>();
    lastJobCreatedQuery.forEach((item) => {
      lastJobCreatedMap.set(item.clientId, new Date(item.createdAt));
    });

    // Combine all data for each company
    const allCompanyStats = allCompanies.map((company) => {
      // Get job count from map or default to 0
      const jobCount = jobCountMap.get(company.clientId) || 0;

      // Get video JD count from map or default to 0
      const videoJDCount = videoJDCountMap.get(company.clientId) || 0;

      // Get candidate count from map or default to 0
      const candidateCount = candidateCountMap.get(company.clientId) || 0;

      // Get last job created date from map
      const lastJobCreated = lastJobCreatedMap.get(company.clientId);

      // Determine last activity date
      const lastActivity =
        lastJobCreated && lastJobCreated > company.updatedAt ? lastJobCreated : company.updatedAt;

      return {
        ...company,
        stats: {
          jobCount,
          videoJDCount,
          candidateCount,
          lastActivity,
        },
      };
    });

    // Sort ALL companies by last activity (most recent first)
    allCompanyStats.sort((a, b) => {
      const dateA = new Date(a.stats.lastActivity).getTime();
      const dateB = new Date(b.stats.lastActivity).getTime();
      return dateB - dateA; // Descending order (most recent first)
    });

    // Apply pagination to the sorted results
    const paginatedCompanyStats = allCompanyStats.slice(skip, skip + limit);

    // Return paginated results with stats
    return {
      data: paginatedCompanyStats,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Find company by clientId without auto-creating
   * Returns null if company doesn't exist
   */
  async findByClientIdOrNull(clientId: string): Promise<Company | null> {
    const company = await this.companyRepository.findOne({
      where: { clientId },
    });
    return company || null;
  }
}
