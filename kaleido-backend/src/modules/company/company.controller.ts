import { Public } from '@/auth/public.decorator';
import { GetUser, User } from '@/shared/decorators/get-user.decorator';
import { SubscriptionCreditsType } from '@/shared/enums/subscription-limit-type.enum';
import { SubscriptionPlan } from '@/shared/enums/subscription-plan.enum';
import { Auth0Guard } from '@/shared/guards/auth0.guard';
import { FILE_UPLOAD } from '@/shared/types/constants';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  NotFoundException,
  Param,
  ParseFilePipeBuilder,
  Patch,
  Post,
  Put,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
// import { FileInterceptor } from '@nestjs/platform-express';
import { FastifyFileInterceptor } from '@/shared/interceptors/fastify-file.interceptor';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiQuery, ApiTags } from '@nestjs/swagger';

import { DigitalOceanSpacesService } from '../../shared/services/digital-ocean-spaces.service';
import { SubscriptionService } from '../subscription/subscription.service';
import { CompanyService } from './company.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { Company } from './entities/company.entity';

@ApiTags('companies')
@ApiBearerAuth()
@Controller('companies')
export class CompanyController {
  constructor(
    private readonly companyService: CompanyService,
    private readonly digitalOceanSpacesService: DigitalOceanSpacesService,
    private readonly subscriptionService: SubscriptionService,
  ) {}

  /**
   * Get default credit allocation for STARTER plan
   */
  private getDefaultCredits(): SubscriptionCreditsType {
    return {
      totalCredits: 2500,
      usedCredits: 0,
      remainingCredits: 2500,
      monthlyAllocation: 2500,
      lastResetDate: new Date(),
      videoJdMaxDuration: 90,
      videoJdMonthlyLimit: 3,
      videoJdUsedThisMonth: 0,
      atsIntegration: 'basic',
      databaseRetentionMonths: 3,
    };
  }

  @UseGuards(Auth0Guard)
  @Post()
  create(@Body() createCompanyDto: CreateCompanyDto): Promise<Company> {
    return this.companyService.createOrUpdate(createCompanyDto);
  }

  @UseGuards(Auth0Guard)
  @Get()
  findAll(): Promise<Company[]> {
    return this.companyService.findAll();
  }

  @UseGuards(Auth0Guard)
  @Get('/grouped-by-status')
  findAllGroupedByStatus(): Promise<{
    approved: Company[];
    pending: Company[];
  }> {
    return this.companyService.findAllGroupedByStatus();
  }

  @UseGuards(Auth0Guard)
  @Get('/usage')
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  async getCompanyUsageStats(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('search') search?: string,
  ) {
    return this.companyService.getCompanyUsageStats(page, limit, search);
  }

  @UseGuards(Auth0Guard)
  @Get('/client')
  async finByClientId(@GetUser() authUser: User): Promise<any> {
    try {
      // Try to find the company
      let company;
      try {
        company = await this.companyService.findByClientId(authUser.userId);
      } catch (error: any) {
        // If the error is related to missing columns, try to get the company with raw query
        if (
          error.message &&
          (error.message.includes('column company.subscriptionPlan does not exist') ||
            error.message.includes('column Company.subscriptionPlan does not exist'))
        ) {
          // Get company without subscription fields
          company = await this.companyService.findByClientId(authUser.userId, true);

          // Add default subscription values
          company.subscriptionPlan = SubscriptionPlan.STARTER;
          company.subscriptionCredits = this.getDefaultCredits();
        } else {
          // If it's another type of error, rethrow it
          throw error;
        }
      }

      // Company exists, check if it has required fields filled out
      const isOnboardingRequired = this.isOnboardingRequired(company);

      // Return the company with the onboardingRequired flag
      return {
        ...company,
        onboardingRequired: isOnboardingRequired,
      };
    } catch (error) {
      // Re-throw any errors (including NotFoundException)
      throw error;
    }
  }

  /**
   * Check if onboarding is required based on company data
   * @param company The company to check
   * @returns true if onboarding is required, false otherwise
   */
  private isOnboardingRequired(company: Company): boolean {
    // Check if essential fields are filled out
    return (
      !company.companyName ||
      !company.companyWebsite ||
      !company.contactEmail ||
      !company.industry ||
      !company.size
    );
  }

  @Public()
  @Get('/by-user')
  async findOneBySlugOrUser(@Param('slug') slug?: string): Promise<Company | null> {
    if (!slug) {
      return null;
    }

    return this.companyService.findBySlug(slug);
  }

  // @UseGuards(Auth0Guard)
  @Get(':clientId/by-user-profile')
  async findOneByUserId(@Param('clientId') clientId: string | null): Promise<Company | null> {
    if (clientId) {
      return this.companyService.findByClientId(clientId);
    }

    return null;
  }

  @Public()
  @Get(':id/public')
  async findOneBySlugOrUserPublic(@Param('id') id: string): Promise<Company | null> {
    if (!id) {
      return null;
    }

    return this.companyService.findOne(id);
  }

  @UseGuards(Auth0Guard)
  @Get(':id/summary')
  async getCompanySummary(@Param('id') id: string) {
    // Get lightweight company summary (without heavy queries)
    const company = await this.companyService.findOne(id);

    // Return just the essential data
    return {
      id: company.id,
      clientId: company.clientId,
      companyName: company.companyName,
      industry: company.industry,
      size: company.size,
      location: company.location,
      logo: company.logo,
      createdAt: company.createdAt,
      updatedAt: company.updatedAt,
      isApproved: company.isApproved,
    };
  }

  @UseGuards(Auth0Guard)
  @Get(':id/details')
  async getCompanyDetails(@Param('id') id: string) {
    // Get company details from company service
    const companyDetails = await this.companyService.getCompanyDetails(id);

    // Get subscription information from subscription service
    try {
      const companyWithSubscription = await this.subscriptionService.getCompanyWithSubscription(id);

      // Add subscription information to company details
      return {
        ...companyDetails,
        subscription: {
          plan: companyWithSubscription.subscriptionPlan || SubscriptionPlan.STARTER,
          startDate: companyWithSubscription.subscriptionStartDate,
          endDate: companyWithSubscription.subscriptionEndDate,
          credits: companyWithSubscription.subscriptionCredits || this.getDefaultCredits(),
        },
      };
    } catch (error) {
      console.error('Error getting subscription information:', error);

      // Return company details with default credit-based subscription information
      return {
        ...companyDetails,
        subscription: {
          plan: SubscriptionPlan.STARTER,
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          credits: this.getDefaultCredits(),
        },
      };
    }
  }

  @UseGuards(Auth0Guard)
  @Patch()
  update(@Body() updateCompanyDto: UpdateCompanyDto, @GetUser() authUser: User): Promise<Company> {
    return this.companyService.update(authUser.userId, updateCompanyDto);
  }

  @UseGuards(Auth0Guard)
  @Delete()
  remove(@GetUser() authUser: User): Promise<void> {
    return this.companyService.remove(authUser.userId);
  }

  @UseGuards(Auth0Guard)
  @Post(':id/approve')
  approveCompany(@Param('id') id: string): Promise<Company> {
    return this.companyService.approve(id);
  }

  @UseGuards(Auth0Guard)
  @Post(':id/decline')
  declineCompany(@Param('id') id: string, @Body() body: { reason: string }): Promise<Company> {
    return this.companyService.decline(id, body.reason);
  }

  @UseGuards(Auth0Guard)
  @Post(':id/toggle-demo-mode')
  async toggleDemoMode(
    @Param('id') id: string,
    @Body() body: { isDemoMode: boolean },
  ): Promise<Company> {
    return this.companyService.updateDemoMode(id, body.isDemoMode);
  }

  // @Public()
  @Post('upload-logo')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(new FastifyFileInterceptor('file'))
  async uploadLogo(
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({
          fileType: /(jpg|jpeg|png|webp|webm|gif|svg)$/,
        })
        .addMaxSizeValidator({
          maxSize: FILE_UPLOAD.MAX_SIZE,
        })
        .build({
          errorHttpStatusCode: 400,
          fileIsRequired: true,
        }),
    )
    file: Express.Multer.File,
    @GetUser() authUser: User,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    try {
      // Generate a unique path for the file
      const userId = authUser?.userId || `temp_${Date.now()}`;
      const path = `company-logos/${userId}`;
      const imageUrl = await this.digitalOceanSpacesService.uploadFile(file, path);

      // Only update company if we have a user
      if (authUser?.userId) {
        const company = await this.companyService.findByClientId(authUser.userId);
        if (company) {
          await this.companyService.update(company.id, { logo: imageUrl });
        }
      }

      return { imageUrl };
    } catch (error) {
      console.error('Error uploading company logo:', error);
      throw new BadRequestException('Failed to upload company logo. Please try again.');
    }
  }

  @UseGuards(Auth0Guard)
  @Post(':id/publish')
  async publishCompany(@Param('id') id: string, @Body() body: { isPublished: boolean }) {
    return this.companyService.update(id, { isPublished: body.isPublished } as any);
  }

  // Temporary company endpoints for onboarding without authentication
  @Post('temp')
  @Public()
  async createTempCompany(
    @Body() createCompanyDto: CreateCompanyDto,
    @Headers('x-temp-user-id') tempUserId: string,
  ) {
    if (!tempUserId) {
      throw new BadRequestException('Temporary user ID is required');
    }

    try {
      // Create company with temporary clientId
      const tempCompany = await this.companyService.createOrUpdate({
        ...createCompanyDto,
        clientId: tempUserId,
      });

      return {
        success: true,
        message: 'Temporary company profile created successfully',
        data: tempCompany,
        tempUserId,
      };
    } catch (error: any) {
      console.error('Error creating temporary company:', error);
      throw new BadRequestException(
        `Failed to create temporary company profile: ${error.message || 'Please try again.'}`,
      );
    }
  }

  @Put('temp')
  @Public()
  async updateTempCompany(
    @Body() updateCompanyDto: Partial<CreateCompanyDto>,
    @Headers('x-temp-user-id') tempUserId: string,
  ) {
    if (!tempUserId) {
      throw new BadRequestException('Temporary user ID is required');
    }

    try {
      // Find existing temporary company
      const existingCompany = await this.companyService.findByClientId(tempUserId);

      if (!existingCompany) {
        // Create if doesn't exist
        return this.createTempCompany(updateCompanyDto as CreateCompanyDto, tempUserId);
      }

      // Update existing temporary company
      const updatedCompany = await this.companyService.update(existingCompany.id, {
        ...updateCompanyDto,
        clientId: tempUserId,
      });

      return {
        success: true,
        message: 'Temporary company profile updated successfully',
        data: updatedCompany,
        tempUserId,
      };
    } catch (error: any) {
      console.error('Error updating temporary company:', error);
      throw new BadRequestException(
        `Failed to update temporary company profile: ${error.message || 'Please try again.'}`,
      );
    }
  }

  @Get('temp')
  @Public()
  async getTempCompany(@Headers('x-temp-user-id') tempUserId: string) {
    if (!tempUserId) {
      throw new BadRequestException('Temporary user ID is required');
    }

    try {
      const tempCompany = await this.companyService.findByClientId(tempUserId);

      if (!tempCompany) {
        return {
          success: false,
          message: 'Temporary company profile not found',
          data: null,
          tempUserId,
        };
      }

      return {
        success: true,
        message: 'Temporary company profile retrieved successfully',
        data: tempCompany,
        tempUserId,
      };
    } catch (error: any) {
      console.error('Error retrieving temporary company:', error);
      throw new BadRequestException(
        `Failed to retrieve temporary company profile: ${error.message || 'Please try again.'}`,
      );
    }
  }
}
