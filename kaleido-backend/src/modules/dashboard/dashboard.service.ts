import { Repository } from 'typeorm';

import { SubscriptionPlan } from '@/shared/enums/subscription-plan.enum';
import { JobStatus } from '@/shared/types/job.types';
import { VideoJDStatus } from '@/shared/types/video.types';
import { Candidate, Company, Job, Notification, VideoJD } from '@modules/entities';
import { JobApplication } from '@modules/job-seeker/entities/job-application.entity';
import { JobSeeker } from '@modules/job-seeker/entities/job-seeker.entity';
import { UserRoleEntity } from '@modules/roles/entities/user-role.entity';
import { SubscriptionService } from '@modules/subscription/subscription.service';
import {
  VideoResponse,
  VideoResponseStatus,
} from '@modules/video-response/entities/video-response.entity';
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ApplicationStatus, CandidateStatus, UserRole } from '@shared/types';

import { Graduate } from '../graduate/entities/graduate.entity';
import {
  ApplicationStatsDto,
  DashboardStatsDto,
  EnhancedDashboardStatsDto,
  JobSeekerMetricsDto,
  MatchedJobStatsDto,
  OnboardingStatusDto,
  SkillStatsDto,
  VideoResponseStatsDto,
} from './dto/dashboard-stats.dto';
import {
  EntityRegistrationStatsDto,
  RegistrationStatsDto,
  TimeBasedRegistrationStatsDto,
} from './dto/registration-stats.dto';
import { SubscriptionStatsDto } from './dto/subscription-stats.dto';

@Injectable()
export class DashboardService {
  private readonly logger = new Logger('DashboardService');

  constructor(
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    @InjectRepository(Candidate)
    private readonly candidateRepository: Repository<Candidate>,
    @InjectRepository(VideoJD)
    private readonly videoJDRepository: Repository<VideoJD>,
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
    @InjectRepository(UserRoleEntity)
    private readonly userRoleRepository: Repository<UserRoleEntity>,
    @InjectRepository(VideoResponse)
    private readonly videoResponseRepository: Repository<VideoResponse>,
    @InjectRepository(JobApplication)
    private readonly jobApplicationRepository: Repository<JobApplication>,
    @InjectRepository(JobSeeker)
    private readonly jobSeekerRepository: Repository<JobSeeker>,
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
    @InjectRepository(Graduate)
    private readonly graduateRepository: Repository<Graduate>,
    private readonly subscriptionService: SubscriptionService,
  ) {}

  async getDashboardStats(clientId: string): Promise<DashboardStatsDto> {
    // Get user role
    const userRole = await this.userRoleRepository.findOne({
      where: { clientId },
    });

    if (!userRole) {
      throw new NotFoundException('User role not found');
    }

    switch (userRole.role) {
      case UserRole.EMPLOYER:
        return this.getEmployerStats(clientId, userRole.role);
      case UserRole.JOB_SEEKER:
        return this.getJobSeekerStats(clientId, userRole.role);
      case UserRole.GRADUATE:
        return this.getGraduateStats(clientId, userRole.role);
      case UserRole.ADMIN:
        // Admins get employer-style stats but with their actual role preserved
        return this.getEmployerStats(clientId, userRole.role);
      default:
        // Default to employer stats for backward compatibility but preserve the original role
        const stats = await this.getEmployerStats(clientId, userRole.role);
        return { ...stats, userRole: userRole.role };
    }
  }

  /**
   * Get enhanced dashboard stats that include company data for employers/admins
   * This combines the dashboard stats with company information in a single call
   */
  async getEnhancedDashboardStats(clientId: string): Promise<EnhancedDashboardStatsDto> {
    // Get user role first
    const userRole = await this.userRoleRepository.findOne({
      where: { clientId },
    });

    if (!userRole) {
      throw new NotFoundException('User role not found');
    }

    // Get dashboard stats
    const dashboardStats = await this.getDashboardStats(clientId);

    // For employers and admins, also fetch company data
    if (userRole.role === UserRole.EMPLOYER || userRole.role === UserRole.ADMIN) {
      try {
        // Get company data
        const company = await this.getCompanyData(clientId);

        return {
          ...dashboardStats,
          company,
        };
      } catch (error) {
        this.logger.warn(`Could not fetch company data for clientId ${clientId}:`, error);
        // Return dashboard stats without company data if company fetch fails
        return {
          ...dashboardStats,
          company: null,
        };
      }
    }

    // For non-employers, return just the dashboard stats
    return {
      ...dashboardStats,
      company: null,
    };
  }

  /**
   * Get company data for a given clientId
   * This replicates the logic from the company service
   */
  private async getCompanyData(clientId: string): Promise<any> {
    // Check if the clientId is a valid UUID format
    const isValidUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
      clientId,
    );

    // Build query with only necessary fields
    const queryBuilder = this.companyRepository
      .createQueryBuilder('company')
      .select([
        'company.id',
        'company.clientId',
        'company.companyName',
        'company.companyWebsite',
        'company.contactEmail',
        'company.industry',
        'company.size',
        'company.location',
        'company.logo',
        'company.layoutPreference',
        'company.primaryColor',
        'company.secondaryColor',
        'company.accentColor',
        'company.jobCount',
        'company.candidateUploadCount',
        'company.isDemoMode',
        'company.isApproved',
        'company.createdAt',
        'company.updatedAt',
      ]);

    // Apply the appropriate filter
    if (isValidUuid) {
      queryBuilder.where('company.id = :id', { id: clientId });
    } else {
      queryBuilder.where('company.clientId = :clientId', { clientId });
    }

    const company = await queryBuilder.getOne();

    if (!company) {
      // Create a basic company record if none exists
      return this.createBasicCompany(clientId);
    }

    return company;
  }

  /**
   * Create a basic company record with just the clientId
   * This mirrors the logic from the company service
   */
  private async createBasicCompany(clientId: string): Promise<any> {
    const company = this.companyRepository.create({
      clientId,
      companyName: '',
      companyWebsite: '',
      contactEmail: '',
      preferences: {},
      layoutPreference: 'default',
      primaryColor: '#6366f1',
      secondaryColor: '#4f46e5',
      accentColor: '#4338ca',
      jobCount: 0,
      candidateUploadCount: 0,
      isDemoMode: true,
      companyValues: [],
      featuredImages: [],
    });

    return await this.companyRepository.save(company);
  }

  private async getJobInfoFromCandidate(candidate: any): Promise<any> {
    // Since job is a lazy relation, we need to handle it carefully
    let job = null;
    if (candidate.job) {
      // If job is already loaded (from leftJoinAndSelect), use it directly
      if (typeof candidate.job === 'object' && candidate.job.id) {
        job = candidate.job;
      } else if (candidate.jobId) {
        // If job is not loaded but we have jobId, fetch it
        job = await this.jobRepository.findOne({ where: { id: candidate.jobId } });
      }
    }

    return {
      jobType: job?.jobType || 'Position',
      companyName: job?.companyName || 'Company',
      department: job?.department,
      location: job?.location
        ? Array.isArray(job.location)
          ? job.location
          : [job.location]
        : undefined,
    };
  }

  async getHiredStatus(clientId: string): Promise<{ isHired: boolean; jobInfo?: any }> {
    try {
      this.logger.debug(`Checking hired status for clientId: ${clientId}`);

      // Find the job seeker
      const jobSeeker = await this.jobSeekerRepository.findOne({
        where: { clientId },
      });

      if (!jobSeeker) {
        this.logger.debug(`No job seeker found for clientId: ${clientId}`);
        return { isHired: false, jobInfo: null };
      }

      this.logger.debug(`Found job seeker with ID: ${jobSeeker.id}`);

      // Check all candidates for this clientId and look in their activity history
      // This is the same approach used in getJobApplicationStatusForCandidate
      const candidates = await this.candidateRepository
        .createQueryBuilder('candidate')
        .leftJoinAndSelect('candidate.job', 'job')
        .where('candidate.clientId = :clientId', { clientId })
        .orderBy('candidate.updatedAt', 'DESC')
        .getMany();

      this.logger.debug(`Found ${candidates.length} candidates for clientId`);

      for (const candidate of candidates) {
        this.logger.debug(`Checking candidate ${candidate.id} with status: ${candidate.status}`);

        // Check current status first
        if (candidate.status === CandidateStatus.HIRED) {
          this.logger.debug(`Found hired candidate with current status: ${candidate.id}`);
          const jobInfo = await this.getJobInfoFromCandidate(candidate);
          return {
            isHired: true,
            jobInfo,
          };
        }

        // Check activity history for status changes (same as getJobApplicationStatusForCandidate)
        const activityHistory = candidate.activityHistory || [];
        this.logger.debug(
          `Candidate ${candidate.id} has ${activityHistory.length} activity records`,
        );

        // Look for STATUS_CHANGED activities with HIRED status
        const statusChanges = activityHistory.filter(
          (activity) => activity.type === 'STATUS_CHANGED',
        );

        for (const activity of statusChanges) {
          const newStatus = activity.metadata?.newStatus;
          this.logger.debug(`Status change activity: ${activity.type}, newStatus: ${newStatus}`);

          if (newStatus === CandidateStatus.HIRED) {
            this.logger.debug(
              `Found hired status in activity history for candidate ${candidate.id}`,
            );
            const jobInfo = await this.getJobInfoFromCandidate(candidate);
            return {
              isHired: true,
              jobInfo,
            };
          }
        }
      }

      // Also check job applications as fallback
      const applications = await this.jobApplicationRepository
        .createQueryBuilder('application')
        .leftJoinAndSelect('application.job', 'job')
        .where('application.jobSeekerId = :jobSeekerId', { jobSeekerId: jobSeeker.id })
        .getMany();

      this.logger.debug(`Found ${applications.length} applications for job seeker`);

      for (const application of applications) {
        this.logger.debug(`Application ${application.id}: status = "${application.status}"`);

        if (application.status === ApplicationStatus.HIRED) {
          this.logger.debug(`Found hired application: ${application.id}`);
          return {
            isHired: true,
            jobInfo: {
              jobType: application.job?.jobType || 'Position',
              companyName: application.job?.companyName || 'Company',
              department: application.job?.department,
              location: application.job?.location
                ? Array.isArray(application.job.location)
                  ? application.job.location
                  : [application.job.location]
                : undefined,
            },
          };
        }
      }

      this.logger.debug(`No hired status found for clientId: ${clientId}`);
      return { isHired: false, jobInfo: null };
    } catch (error) {
      this.logger.error('Error checking hired status:', error);
      return { isHired: false, jobInfo: null };
    }
  }

  private async getEmployerStats(
    clientId?: string,
    actualUserRole?: UserRole,
  ): Promise<DashboardStatsDto> {
    const [
      jobStats,
      candidateStats,
      videoJDStats,
      notificationStats,
      culturalFitStats,
      employerMetrics,
      subscriptionStats,
    ] = await Promise.all([
      this.getJobStats(clientId),
      this.getCandidateStats(clientId),
      this.getVideoJDStats(clientId),
      this.getNotificationStats(clientId),
      this.getCulturalFitStats(clientId),
      this.getEmployerMetrics(clientId),
      this.getSubscriptionStats(clientId),
    ]);

    return {
      userRole: actualUserRole || UserRole.EMPLOYER,
      jobs: jobStats,
      candidates: candidateStats,
      videoJDs: videoJDStats,
      notifications: notificationStats,
      culturalFit: culturalFitStats,
      metrics: employerMetrics,
      subscription: subscriptionStats,
    };
  }

  /**
   * Get subscription statistics for a company
   * @param clientId The company's client ID
   * @returns Subscription statistics
   */
  private async getSubscriptionStats(clientId?: string): Promise<SubscriptionStatsDto | undefined> {
    if (!clientId) {
      return undefined;
    }

    try {
      // Check if the clientId is a valid UUID format
      const isValidUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
        clientId,
      );

      // Try to get credit-based subscription data directly from company repository
      const company = await this.companyRepository.findOne({
        where: isValidUuid ? { id: clientId } : { clientId: clientId },
      });

      if (!company) {
        this.logger.warn(`Company not found for clientId: ${clientId}`);
        return undefined;
      }

      // Prioritize credit-based subscription data
      if (company.subscriptionCredits) {
        const daysRemaining = company.subscriptionEndDate
          ? Math.max(
              0,
              Math.ceil(
                (company.subscriptionEndDate.getTime() - new Date().getTime()) /
                  (1000 * 60 * 60 * 24),
              ),
            )
          : 365;

        return {
          plan: (company.subscriptionPlan as SubscriptionPlan) || SubscriptionPlan.FREE,
          credits: company.subscriptionCredits,
          startDate: company.subscriptionStartDate || new Date(),
          endDate: company.subscriptionEndDate,
          autoRenew: company.subscriptionAutoRenew || false,
          daysRemaining,
        };
      }

      // No subscription data found
      this.logger.warn(`No subscription data found for clientId: ${clientId}`);
      return undefined;
    } catch (error) {
      this.logger.error(`Error getting subscription stats for clientId ${clientId}:`, error);
      return undefined;
    }
  }

  /**
   * Calculate percentage with a maximum of 100%
   * @param used The used amount
   * @param limit The limit
   * @returns The percentage (0-100)
   */
  private calculatePercentage(used: number, limit: number): number {
    if (!limit) return 0;
    return Math.min(100, Math.round((used / limit) * 100));
  }

  /**
   * Get comprehensive employer metrics
   */
  private async getEmployerMetrics(clientId?: string) {
    // Get all job seekers, candidates, jobs, and applications
    const jobSeekers = await this.jobSeekerRepository.find();

    const candidatesQuery = this.candidateRepository
      .createQueryBuilder('candidate')
      .leftJoinAndSelect('candidate.job', 'job');

    if (clientId) {
      candidatesQuery.where('job.clientId = :clientId', { clientId });
    }

    const candidates = await candidatesQuery.getMany();

    const jobsQuery = this.jobRepository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.company', 'company');

    if (clientId) {
      jobsQuery.where('company.clientId = :clientId', { clientId });
    }

    const jobs = await jobsQuery.getMany();

    // Calculate time-based metrics
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);

    // Active jobs by time period
    const activeJobs = {
      daily: jobs.filter(
        (job) =>
          job.status !== JobStatus.HIRED &&
          job.status !== JobStatus.REJECTED &&
          job.updatedAt > oneDayAgo,
      ).length,
      weekly: jobs.filter(
        (job) =>
          job.status !== JobStatus.HIRED &&
          job.status !== JobStatus.REJECTED &&
          job.updatedAt > oneWeekAgo,
      ).length,
      monthly: jobs.filter(
        (job) =>
          job.status !== JobStatus.HIRED &&
          job.status !== JobStatus.REJECTED &&
          job.updatedAt > oneMonthAgo,
      ).length,
      yearly: jobs.filter(
        (job) =>
          job.status !== JobStatus.HIRED &&
          job.status !== JobStatus.REJECTED &&
          job.updatedAt > oneYearAgo,
      ).length,
    };

    // Active jobseekers by time period
    const activeJobseekers = {
      daily: jobSeekers.filter((js) => js.updatedAt > oneDayAgo).length,
      weekly: jobSeekers.filter((js) => js.updatedAt > oneWeekAgo).length,
      monthly: jobSeekers.filter((js) => js.updatedAt > oneMonthAgo).length,
      yearly: jobSeekers.filter((js) => js.updatedAt > oneYearAgo).length,
    };

    // Calculate conversion rates
    const totalApplications = candidates.length;
    const totalInterviews = candidates.filter(
      (c) => c.status === CandidateStatus.INTERVIEWING,
    ).length;
    const totalHires = candidates.filter((c) => c.status === CandidateStatus.HIRED).length;

    const applicationToInterviewRate =
      totalApplications > 0 ? (totalInterviews / totalApplications) * 100 : 0;

    const interviewToHireRate = totalInterviews > 0 ? (totalHires / totalInterviews) * 100 : 0;

    const overallConversionRate =
      totalApplications > 0 ? (totalHires / totalApplications) * 100 : 0;

    // Calculate average time metrics (if status history is available)
    let totalTimeToHire = 0;
    let totalTimeToInterview = 0;
    let hiresWithTimeData = 0;
    let interviewsWithTimeData = 0;

    candidates.forEach((candidate) => {
      if (candidate.statusHistory && candidate.statusHistory.length > 0) {
        // Find application date (first status change or creation date)
        const applicationDate = candidate.statusHistory[0]?.changedAt || candidate.createdAt;

        // Find interview date
        const interviewStatus = candidate.statusHistory?.filter(
          (sh: any) => sh.newStatus === CandidateStatus.INTERVIEWING,
        )?.[0];

        if (interviewStatus) {
          const interviewDate = interviewStatus.changedAt;
          const daysToInterview = Math.ceil(
            (interviewDate.getTime() - applicationDate.getTime()) / (1000 * 60 * 60 * 24),
          );

          if (daysToInterview >= 0) {
            totalTimeToInterview += daysToInterview;
            interviewsWithTimeData++;
          }
        }

        // Find hire date
        const hireStatus = candidate.statusHistory?.filter(
          (sh: any) => sh.newStatus === CandidateStatus.HIRED,
        )?.[0];

        if (hireStatus) {
          const hireDate = hireStatus.changedAt;
          const daysToHire = Math.ceil(
            (hireDate.getTime() - applicationDate.getTime()) / (1000 * 60 * 60 * 24),
          );

          if (daysToHire >= 0) {
            totalTimeToHire += daysToHire;
            hiresWithTimeData++;
          }
        }
      }
    });

    const averageTimeToInterview =
      interviewsWithTimeData > 0
        ? Math.round(totalTimeToInterview / interviewsWithTimeData)
        : undefined;

    const averageTimeToHire =
      hiresWithTimeData > 0 ? Math.round(totalTimeToHire / hiresWithTimeData) : undefined;

    return {
      // Overall metrics
      totalJobSeekers: jobSeekers.length,
      totalCandidates: candidates.length,
      totalJobs: jobs.length,
      totalApplications,
      totalHires,
      totalUploads: candidates.filter((c) => c.source === 'RESUME_UPLOAD').length,

      // Conversion rates
      applicationToInterviewRate: Math.round(applicationToInterviewRate),
      interviewToHireRate: Math.round(interviewToHireRate),
      overallConversionRate: Math.round(overallConversionRate),

      // Time-based metrics
      averageTimeToHire,
      averageTimeToInterview,

      // Activity metrics
      activeJobseekers,
      activeJobs,
    };
  }

  private async getJobSeekerStats(
    clientId?: string,
    actualUserRole?: UserRole,
  ): Promise<DashboardStatsDto> {
    const [
      applicationStats,
      matchedJobStats,
      notificationStats,
      onboardingStatus,
      jobSeekerMetrics,
    ] = await Promise.all([
      this.getApplicationStats(clientId),
      this.getMatchedJobStats(clientId),
      this.getNotificationStats(clientId),
      this.getJobSeekerOnboardingStatus(clientId),
      this.getJobSeekerMetrics(clientId),
    ]);

    return {
      userRole: actualUserRole || UserRole.JOB_SEEKER,
      applications: applicationStats,
      matchedJobs: matchedJobStats,
      notifications: notificationStats,
      onboardingStatus,
      jobSeekerMetrics,
    };
  }

  /**
   * Get comprehensive job seeker metrics
   */
  private async getJobSeekerMetrics(clientId?: string): Promise<JobSeekerMetricsDto | undefined> {
    if (!clientId) {
      return undefined;
    }

    // Get the job seeker - try clientId first, then userId as fallback
    let jobSeeker = await this.jobSeekerRepository.findOne({
      where: { clientId },
    });
    if (!jobSeeker) {
      // Fallback: try to find by userId in case clientId is not set properly
      jobSeeker = await this.jobSeekerRepository.findOne({
        where: { userId: clientId },
      });
    }

    if (!jobSeeker) {
      return undefined;
    }

    // Get all applications for this job seeker
    const applications = await this.jobApplicationRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.job', 'job')
      .where('application.jobSeekerId = :jobSeekerId', { jobSeekerId: jobSeeker.id })
      .getMany();

    // Calculate time-based metrics
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);

    // Application activity by time period
    const applicationActivity = {
      daily: applications.filter((app) => app.createdAt > oneDayAgo).length,
      weekly: applications.filter((app) => app.createdAt > oneWeekAgo).length,
      monthly: applications.filter((app) => app.createdAt > oneMonthAgo).length,
      yearly: applications.filter((app) => app.createdAt > oneYearAgo).length,
    };

    // Match activity by time period
    const matchActivity = {
      daily: applications.filter(
        (app) => app.status === ApplicationStatus.SHORTLISTED && app.updatedAt > oneDayAgo,
      ).length,
      weekly: applications.filter(
        (app) => app.status === ApplicationStatus.SHORTLISTED && app.updatedAt > oneWeekAgo,
      ).length,
      monthly: applications.filter(
        (app) => app.status === ApplicationStatus.SHORTLISTED && app.updatedAt > oneMonthAgo,
      ).length,
      yearly: applications.filter(
        (app) => app.status === ApplicationStatus.SHORTLISTED && app.updatedAt > oneYearAgo,
      ).length,
    };

    // Calculate conversion rates
    const totalApplications = applications.length;
    const totalInterviews = applications.filter(
      (app) => app.status === ApplicationStatus.REVIEWING,
    ).length;
    const totalOffers = applications.filter(
      (app) => app.status === ApplicationStatus.SHORTLISTED,
    ).length;
    const totalHires = applications.filter((app) => app.status === ApplicationStatus.HIRED).length;

    const applicationToInterviewRate =
      totalApplications > 0 ? (totalInterviews / totalApplications) * 100 : 0;

    const interviewToOfferRate = totalInterviews > 0 ? (totalOffers / totalInterviews) * 100 : 0;

    const overallSuccessRate = totalApplications > 0 ? (totalHires / totalApplications) * 100 : 0;

    // Calculate average time metrics (if status history is available)
    let totalTimeToInterview = 0;
    let totalTimeToOffer = 0;
    let interviewsWithTimeData = 0;
    let offersWithTimeData = 0;

    // For now, we'll use application creation and update dates as proxies for status changes
    // since JobApplication doesn't have statusHistory
    applications.forEach((application) => {
      const applicationDate = application.createdAt;

      // For interview status, check if the application is in REVIEWING status
      if (application.status === ApplicationStatus.REVIEWING) {
        const interviewDate = application.updatedAt;
        const daysToInterview = Math.ceil(
          (interviewDate.getTime() - applicationDate.getTime()) / (1000 * 60 * 60 * 24),
        );

        if (daysToInterview >= 0) {
          totalTimeToInterview += daysToInterview;
          interviewsWithTimeData++;
        }
      }

      // For offer status, check if the application is in SHORTLISTED status
      if (application.status === ApplicationStatus.SHORTLISTED) {
        const offerDate = application.updatedAt;
        const daysToOffer = Math.ceil(
          (offerDate.getTime() - applicationDate.getTime()) / (1000 * 60 * 60 * 24),
        );

        if (daysToOffer >= 0) {
          totalTimeToOffer += daysToOffer;
          offersWithTimeData++;
        }
      }
    });

    const averageTimeToInterview =
      interviewsWithTimeData > 0
        ? Math.round(totalTimeToInterview / interviewsWithTimeData)
        : undefined;

    const averageTimeToOffer =
      offersWithTimeData > 0 ? Math.round(totalTimeToOffer / offersWithTimeData) : undefined;

    // Generate application trends for the last 30 days
    const applicationTrends = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0);
      const endOfDay = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        23,
        59,
        59,
        999,
      );

      applicationTrends.push({
        date: startOfDay,
        applications: applications.filter(
          (app) => app.createdAt >= startOfDay && app.createdAt <= endOfDay,
        ).length,
        interviews: applications.filter(
          (app) =>
            app.status === ApplicationStatus.REVIEWING &&
            app.updatedAt >= startOfDay &&
            app.updatedAt <= endOfDay,
        ).length,
        offers: applications.filter(
          (app) =>
            app.status === ApplicationStatus.SHORTLISTED &&
            app.updatedAt >= startOfDay &&
            app.updatedAt <= endOfDay,
        ).length,
      });
    }

    // Calculate job engagement metrics
    const jobsByIndustry = applications.reduce(
      (acc, app) => {
        const job = app.job instanceof Promise ? null : app.job;
        if (job) {
          // Use department as a proxy for industry since Job doesn't have industry field
          const industry = job.department || 'Other';
          acc[industry] = (acc[industry] || 0) + 1;
        }
        return acc;
      },
      {} as Record<string, number>,
    );

    const mostMatchedIndustries = Object.entries(jobsByIndustry)
      .map(([industry, count]) => ({
        industry,
        count,
        percentage: totalApplications > 0 ? (count / totalApplications) * 100 : 0,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    const jobsByCategory = applications.reduce(
      (acc, app) => {
        const job = app.job instanceof Promise ? null : app.job;
        if (job) {
          const category = job.department || 'Other';
          acc[category] = (acc[category] || 0) + 1;
        }
        return acc;
      },
      {} as Record<string, number>,
    );

    const mostAppliedCategories = Object.entries(jobsByCategory)
      .map(([category, count]) => ({
        category,
        count,
        percentage: totalApplications > 0 ? (count / totalApplications) * 100 : 0,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Calculate average match score
    let totalMatchScore = 0;
    let matchesWithScore = 0;

    applications.forEach((app) => {
      const job = app.job instanceof Promise ? null : app.job;
      if (job && job.candidates && job.candidates.length > 0) {
        const candidate = job.candidates.filter((c: any) => c.clientId === clientId)?.[0];
        if (candidate && candidate.evaluation && candidate.evaluation.matchScore) {
          totalMatchScore += candidate.evaluation.matchScore;
          matchesWithScore++;
        }
      }
    });

    const averageMatchScore =
      matchesWithScore > 0 ? Math.round((totalMatchScore / matchesWithScore) * 100) : undefined;

    return {
      // Overall metrics
      totalApplications,
      totalMatches: totalOffers,
      totalInterviews,
      totalOffers,
      totalHires,

      // Conversion rates
      applicationToInterviewRate: Math.round(applicationToInterviewRate),
      interviewToOfferRate: Math.round(interviewToOfferRate),
      overallSuccessRate: Math.round(overallSuccessRate),

      // Time-based metrics
      averageTimeToInterview,
      averageTimeToOffer,
      averageResponseTime: 7, // Sample data - would need actual calculation

      // Activity metrics
      applicationActivity,
      matchActivity,

      // Job engagement metrics
      jobEngagement: {
        averageMatchScore,
        mostMatchedIndustries,
        mostAppliedCategories,
      },

      // Application trends
      applicationTrends,
    };
  }

  private async getGraduateStats(
    clientId?: string,
    actualUserRole?: UserRole,
  ): Promise<DashboardStatsDto> {
    const [applicationStats, skillStats, notificationStats] = await Promise.all([
      this.getApplicationStats(clientId),
      this.getSkillStats(clientId),
      this.getNotificationStats(clientId),
    ]);

    return {
      userRole: actualUserRole || UserRole.GRADUATE,
      applications: applicationStats,
      skills: skillStats,
      notifications: notificationStats,
    };
  }

  private async getApplicationStats(clientId?: string): Promise<ApplicationStatsDto> {
    // First get the jobSeeker by clientId, then try userId as fallback
    let jobSeeker = await this.jobSeekerRepository.findOne({
      where: { clientId },
    });
    if (!jobSeeker) {
      // Fallback: try to find by userId in case clientId is not set properly
      jobSeeker = await this.jobSeekerRepository.findOne({
        where: { userId: clientId },
      });
    }

    if (!jobSeeker) {
      return {
        total: 0,
        statusBreakdown: {
          new: 0,
          matched: 0,
          contacted: 0,
          interested: 0,
          notInterested: 0,
          interviewing: 0,
          hired: 0,
          rejected: 0,
          withdrawn: 0,
          culturalFitAnswered: 0,
        },
        recentApplications: [],
      };
    }

    const queryBuilder = this.jobApplicationRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.job', 'job')
      .withDeleted()
      .where('application.jobSeekerId = :jobSeekerId', { jobSeekerId: jobSeeker.id });

    const applications = await queryBuilder.getMany();

    // Calculate time-based metrics
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);

    // Application activity by time period
    const applicationActivity = {
      daily: applications.filter((app) => app.createdAt > oneDayAgo).length,
      weekly: applications.filter((app) => app.createdAt > oneWeekAgo).length,
      monthly: applications.filter((app) => app.createdAt > oneMonthAgo).length,
      yearly: applications.filter((app) => app.createdAt > oneYearAgo).length,
    };

    // Industry and department breakdown
    const byIndustry: Record<string, number> = {};
    const byDepartment: Record<string, number> = {};

    // Process jobs to get industry and department data
    await Promise.all(
      applications.map(async (app) => {
        const job = app.job instanceof Promise ? await app.job : app.job;
        if (job) {
          // Use department as a proxy for industry since Job might not have industry field
          const industry = job.industry || job.department || 'Other';
          byIndustry[industry] = (byIndustry[industry] || 0) + 1;

          const department = job.department || 'Other';
          byDepartment[department] = (byDepartment[department] || 0) + 1;
        }
      }),
    );

    // Generate application trends for the last 30 days
    const applicationTrends = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0);
      const endOfDay = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        23,
        59,
        59,
        999,
      );

      applicationTrends.push({
        date: startOfDay,
        count: applications.filter(
          (app) => app.createdAt >= startOfDay && app.createdAt <= endOfDay,
        ).length,
      });
    }

    return {
      total: applications.length,
      statusBreakdown: {
        new: applications.filter((app) => app.status === ApplicationStatus.PENDING).length,
        matched: applications.filter((app) => app.status === ApplicationStatus.SHORTLISTED).length,
        contacted: applications.filter((app) => app.status === ApplicationStatus.REVIEWING).length,
        interested: applications.filter((app) => app.status === ApplicationStatus.SHORTLISTED)
          .length,
        notInterested: applications.filter((app) => app.status === ApplicationStatus.REJECTED)
          .length,
        interviewing: applications.filter((app) => app.status === ApplicationStatus.REVIEWING)
          .length,
        hired: applications.filter((app) => app.status === ApplicationStatus.HIRED).length,
        rejected: applications.filter((app) => app.status === ApplicationStatus.REJECTED).length,
        withdrawn: applications.filter(
          (app) => app.status === ApplicationStatus.WITHDRAWN && app.deletedAt,
        ).length,
        culturalFitAnswered: applications.filter(
          (app) => app.status === ApplicationStatus.SHORTLISTED,
        ).length,
      },
      recentApplications: await Promise.all(
        applications
          .filter((app) => !app.deletedAt)
          .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
          .slice(0, 5)
          .map(async (app) => {
            const job = app.job instanceof Promise ? await app.job : app.job;
            return {
              id: app.id,
              jobTitle: job?.jobType || '',
              companyName: job?.companyName || '',
              status: app.status,
              appliedAt: app.createdAt,
            };
          }),
      ),
      // Add new fields
      applicationActivity,
      byIndustry,
      byDepartment,
      applicationTrends,
    };
  }

  private async getMatchedJobStats(clientId?: string): Promise<MatchedJobStatsDto> {
    const queryBuilder = this.jobRepository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.candidates', 'candidates')
      .where('candidates.clientId = :clientId', { clientId });

    const matchedJobs = await queryBuilder.getMany();

    // Calculate time-based metrics
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);

    // Match activity by time period
    const matchActivity = {
      daily: matchedJobs.filter((job) => job.createdAt > oneDayAgo).length,
      weekly: matchedJobs.filter((job) => job.createdAt > oneWeekAgo).length,
      monthly: matchedJobs.filter((job) => job.createdAt > oneMonthAgo).length,
      yearly: matchedJobs.filter((job) => job.createdAt > oneYearAgo).length,
    };

    // Location and job type breakdown
    const byLocation: Record<string, number> = {};
    const byJobType: Record<string, number> = {};

    matchedJobs.forEach((job) => {
      // Handle location which might be an array or string
      let locationKey = 'Remote';
      if (job.location) {
        if (Array.isArray(job.location)) {
          locationKey = job.location.join(', ') || 'Remote';
        } else {
          locationKey = job.location as string;
        }
      }
      byLocation[locationKey] = (byLocation[locationKey] || 0) + 1;

      const jobType = job.jobType || 'Other';
      byJobType[jobType] = (byJobType[jobType] || 0) + 1;
    });

    // Generate match trends for the last 30 days
    const matchTrends = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0);
      const endOfDay = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        23,
        59,
        59,
        999,
      );

      const dailyMatches = matchedJobs.filter(
        (job) => job.createdAt >= startOfDay && job.createdAt <= endOfDay,
      );

      // Calculate average match score for the day
      let totalScore = 0;
      dailyMatches.forEach((job) => {
        totalScore += Math.round((job.candidates?.[0]?.evaluation?.matchScore || 0) * 100);
      });
      const averageScore =
        dailyMatches.length > 0 ? Math.round(totalScore / dailyMatches.length) : 0;

      matchTrends.push({
        date: startOfDay,
        count: dailyMatches.length,
        averageScore,
      });
    }

    // Calculate match score distribution
    const matchScores = matchedJobs.map((job) =>
      Math.round((job.candidates?.[0]?.evaluation?.matchScore || 0) * 100),
    );

    const matchScoreDistribution = {
      excellent: matchScores.filter((score) => score >= 90).length,
      good: matchScores.filter((score) => score >= 75 && score < 90).length,
      average: matchScores.filter((score) => score >= 50 && score < 75).length,
      poor: matchScores.filter((score) => score < 50).length,
    };

    return {
      total: matchedJobs.length,
      matchedThisWeek: matchedJobs.filter(
        (job) => job.createdAt > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      ).length,
      byIndustry: matchedJobs.reduce(
        (acc, job) => {
          const department = job.department || 'Other';
          acc[department] = (acc[department] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      ),
      recentMatches: matchedJobs
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .slice(0, 5)
        .map((job) => ({
          id: job.id,
          jobType: job.jobType || '',
          companyName: job.companyName || '',
          matchScore: Math.round((job.candidates?.[0]?.evaluation?.matchScore || 0) * 100),
          matchedAt: job.createdAt,
        })),
      // Add new fields
      matchActivity,
      byLocation,
      byJobType,
      matchTrends,
      matchScoreDistribution,
    };
  }

  private async getSkillStats(clientId?: string): Promise<SkillStatsDto> {
    const candidate = await this.candidateRepository
      .createQueryBuilder('candidate')
      .select(['candidate.id', 'candidate.skills', 'candidate.evaluation'])
      .where('candidate.clientId = :clientId', { clientId })
      .getOne();

    const recentEvaluations = [];
    if (candidate?.evaluation) {
      recentEvaluations.push({
        id: candidate.id,
        skillName: 'Overall Match',
        score: Math.round((candidate.evaluation.matchScore || 0) * 100),
        evaluatedAt: candidate.evaluation.lastEvaluatedAt || new Date(),
      });
    }

    return {
      totalSkills: candidate?.skills?.length || 0,
      skillBreakdown:
        candidate?.skills?.reduce(
          (acc, skill) => {
            acc[skill] = (acc[skill] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>,
        ) || {},
      recentEvaluations,
    };
  }

  /**
   * Get video response stats for a job seeker
   * Note: This method is currently not used in getJobSeekerStats but is kept for future use
   * @eslint-disable-next-line @typescript-eslint/no-unused-vars
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private async getVideoResponseStats(clientId?: string): Promise<VideoResponseStatsDto> {
    const queryBuilder = this.videoResponseRepository
      .createQueryBuilder('videoResponse')
      .leftJoinAndSelect('videoResponse.job', 'job')
      .where('videoResponse.jobSeekerId = :clientId', { clientId });

    const videoResponses = await queryBuilder.getMany();

    return {
      total: videoResponses.length,
      completed: videoResponses.filter((v) => v.status === VideoResponseStatus.COMPLETED).length,
      pending: videoResponses.filter((v) => v.status === VideoResponseStatus.PENDING).length,
      recentResponses: await Promise.all(
        videoResponses
          .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
          .slice(0, 5)
          .map(async (video) => {
            const job = video.job instanceof Promise ? await video.job : video.job;
            return {
              id: video.id,
              jobType: job?.jobType || '',
              status: video.status,
              submittedAt: video.createdAt,
            };
          }),
      ),
    };
  }

  private async getJobStats(clientId?: string) {
    const queryBuilder = this.jobRepository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.candidates', 'candidates')
      .leftJoinAndSelect('job.company', 'company');

    if (clientId) {
      queryBuilder.where('company.clientId = :clientId', { clientId });
    }

    const jobs = await queryBuilder.getMany();

    const recentJobs = jobs
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, 5)
      .map((job) => ({
        id: job.id,
        companyName: job.companyName,
        jobType: job.jobType,
        status: job.status,
        candidateCount: job.candidates?.length || 0,
        createdAt: job.createdAt,
      }));

    // Calculate time-based stats
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);

    // New jobs by time period
    const newJobs = {
      daily: jobs.filter((job) => job.createdAt > oneDayAgo).length,
      weekly: jobs.filter((job) => job.createdAt > oneWeekAgo).length,
      monthly: jobs.filter((job) => job.createdAt > oneMonthAgo).length,
      yearly: jobs.filter((job) => job.createdAt > oneYearAgo).length,
    };

    // Active jobs by time period (not hired or rejected)
    const activeJobs = {
      daily: jobs.filter(
        (job) =>
          job.status !== JobStatus.HIRED &&
          job.status !== JobStatus.REJECTED &&
          job.updatedAt > oneDayAgo,
      ).length,
      weekly: jobs.filter(
        (job) =>
          job.status !== JobStatus.HIRED &&
          job.status !== JobStatus.REJECTED &&
          job.updatedAt > oneWeekAgo,
      ).length,
      monthly: jobs.filter(
        (job) =>
          job.status !== JobStatus.HIRED &&
          job.status !== JobStatus.REJECTED &&
          job.updatedAt > oneMonthAgo,
      ).length,
      yearly: jobs.filter(
        (job) =>
          job.status !== JobStatus.HIRED &&
          job.status !== JobStatus.REJECTED &&
          job.updatedAt > oneYearAgo,
      ).length,
    };

    // Closed jobs by time period (hired or rejected)
    const closedJobs = {
      daily: jobs.filter(
        (job) =>
          (job.status === JobStatus.HIRED || job.status === JobStatus.REJECTED) &&
          job.updatedAt > oneDayAgo,
      ).length,
      weekly: jobs.filter(
        (job) =>
          (job.status === JobStatus.HIRED || job.status === JobStatus.REJECTED) &&
          job.updatedAt > oneWeekAgo,
      ).length,
      monthly: jobs.filter(
        (job) =>
          (job.status === JobStatus.HIRED || job.status === JobStatus.REJECTED) &&
          job.updatedAt > oneMonthAgo,
      ).length,
      yearly: jobs.filter(
        (job) =>
          (job.status === JobStatus.HIRED || job.status === JobStatus.REJECTED) &&
          job.updatedAt > oneYearAgo,
      ).length,
    };

    // Generate trend data for the last 30 days
    const jobTrends = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0);
      const endOfDay = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        23,
        59,
        59,
        999,
      );

      jobTrends.push({
        date: startOfDay,
        newCount: jobs.filter((job) => job.createdAt >= startOfDay && job.createdAt <= endOfDay)
          .length,
        activeCount: jobs.filter(
          (job) =>
            job.status !== JobStatus.HIRED &&
            job.status !== JobStatus.REJECTED &&
            job.updatedAt >= startOfDay &&
            job.updatedAt <= endOfDay,
        ).length,
        closedCount: jobs.filter(
          (job) =>
            (job.status === JobStatus.HIRED || job.status === JobStatus.REJECTED) &&
            job.updatedAt >= startOfDay &&
            job.updatedAt <= endOfDay,
        ).length,
      });
    }

    return {
      total: jobs.length,
      statusBreakdown: {
        new: jobs.filter((job) => job.status === JobStatus.NEW).length,
        matched: jobs.filter((job) => job.status === JobStatus.MATCHED).length,
        open: jobs.filter((job) => job.status === JobStatus.OPEN).length,
        contacted: jobs.filter((job) => job.status === JobStatus.CONTACTED).length,
        interested: jobs.filter((job) => job.status === JobStatus.INTERESTED).length,
        notInterested: jobs.filter((job) => job.status === JobStatus.NOT_INTERESTED).length,
        interviewing: jobs.filter((job) => job.status === JobStatus.INTERVIEWING).length,
        hired: jobs.filter((job) => job.status === JobStatus.HIRED).length,
        rejected: jobs.filter((job) => job.status === JobStatus.REJECTED).length,
      },
      recentJobs,
      newJobs,
      activeJobs,
      closedJobs,
      jobTrends,
    };
  }

  private async getCandidateStats(clientId?: string) {
    const queryBuilder = this.candidateRepository
      .createQueryBuilder('candidate')
      .leftJoinAndSelect('candidate.job', 'job')
      .select([
        'candidate.id',
        'candidate.fullName',
        'candidate.jobTitle',
        'candidate.status',
        'candidate.lastContactDate',
        'candidate.evaluation',
        'candidate.hasCompletedVideoInterview',
        'candidate.source',
        'candidate.createdAt',
        'candidate.updatedAt',
      ]);

    if (clientId) {
      queryBuilder.where('job.clientId = :clientId', { clientId });
    }

    const candidates = await queryBuilder.getMany();

    const recentMatches = candidates
      .filter((candidate) => candidate.status === CandidateStatus.MATCHED)
      .slice(0, 10)
      .map((candidate) => ({
        id: candidate.id,
        fullName: candidate.fullName,
        jobTitle: candidate.jobTitle || '',
        matchScore: Math.round((candidate.evaluation?.matchScore || 0) * 100),
        status: candidate.status,
        lastContactDate: candidate.lastContactDate || new Date(),
      }));

    // Calculate time-based stats
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);

    // New candidates by time period
    const newCandidates = {
      daily: candidates.filter((c) => c.createdAt > oneDayAgo).length,
      weekly: candidates.filter((c) => c.createdAt > oneWeekAgo).length,
      monthly: candidates.filter((c) => c.createdAt > oneMonthAgo).length,
      yearly: candidates.filter((c) => c.createdAt > oneYearAgo).length,
    };

    // Uploaded CVs by time period
    const uploadedCVs = {
      daily: candidates.filter((c) => c.source === 'RESUME_UPLOAD' && c.createdAt > oneDayAgo)
        .length,
      weekly: candidates.filter((c) => c.source === 'RESUME_UPLOAD' && c.createdAt > oneWeekAgo)
        .length,
      monthly: candidates.filter((c) => c.source === 'RESUME_UPLOAD' && c.createdAt > oneMonthAgo)
        .length,
      yearly: candidates.filter((c) => c.source === 'RESUME_UPLOAD' && c.createdAt > oneYearAgo)
        .length,
    };

    // Applied candidates by time period
    const appliedCandidates = {
      daily: candidates.filter((c) => c.source === 'JOB_SEEKER' && c.createdAt > oneDayAgo).length,
      weekly: candidates.filter((c) => c.source === 'JOB_SEEKER' && c.createdAt > oneWeekAgo)
        .length,
      monthly: candidates.filter((c) => c.source === 'JOB_SEEKER' && c.createdAt > oneMonthAgo)
        .length,
      yearly: candidates.filter((c) => c.source === 'JOB_SEEKER' && c.createdAt > oneYearAgo)
        .length,
    };

    // Source breakdown
    const bySource = candidates.reduce(
      (acc, candidate) => {
        const source = candidate.source || 'UNKNOWN';
        acc[source] = (acc[source] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    // Generate trend data for the last 30 days
    const candidateTrends = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const startOfDay = new Date(date.setHours(0, 0, 0, 0));
      const endOfDay = new Date(date.setHours(23, 59, 59, 999));

      candidateTrends.push({
        date: startOfDay,
        newCount: candidates.filter((c) => c.createdAt >= startOfDay && c.createdAt <= endOfDay)
          .length,
        matchedCount: candidates.filter(
          (c) =>
            c.status === CandidateStatus.MATCHED &&
            c.updatedAt >= startOfDay &&
            c.updatedAt <= endOfDay,
        ).length,
        interviewingCount: candidates.filter(
          (c) =>
            c.status === CandidateStatus.INTERVIEWING &&
            c.updatedAt >= startOfDay &&
            c.updatedAt <= endOfDay,
        ).length,
        hiredCount: candidates.filter(
          (c) =>
            c.status === CandidateStatus.HIRED &&
            c.updatedAt >= startOfDay &&
            c.updatedAt <= endOfDay,
        ).length,
      });
    }

    return {
      total: candidates.length,
      matched: candidates.filter((c) => c.status === CandidateStatus.MATCHED).length,
      culturalFitAnswered: candidates.filter((c) => c.hasCompletedVideoInterview).length,
      recentMatches,
      byStatus: candidates.reduce(
        (acc, candidate) => {
          acc[candidate.status] = (acc[candidate.status] || 0) + 1;
          return acc;
        },
        {} as Record<CandidateStatus, number>,
      ),
      newCandidates,
      uploadedCVs,
      appliedCandidates,
      bySource,
      candidateTrends,
    };
  }

  private async getVideoJDStats(clientId?: string) {
    const queryBuilder = this.videoJDRepository
      .createQueryBuilder('videoJD')
      .leftJoinAndSelect('videoJD.job', 'job')
      .where('job.clientId = :clientId', { clientId });

    const videoJDs = await queryBuilder.getMany();

    return {
      total: videoJDs.length,
      completed: videoJDs.filter((v) => v.status === VideoJDStatus.COMPLETED).length,
      pending: videoJDs.filter((v) => v.status !== VideoJDStatus.COMPLETED).length,
      byStatus: videoJDs.reduce(
        (acc, video) => {
          acc[video.status] = (acc[video.status] || 0) + 1;
          return acc;
        },
        {} as Record<VideoJDStatus, number>,
      ),
      recentVideos: videoJDs.slice(0, 5).map((video) => ({
        id: video.id,
        jobType: video.jobType || '',
        status: video.status,
        createdAt: video.createdAt,
      })),
    };
  }

  private async getNotificationStats(clientId?: string) {
    const queryBuilder = this.notificationRepository.createQueryBuilder('notification');

    if (clientId) {
      queryBuilder.where('notification.clientId = :clientId', { clientId });
    }

    const notifications = await queryBuilder.orderBy('notification.createdAt', 'DESC').getMany();

    // Get today's notifications
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayNotifications = notifications.filter((n) => n.createdAt >= today).length;

    // Group notifications by type
    const byType = notifications.reduce(
      (acc, notification) => {
        const type = notification.type || 'Other';
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    return {
      total: notifications.length,
      unread: notifications.filter((n) => !n.read).length,
      today: todayNotifications,
      byType,
      recent: notifications.slice(0, 5).map((notification) => ({
        id: notification.id,
        title: notification.title,
        message: notification.message,
        createdAt: notification.createdAt,
        read: notification.read,
        type: notification.type,
      })),
    };
  }

  private async getCulturalFitStats(clientId?: string) {
    const queryBuilder = this.candidateRepository
      .createQueryBuilder('candidate')
      .leftJoinAndSelect('candidate.job', 'job')
      .leftJoinAndSelect('candidate.videoResponses', 'videoResponses')
      .where('candidate.hasCompletedVideoInterview = :completed', { completed: true });

    if (clientId) {
      queryBuilder.andWhere('job.clientId = :clientId', { clientId });
    }

    const candidates = await queryBuilder.orderBy('candidate.updatedAt', 'DESC').getMany();

    const recentResponses = await Promise.all(
      candidates.slice(0, 5).map(async (candidate) => {
        const job = candidate.job instanceof Promise ? await candidate.job : candidate.job;
        return {
          candidateId: candidate.id,
          fullName: candidate.fullName,
          jobTitle: candidate.jobTitle,
          completedAt: candidate.updatedAt,
          jobName: job?.jobType || '',
          responseCount: candidate.videoResponses?.length || 0,
          matchScore: Math.round((candidate.evaluation?.matchScore || 0) * 100),
        };
      }),
    );

    // Group candidates by job with proper type handling
    const candidatesByJob = await Promise.all(
      candidates.map(async (candidate) => {
        const job = candidate.job instanceof Promise ? await candidate.job : candidate.job;
        return {
          candidate,
          job,
          jobId: candidate.jobId,
        };
      }),
    ).then((candidatesWithJobs) =>
      candidatesWithJobs.reduce(
        (acc, { candidate, jobId }) => {
          // Skip candidates with no jobId
          if (!jobId) return acc;

          if (!acc[jobId]) {
            acc[jobId] = [];
          }
          acc[jobId].push(candidate);
          return acc;
        },
        {} as Record<string, typeof candidates>,
      ),
    );

    const jobBreakdown = await Promise.all(
      Object.entries(candidatesByJob).map(async ([jobId, jobCandidates]) => {
        const firstCandidate = jobCandidates[0];
        const job =
          firstCandidate.job instanceof Promise ? await firstCandidate.job : firstCandidate.job;

        return {
          jobId,
          jobName: job?.jobType || '',
          candidateCount: jobCandidates.length,
          averageScore: Math.round(
            (jobCandidates.reduce((sum, c) => sum + (c.evaluation?.matchScore || 0), 0) /
              jobCandidates.length) *
              100,
          ),
        };
      }),
    );

    return {
      totalAnswered: candidates.length,
      recentResponses,
      jobBreakdown,
      averageMatchScore:
        Math.round(
          (candidates.reduce((sum, c) => sum + (c.evaluation?.matchScore || 0), 0) /
            (candidates.length || 1)) *
            100,
        ) / 100,
    };
  }

  /**
   * Get the job seeker's onboarding status
   * @param clientId The client ID
   * @returns Object containing onboarding progress information
   */
  /**
   * Get registration statistics for admin dashboard
   */
  async getRegistrationStats(): Promise<RegistrationStatsDto> {
    // Calculate time-based metrics
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);

    // Get employer stats - filter out companies without names
    const employers = await this.companyRepository
      .createQueryBuilder('company')
      .select([
        'company.id',
        'company.companyName',
        'company.createdAt',
        'company.updatedAt',
        'company.isApproved',
      ])
      .where('company.companyName IS NOT NULL AND company.companyName != :emptyString', {
        emptyString: '',
      })
      .getMany();
    const employerStats = this.calculateEntityStats(
      employers,
      oneDayAgo,
      oneWeekAgo,
      oneMonthAgo,
      oneYearAgo,
    );

    // Get job seeker stats
    const jobSeekers = await this.jobSeekerRepository.find();
    const jobSeekerStats = this.calculateEntityStats(
      jobSeekers,
      oneDayAgo,
      oneWeekAgo,
      oneMonthAgo,
      oneYearAgo,
    );

    // Get graduate stats
    const graduates = await this.graduateRepository.find();
    const graduateStats = this.calculateEntityStats(
      graduates,
      oneDayAgo,
      oneWeekAgo,
      oneMonthAgo,
      oneYearAgo,
    );

    // Calculate overall stats
    const totalRegistrations = employers.length + jobSeekers.length + graduates.length;
    const pendingApprovals = employerStats.pending + jobSeekerStats.pending + graduateStats.pending;

    // Calculate time-based overall stats
    const registrationsByPeriod: TimeBasedRegistrationStatsDto = {
      daily:
        employerStats.newRegistrations.daily +
        jobSeekerStats.newRegistrations.daily +
        graduateStats.newRegistrations.daily,
      weekly:
        employerStats.newRegistrations.weekly +
        jobSeekerStats.newRegistrations.weekly +
        graduateStats.newRegistrations.weekly,
      monthly:
        employerStats.newRegistrations.monthly +
        jobSeekerStats.newRegistrations.monthly +
        graduateStats.newRegistrations.monthly,
      yearly:
        employerStats.newRegistrations.yearly +
        jobSeekerStats.newRegistrations.yearly +
        graduateStats.newRegistrations.yearly,
    };

    // Generate trend data for the last 30 days
    const overallTrends = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const startOfDay = new Date(date.setHours(0, 0, 0, 0));
      const endOfDay = new Date(date.setHours(23, 59, 59, 999));

      const employerCount = employers.filter(
        (e) => e.createdAt >= startOfDay && e.createdAt <= endOfDay,
      ).length;

      const jobSeekerCount = jobSeekers.filter(
        (js) => js.createdAt >= startOfDay && js.createdAt <= endOfDay,
      ).length;

      const graduateCount = graduates.filter(
        (g) => g.createdAt >= startOfDay && g.createdAt <= endOfDay,
      ).length;

      overallTrends.push({
        date: startOfDay,
        employerCount,
        jobSeekerCount,
        graduateCount,
        totalCount: employerCount + jobSeekerCount + graduateCount,
      });
    }

    return {
      employers: employerStats,
      jobSeekers: jobSeekerStats,
      graduates: graduateStats,
      totalRegistrations,
      pendingApprovals,
      registrationsByPeriod,
      overallTrends,
    };
  }

  /**
   * Get platform usage statistics for admin dashboard - optimized version
   */
  async getUsageStats(page = 1, limit = 10, search?: string) {
    // Use aggregated queries instead of fetching all records

    // Get total counts using COUNT queries (very fast)
    // Filter out companies without a valid name
    const totalCompaniesQuery = this.companyRepository
      .createQueryBuilder('company')
      .where('company.companyName IS NOT NULL AND company.companyName != :emptyString', {
        emptyString: '',
      })
      .getCount();

    const [totalCompanies, totalJobs, totalVideoJDs, totalCandidates] = await Promise.all([
      totalCompaniesQuery,
      this.jobRepository.count(),
      this.videoJDRepository.count(),
      this.candidateRepository.count(),
    ]);

    // Calculate time-based metrics
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Count active companies using a query (not loading all data)
    const activeCompaniesCount = await this.companyRepository
      .createQueryBuilder('company')
      .where('company.companyName IS NOT NULL AND company.companyName != :emptyString', {
        emptyString: '',
      })
      .andWhere('company.updatedAt > :thirtyDaysAgo', { thirtyDaysAgo })
      .getCount();

    // Get industry distribution using aggregation
    const industryDistributionQuery = await this.companyRepository
      .createQueryBuilder('company')
      .select('company.industry', 'industry')
      .addSelect('COUNT(company.id)', 'count')
      .where('company.industry IS NOT NULL')
      .andWhere('company.companyName IS NOT NULL AND company.companyName != :emptyString', {
        emptyString: '',
      })
      .groupBy('company.industry')
      .orderBy('count', 'DESC')
      .limit(7) // Get top 6 + potentially "Other"
      .getRawMany();

    // Process industry distribution
    const topIndustries = industryDistributionQuery.slice(0, 6).map((item) => ({
      name: item.industry,
      value: parseInt(item.count, 10),
    }));

    // If there are more than 6 industries, calculate "Other" count
    if (industryDistributionQuery.length > 6) {
      const otherCount = await this.companyRepository
        .createQueryBuilder('company')
        .where('company.industry IS NOT NULL')
        .andWhere('company.companyName IS NOT NULL AND company.companyName != :emptyString', {
          emptyString: '',
        })
        .andWhere('company.industry NOT IN (:...topIndustries)', {
          topIndustries: topIndustries.map((i) => i.name),
        })
        .getCount();

      if (otherCount > 0) {
        topIndustries.push({ name: 'Other', value: otherCount });
      }
    }

    // Get paginated company data using the optimized company service method
    const companyUsageData = await this.getCompanyUsageDataPaginated(page, limit, search);

    // Calculate platform activity metrics
    const platformActivity = {
      jobsCreated: await this.jobRepository
        .createQueryBuilder('job')
        .where('job.createdAt > :thirtyDaysAgo', { thirtyDaysAgo })
        .getCount(),
      videoJDs: await this.videoJDRepository
        .createQueryBuilder('videoJD')
        .where('videoJD.createdAt > :thirtyDaysAgo', { thirtyDaysAgo })
        .getCount(),
      candidates: await this.candidateRepository
        .createQueryBuilder('candidate')
        .where('candidate.createdAt > :thirtyDaysAgo', { thirtyDaysAgo })
        .getCount(),
    };

    return {
      // Overall stats (fast counts)
      totalCompanies,
      totalJobs,
      totalVideoJDs,
      totalCandidates,
      activeCompanies: activeCompaniesCount,
      industryDistribution: topIndustries,

      // Platform activity
      platformActivity,

      // Paginated company data
      companies: companyUsageData.data,
      pagination: companyUsageData.meta,
    };
  }

  /**
   * Get paginated company usage data - optimized for performance
   */
  private async getCompanyUsageDataPaginated(page = 1, limit = 10, search?: string) {
    const skip = (page - 1) * limit;

    // Build base query for companies
    let companyQueryBuilder = this.companyRepository.createQueryBuilder('company');

    // Apply search filter if provided
    if (search) {
      companyQueryBuilder = companyQueryBuilder.where(
        'company.companyName ILIKE :search OR company.industry ILIKE :search',
        { search: `%${search}%` },
      );
    }

    // Get total count for pagination
    const total = await companyQueryBuilder.getCount();

    // Get paginated companies
    const companies = await companyQueryBuilder
      .select([
        'company.id',
        'company.clientId',
        'company.companyName',
        'company.industry',
        'company.size',
        'company.location',
        'company.logo',
        'company.createdAt',
        'company.updatedAt',
      ])
      .orderBy('company.updatedAt', 'DESC')
      .skip(skip)
      .take(limit)
      .getMany();

    // If no companies, return empty result
    if (companies.length === 0) {
      return {
        data: [],
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    }

    // Get company IDs for batch queries
    const companyIds = companies.map((c) => c.id);

    // Batch query for stats - all in parallel for performance
    // Note: Using proper column references with quotes for case-sensitive PostgreSQL
    const [jobStats, videoJDStats, candidateStats, lastActivityStats] = await Promise.all([
      // Job counts
      this.jobRepository
        .createQueryBuilder('job')
        .leftJoin('job.company', 'company')
        .select('company.id', 'companyId')
        .addSelect('COUNT(job.id)', 'count')
        .where('company.id IN (:...companyIds)', { companyIds })
        .groupBy('company.id')
        .getRawMany(),

      // Video JD counts
      this.videoJDRepository
        .createQueryBuilder('videoJD')
        .innerJoin('videoJD.job', 'job')
        .innerJoin('job.company', 'company')
        .select('company.id', 'companyId')
        .addSelect('COUNT(videoJD.id)', 'count')
        .where('company.id IN (:...companyIds)', { companyIds })
        .groupBy('company.id')
        .getRawMany(),

      // Candidate counts
      this.candidateRepository
        .createQueryBuilder('candidate')
        .innerJoin('candidate.job', 'job')
        .innerJoin('job.company', 'company')
        .select('company.id', 'companyId')
        .addSelect('COUNT(candidate.id)', 'count')
        .where('company.id IN (:...companyIds)', { companyIds })
        .groupBy('company.id')
        .getRawMany(),

      // Last activity (most recent job creation)
      this.jobRepository
        .createQueryBuilder('job')
        .leftJoin('job.company', 'company')
        .select('company.id', 'companyId')
        .addSelect('MAX(job.createdAt)', 'lastActivity')
        .where('company.id IN (:...companyIds)', { companyIds })
        .groupBy('company.id')
        .getRawMany(),
    ]);

    // Create maps for O(1) lookup
    const jobCountMap = new Map(jobStats.map((s) => [s.companyId, parseInt(s.count, 10)]));
    const videoJDCountMap = new Map(videoJDStats.map((s) => [s.companyId, parseInt(s.count, 10)]));
    const candidateCountMap = new Map(
      candidateStats.map((s) => [s.companyId, parseInt(s.count, 10)]),
    );
    const lastActivityMap = new Map(lastActivityStats.map((s) => [s.companyId, s.lastActivity]));

    // Combine data
    const companiesWithStats = companies.map((company) => ({
      ...company,
      jobCount: jobCountMap.get(company.id) || 0,
      videoJDCount: videoJDCountMap.get(company.id) || 0,
      candidateCount: candidateCountMap.get(company.id) || 0,
      lastActivity: lastActivityMap.get(company.id) || company.updatedAt,
    }));

    return {
      data: companiesWithStats,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Calculate entity statistics
   */
  private calculateEntityStats(
    entities: any[],
    oneDayAgo: Date,
    oneWeekAgo: Date,
    oneMonthAgo: Date,
    oneYearAgo: Date,
  ): EntityRegistrationStatsDto {
    // Calculate total counts
    const total = entities.length;
    const approved = entities.filter((e) => e.isApproved === true).length;
    const declined = entities.filter((e) => e.isApproved === false).length;
    const pending = entities.filter(
      (e) => e.isApproved === null || e.isApproved === undefined,
    ).length;

    // Calculate time-based stats
    const newRegistrations: TimeBasedRegistrationStatsDto = {
      daily: entities.filter((e) => e.createdAt > oneDayAgo).length,
      weekly: entities.filter((e) => e.createdAt > oneWeekAgo).length,
      monthly: entities.filter((e) => e.createdAt > oneMonthAgo).length,
      yearly: entities.filter((e) => e.createdAt > oneYearAgo).length,
    };

    // Generate trend data for the last 30 days
    const now = new Date();
    const registrationTrends = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const startOfDay = new Date(date.setHours(0, 0, 0, 0));
      const endOfDay = new Date(date.setHours(23, 59, 59, 999));

      const dailyEntities = entities.filter(
        (e) => e.createdAt >= startOfDay && e.createdAt <= endOfDay,
      );

      registrationTrends.push({
        date: startOfDay,
        count: dailyEntities.length,
        approvedCount: dailyEntities.filter((e) => e.isApproved === true).length,
        declinedCount: dailyEntities.filter((e) => e.isApproved === false).length,
      });
    }

    // Calculate approval rate
    const approvalRate = total > 0 ? (approved / (approved + declined)) * 100 : 0;

    return {
      total,
      approved,
      pending,
      declined,
      newRegistrations,
      registrationTrends,
      approvalRate: Math.round(approvalRate),
    };
  }

  private async getJobSeekerOnboardingStatus(clientId?: string): Promise<OnboardingStatusDto> {
    if (!clientId) {
      return {
        overall: { percentage: 0, completed: false },
        sections: {
          basicInfo: { percentage: 0, completed: false },
          professionalInfo: { percentage: 0, completed: false },
          preferences: { percentage: 0, completed: false },
          additionalInfo: { percentage: 0, completed: false },
        },
        canApplyForJobs: false,
        hasCompletedOnboarding: false,
      };
    }

    // First get the jobSeeker by clientId
    const jobSeeker = await this.jobSeekerRepository.findOne({
      where: { clientId },
    });

    if (!jobSeeker) {
      return {
        overall: { percentage: 0, completed: false },
        sections: {
          basicInfo: { percentage: 0, completed: false },
          professionalInfo: { percentage: 0, completed: false },
          preferences: { percentage: 0, completed: false },
          additionalInfo: { percentage: 0, completed: false },
        },
        canApplyForJobs: false,
        hasCompletedOnboarding: false,
      };
    }

    // If onboarding progress is not set, return default values
    if (!jobSeeker.onboardingProgress) {
      return {
        overall: { percentage: 0, completed: false },
        sections: {
          basicInfo: { percentage: 0, completed: false },
          professionalInfo: { percentage: 0, completed: false },
          preferences: { percentage: 0, completed: false },
          additionalInfo: { percentage: 0, completed: false },
        },
        canApplyForJobs: false,
        hasCompletedOnboarding: false,
      };
    }

    // Extract onboarding progress
    const { basicInfo, professionalInfo, preferences, additionalInfo, overall } =
      jobSeeker.onboardingProgress;

    // Check if videoIntroUrl exists and add it to additionalInfo completedFields if not already there
    if (
      jobSeeker.videoIntroUrl &&
      additionalInfo &&
      additionalInfo.completedFields &&
      !additionalInfo.completedFields.includes('videoIntroUrl')
    ) {
      // Add videoIntroUrl to completedFields
      additionalInfo.completedFields.push('videoIntroUrl');

      // Recalculate percentage for additionalInfo section
      const totalFields = 3; // resume, languages, videoIntroUrl
      const completedCount = additionalInfo.completedFields.length;
      additionalInfo.percentage = Math.round((completedCount / totalFields) * 100);

      // Update the job seeker in the database
      await this.jobSeekerRepository.save(jobSeeker);
    }

    // Check if job seeker can apply for jobs (minimum 70% completion and required sections completed)
    const minimumRequiredPercentage = 70;
    const requiredSectionsCompleted =
      basicInfo.completed && professionalInfo.completed && preferences.completed;
    const canApplyForJobs =
      requiredSectionsCompleted && overall.percentage >= minimumRequiredPercentage;

    return {
      overall: {
        percentage: overall.percentage,
        completed: overall.completed,
        completedAt: overall.completedAt,
      },
      sections: {
        basicInfo: {
          percentage: basicInfo.percentage,
          completed: basicInfo.completed,
          completedAt: basicInfo.completedAt,
          requiredFields: basicInfo.requiredFields,
          completedFields: basicInfo.completedFields,
        },
        professionalInfo: {
          percentage: professionalInfo.percentage,
          completed: professionalInfo.completed,
          completedAt: professionalInfo.completedAt,
          requiredFields: professionalInfo.requiredFields,
          completedFields: professionalInfo.completedFields,
        },
        preferences: {
          percentage: preferences.percentage,
          completed: preferences.completed,
          completedAt: preferences.completedAt,
          requiredFields: preferences.requiredFields,
          completedFields: preferences.completedFields,
        },
        additionalInfo: {
          percentage: additionalInfo.percentage,
          completed: additionalInfo.completed,
          completedAt: additionalInfo.completedAt,
          requiredFields: additionalInfo.requiredFields,
          completedFields: additionalInfo.completedFields,
        },
      },
      lastUpdated: jobSeeker.onboardingProgress.lastUpdated,
      canApplyForJobs,
      hasCompletedOnboarding: jobSeeker.hasCompletedOnboarding,
      isLinkedInImported: jobSeeker.isImportedFromLinkedIn,
      linkedInImportDate: jobSeeker.linkedInImportDate,
    };
  }
}
