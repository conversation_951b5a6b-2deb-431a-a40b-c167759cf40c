import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { GetUser, User } from '@shared/decorators/get-user.decorator';

import { Auth0Guard } from '../../auth/auth.guard';
import { DashboardService } from './dashboard.service';

@ApiTags('dashboard')
@ApiBearerAuth()
@UseGuards(Auth0Guard)
@Controller('dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('stats')
  @ApiOperation({ summary: 'Get dashboard statistics for a user' })
  async getDashboardStats(@GetUser() user: User) {
    return this.dashboardService.getDashboardStats(user.userId);
  }

  @Get('enhanced-stats')
  @ApiOperation({ summary: 'Get enhanced dashboard statistics including company data for a user' })
  async getEnhancedDashboardStats(@GetUser() user: User) {
    return this.dashboardService.getEnhancedDashboardStats(user.userId);
  }

  @Get('registration-stats')
  @ApiOperation({ summary: 'Get registration statistics for admin dashboard' })
  async getRegistrationStats() {
    return this.dashboardService.getRegistrationStats();
  }

  @Get('usage-stats')
  @ApiOperation({ summary: 'Get platform usage statistics for admin dashboard' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for company name or industry',
  })
  async getUsageStats(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('search') search?: string,
  ) {
    return this.dashboardService.getUsageStats(page, limit, search);
  }

  @Get('hired-status')
  @ApiOperation({
    summary: 'Check if job seeker has been hired and get job info for congratulatory modal',
  })
  async getHiredStatus(@GetUser() user: User) {
    return this.dashboardService.getHiredStatus(user.userId);
  }
}
