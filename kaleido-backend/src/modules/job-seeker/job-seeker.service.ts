import { Repository } from 'typeorm';

import { UserRole } from '@/common/enums/role.enum';
import { User } from '@/shared/decorators/get-user.decorator';
import { ResumeProcessorService } from '@/shared/services/resume-processor.service';
import { CandidateService } from '@modules/candidate/candidate.service';
import { Candidate } from '@modules/candidate/entities/candidate.entity';
import { CandidateCreatorUtil } from '@modules/candidate/utils/candidate-creator.util';
import { EmailService } from '@modules/email/email.service';
import { Job } from '@modules/job/entities/job.entity';
import { NotificationService } from '@modules/notification/notification.service';
import { RolesService } from '@modules/roles/roles.service';
import {
  BadRequestException,
  ConflictException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DigitalOceanSpacesService } from '@shared/services/digital-ocean-spaces.service';
import { PdfService } from '@shared/services/pdf.service';
import {
  ApplicationStatus,
  CandidateStatus,
  ERROR_MESSAGES,
  NotificationType,
  ResumeExport,
  VerificationType,
} from '@shared/types';

import { Auth0ManagementService } from '@/auth/auth0-management.service';
import { CompanyService } from '@modules/company/company.service';
import { CreateJobSeekerDraftDto } from './dto/create-job-seeker-draft.dto';
import { CreateJobSeekerDto } from './dto/create-job-seeker.dto';
import { JobApplication } from './entities/job-application.entity';
import { JobSeeker } from './entities/job-seeker.entity';

@Injectable()
export class JobSeekerService {
  private readonly logger = new Logger(JobSeekerService.name);

  constructor(
    @InjectRepository(JobSeeker)
    private readonly jobSeekerRepository: Repository<JobSeeker>,
    @InjectRepository(JobApplication)
    private readonly jobApplicationRepository: Repository<JobApplication>,
    @InjectRepository(Candidate)
    private readonly candidateRepository: Repository<Candidate>,
    @Inject(forwardRef(() => CandidateService))
    private readonly candidateService: CandidateService,
    private readonly notificationService: NotificationService,
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    private readonly pdfService: PdfService,
    private readonly resumeProcessor: ResumeProcessorService,
    private readonly digitalOceanSpacesService: DigitalOceanSpacesService,
    private readonly candidateCreatorUtil: CandidateCreatorUtil,
    private readonly emailService: EmailService,
    private readonly auth0ManagementService: Auth0ManagementService,
    @Inject(forwardRef(() => CompanyService))
    private readonly companyService: CompanyService,
    @Inject(forwardRef(() => RolesService))
    private readonly rolesService: RolesService,
  ) {}

  async create(createJobSeekerDto: CreateJobSeekerDto): Promise<JobSeeker> {
    // CRITICAL: Check user's role first
    if (createJobSeekerDto.clientId) {
      const userRoleEntity = await this.rolesService.findByClientId(createJobSeekerDto.clientId);

      if (userRoleEntity && userRoleEntity.role === UserRole.EMPLOYER) {
        this.logger.error(
          `BLOCKED: Attempted to create job seeker for user ${createJobSeekerDto.clientId} who has employer role.`,
        );
        throw new ConflictException('Cannot create job seeker profile. User has employer role.');
      }

      // If user has job-seeker or graduate role, check if they already have a profile
      if (
        userRoleEntity &&
        (userRoleEntity.role === UserRole.JOB_SEEKER || userRoleEntity.role === UserRole.GRADUATE)
      ) {
        try {
          const existingProfile = await this.getByClientId(createJobSeekerDto.clientId);
          if (existingProfile) {
            this.logger.warn(
              `User ${createJobSeekerDto.clientId} already has a job seeker profile.`,
            );
            return existingProfile;
          }
        } catch (error) {
          // Profile doesn't exist, continue with creation
        }
      }
    }

    const jobSeeker = this.jobSeekerRepository.create({
      ...createJobSeekerDto,
      preferences: createJobSeekerDto.preferences
        ? {
            desiredSalary: createJobSeekerDto.preferences.desiredSalary,
            jobTypes: createJobSeekerDto.preferences.jobTypes || [],
            locations: createJobSeekerDto.preferences.locations || [],
            remotePreference: createJobSeekerDto.preferences.remotePreference,
            industries: createJobSeekerDto.preferences.industries || [],
          }
        : undefined,
    } as JobSeeker);

    const savedJobSeeker = await this.jobSeekerRepository.save(jobSeeker);

    // Send registration notification email to support team
    try {
      const userName =
        `${createJobSeekerDto.firstName || ''} ${createJobSeekerDto.lastName || ''}`.trim() ||
        'Unknown User';
      const userEmail = createJobSeekerDto.email || 'No email provided';

      await this.emailService.sendUserRegistrationNotification('Job Seeker', userName, userEmail, {
        location: createJobSeekerDto.location,
      });
    } catch (emailError) {
      // Log the error but don't fail the job seeker creation
      console.error('Failed to send job seeker registration notification email:', emailError);
    }

    return savedJobSeeker;
  }

  /**
   * Normalizes location data to a proper string format
   */
  private normalizeLocation(location: any): string {
    if (!location) return '';

    // If already a proper string, return as is
    if (typeof location === 'string') {
      try {
        // Check if it's a stringified JSON
        if (location.startsWith('{') && location.endsWith('}')) {
          const parsed = JSON.parse(location);
          if (typeof parsed === 'object') {
            return this.extractLocationFromObject(parsed);
          }
        }
        return location;
      } catch {
        return location;
      }
    }

    // If it's an object, extract meaningful location
    if (typeof location === 'object') {
      return this.extractLocationFromObject(location);
    }

    return String(location);
  }

  /**
   * Extracts a meaningful location string from an object
   */
  private extractLocationFromObject(obj: any): string {
    if (!obj) return '';

    // Common location object patterns
    if (obj.city && obj.country) {
      return `${obj.city}, ${obj.country}`;
    }
    if (obj.city && obj.state) {
      return `${obj.city}, ${obj.state}`;
    }
    if (obj.name) {
      return obj.name;
    }
    if (obj.country) {
      return obj.country;
    }
    if (obj.city) {
      return obj.city;
    }

    // Fallback: join all non-empty string values
    const values = Object.values(obj).filter((v) => v && typeof v === 'string');
    return values.join(', ');
  }

  async uploadResume(
    clientId: string,
    file: Express.Multer.File,
    userId?: string,
  ): Promise<JobSeeker> {
    let jobSeeker = await this.jobSeekerRepository.findOneBy({
      clientId: decodeURIComponent(clientId),
    });

    try {
      // Upload file to Digital Ocean Spaces
      const path = `resumes/${clientId}`;
      const resumeUrl = await this.digitalOceanSpacesService.uploadFile(file, path);

      // Extract text from resume
      let resumeText: string;
      try {
        // Handle different file types
        switch (file.mimetype) {
          case 'application/pdf':
            try {
              const pdfParse = await import('pdf-parse');
              const pdfData = await pdfParse.default(file.buffer);
              resumeText = pdfData.text;
            } catch (error) {
              console.error('PDF parsing error:', error);
              throw new BadRequestException(
                'Failed to parse PDF file. Please ensure it is not corrupted.',
              );
            }
            break;

          case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
            try {
              const mammoth = await import('mammoth');
              const result = await mammoth.default.extractRawText({ buffer: file.buffer });
              resumeText = result.value;
            } catch (error) {
              console.error('DOCX parsing error:', error);
              throw new BadRequestException(
                'Failed to parse DOCX file. Please ensure it is not corrupted.',
              );
            }
            break;

          case 'text/plain':
            resumeText = file.buffer.toString('utf-8');
            break;

          default:
            throw new BadRequestException(
              'Unsupported file type. Please upload a PDF, DOCX, or text file.',
            );
        }
      } catch (error) {
        console.error('Error extracting text from resume:', error);
        throw new BadRequestException(
          'Failed to process resume. Please ensure the file is not corrupted.',
        );
      }

      if (!resumeText || resumeText.trim().length === 0) {
        throw new BadRequestException('Could not extract text from the provided file');
      }

      // Process resume with OpenAI
      const parsedData = await this.resumeProcessor.processResume(resumeText);

      if (!jobSeeker) {
        // Create new job seeker if doesn't exist
        jobSeeker = this.jobSeekerRepository.create({
          userId: userId || decodeURIComponent(clientId), // Use provided userId (Auth0 sub) or fallback to clientId
          clientId: decodeURIComponent(clientId),
          ...parsedData,
          resumeUrl,
          passportId: `JS_${Date.now()}`,
        } as unknown as JobSeeker);
      } else {
        // Update existing job seeker - only add new data, don't override existing data
        // Always update resumeUrl
        jobSeeker.resumeUrl = resumeUrl;

        // Merge data smartly - only update if field is empty or null
        await this.mergeProfileData(jobSeeker, parsedData);
      }

      const savedJobSeeker = await this.jobSeekerRepository.save(jobSeeker);

      return savedJobSeeker;
    } catch (error) {
      console.error('Resume processing error:', error);

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException(
        'Failed to process resume. Please ensure the file is not corrupted and try again.',
      );
    }
  }

  /**
   * Helper function to merge profile data from parsed resume without overriding existing data
   * @param jobSeeker Existing job seeker profile
   * @param parsedData New data parsed from resume
   */
  private async mergeProfileData(jobSeeker: JobSeeker, parsedData: any): Promise<void> {
    // Check if this profile was imported from LinkedIn
    const wasImportedFromLinkedIn = jobSeeker.isImportedFromLinkedIn;

    // Track if name data was updated for Auth0 sync
    let nameUpdated = false;

    // Basic profile fields - handle undefined, null, or empty string cases
    if ((!jobSeeker.firstName || jobSeeker.firstName === 'undefined') && parsedData.firstName) {
      jobSeeker.firstName = parsedData.firstName;
      nameUpdated = true;
    }
    if ((!jobSeeker.lastName || jobSeeker.lastName === 'undefined') && parsedData.lastName) {
      jobSeeker.lastName = parsedData.lastName;
      nameUpdated = true;
    }

    // Update Auth0 profile if name data was updated
    if (nameUpdated && jobSeeker.userId) {
      this.logger.log(
        `Updating Auth0 profile for user ${jobSeeker.userId} with name data from resume`,
      );
      await this.auth0ManagementService.updateUserNameFromResume(
        jobSeeker.userId,
        jobSeeker.firstName,
        jobSeeker.lastName,
      );
    }
    if (!jobSeeker.email && parsedData.email) jobSeeker.email = parsedData.email;
    if (!jobSeeker.phone && parsedData.phone) jobSeeker.phone = parsedData.phone;
    if (!jobSeeker.location && parsedData.location) jobSeeker.location = parsedData.location;
    if (!jobSeeker.summary && parsedData.summary) jobSeeker.summary = parsedData.summary;

    // Merge skills - add new skills but don't duplicate existing ones
    if (parsedData.skills && parsedData.skills.length > 0) {
      const existingSkills = new Set(jobSeeker.skills || []);
      parsedData.skills.forEach((skill: string) => existingSkills.add(skill));
      jobSeeker.skills = Array.from(existingSkills);
    }

    // Merge experience - add new experiences but don't duplicate existing ones
    if (parsedData.experience && parsedData.experience.length > 0) {
      const existingExperience = jobSeeker.experience || [];
      const existingCompanyTitles = new Set(
        existingExperience.map((exp) => `${exp.company}:${exp.title}`),
      );

      parsedData.experience.forEach((newExp: any) => {
        const companyTitleKey = `${newExp.company}:${newExp.title}`;
        if (!existingCompanyTitles.has(companyTitleKey)) {
          // Add source metadata to track where this experience came from
          newExp.source = 'resume';
          existingExperience.push(newExp);
          existingCompanyTitles.add(companyTitleKey);
        }
      });

      jobSeeker.experience = existingExperience;
    }

    // Merge education - add new education entries but don't duplicate existing ones
    if (parsedData.education && parsedData.education.length > 0) {
      const existingEducation = jobSeeker.education || [];
      const existingEducationKeys = new Set(
        existingEducation.map((edu) => `${edu.institution}:${edu.degree}:${edu.field}`),
      );

      parsedData.education.forEach((newEdu: any) => {
        const eduKey = `${newEdu.institution}:${newEdu.degree}:${newEdu.field}`;
        if (!existingEducationKeys.has(eduKey)) {
          // Add source metadata to track where this education came from
          newEdu.source = 'resume';
          existingEducation.push(newEdu);
          existingEducationKeys.add(eduKey);
        }
      });

      jobSeeker.education = existingEducation;
    }

    // Merge certifications - add new certifications but don't duplicate existing ones
    if (parsedData.certifications && parsedData.certifications.length > 0) {
      const existingCertifications = jobSeeker.certifications || [];
      const existingCertKeys = new Set(
        existingCertifications.map((cert) => `${cert.name}:${cert.issuer}`),
      );

      parsedData.certifications.forEach((newCert: any) => {
        const certKey = `${newCert.name}:${newCert.issuer}`;
        if (!existingCertKeys.has(certKey)) {
          // Add source metadata to track where this certification came from
          newCert.source = 'resume';
          existingCertifications.push(newCert);
          existingCertKeys.add(certKey);
        }
      });

      jobSeeker.certifications = existingCertifications;
    }

    // Merge languages - add new languages without duplicates
    if (parsedData.languages && parsedData.languages.length > 0) {
      const existingLanguages = new Set(jobSeeker.languages || []);
      parsedData.languages.forEach((lang: string) => existingLanguages.add(lang));
      jobSeeker.languages = Array.from(existingLanguages);
    }

    // Add LinkedIn URL if not present
    if (!jobSeeker.linkedinUrl && parsedData.linkedinUrl) {
      jobSeeker.linkedinUrl = parsedData.linkedinUrl;
    }

    // Add GitHub URL if not present
    if (!jobSeeker.githubUrl && parsedData.githubUrl) {
      jobSeeker.githubUrl = parsedData.githubUrl;
    }

    // Add portfolio URL if not present
    if (!jobSeeker.portfolioUrl && parsedData.portfolioUrl) {
      jobSeeker.portfolioUrl = parsedData.portfolioUrl;
    }

    // Update metadata to include resume processing info
    if (!jobSeeker.metadata) {
      jobSeeker.metadata = {};
    }

    // Add or initialize resumeProcessing array using type assertion
    const metadata = jobSeeker.metadata as Record<string, any>;
    if (!metadata.resumeProcessing) {
      metadata.resumeProcessing = [];
    }

    // Add resume processing event
    metadata.resumeProcessing.push({
      date: new Date(),
      action: 'merge',
      fields: Object.keys(parsedData),
      preservedLinkedInData: wasImportedFromLinkedIn,
    });

    // Update the job seeker metadata
    jobSeeker.metadata = metadata;

    // Ensure we don't lose the LinkedIn imported flag
    if (wasImportedFromLinkedIn) {
      jobSeeker.isImportedFromLinkedIn = true;
    }
  }

  async applyForJob(
    clientId: string,
    jobId: string,
    coverLetter?: string,
    applicationReason?: string,
  ): Promise<JobApplication> {
    // Try to find job seeker by clientId first, then by userId if not found
    let jobSeeker = await this.jobSeekerRepository.findOneBy({ clientId });
    if (!jobSeeker) {
      // Fallback: try to find by userId in case clientId is not set properly
      jobSeeker = await this.jobSeekerRepository.findOneBy({ userId: clientId });
    }
    if (!jobSeeker) {
      throw new NotFoundException(ERROR_MESSAGES.NOT_FOUND.JOB_SEEKER);
    }

    // Check if the job seeker is eligible to apply
    const eligibility = await this.checkJobApplicationEligibility(jobSeeker.id);
    if (!eligibility.canApply) {
      throw new BadRequestException({
        message: 'Profile is incomplete. Please complete your profile before applying for jobs.',
        details: eligibility,
      });
    }

    const job = await this.jobRepository.findOne({
      where: { id: jobId },
      relations: ['candidates'],
    });
    if (!job) {
      throw new NotFoundException(ERROR_MESSAGES.NOT_FOUND.JOB);
    }

    const existingApplication = await this.jobApplicationRepository.findOne({
      where: { clientId, jobId },
    });
    if (existingApplication) {
      throw new ConflictException(ERROR_MESSAGES.VALIDATION.DUPLICATE_APPLICATION);
    }

    const application = this.jobApplicationRepository.create({
      clientId,
      jobSeekerId: jobSeeker.id,
      jobId,
      coverLetter,
      applicationReason,
      status: ApplicationStatus.APPLIED,
    });
    await this.jobApplicationRepository.save(application);

    // Create or update the candidate using our utility service
    const candidate = await this.candidateCreatorUtil.createOrUpdateFromJobSeeker(jobSeeker, jobId);

    // Update the job's candidates list if the candidate is not already in the list
    if (!job.candidates) {
      job.candidates = [];
    }

    // Check if the candidate is already in the job's candidates list
    const candidateExists = job.candidates.some((c) => c.id === candidate.id);
    if (!candidateExists) {
      job.candidates.push(candidate);
      await this.jobRepository.save(job);
    }

    await this.notificationService.createNotification({
      jobId,
      type: NotificationType.NEW_APPLICATION,
      title: 'New Job Application',
      message: `${jobSeeker.firstName} ${jobSeeker.lastName} has applied for the position`,
    });

    // Ensure clientId consistency for the job seeker
    await this.ensureClientIdConsistency(jobSeeker);

    return application;
  }

  /**
   * Ensures that the clientId field is consistent with userId for a job seeker
   * This helps fix any data inconsistencies that might prevent job applications from working
   */
  private async ensureClientIdConsistency(jobSeeker: JobSeeker): Promise<void> {
    if (!jobSeeker.clientId || jobSeeker.clientId !== jobSeeker.userId) {
      jobSeeker.clientId = jobSeeker.userId;
      await this.jobSeekerRepository.save(jobSeeker);
    }
  }

  /**
   * Ensures that array fields are properly initialized (not null)
   * This helps fix data consistency issues with array fields
   */
  private ensureArrayFieldsInitialized(jobSeeker: JobSeeker): JobSeeker {
    // Ensure all array fields are initialized to empty arrays if null/undefined
    if (!jobSeeker.skills) jobSeeker.skills = [];
    if (!jobSeeker.experience) jobSeeker.experience = [];
    if (!jobSeeker.education) jobSeeker.education = [];
    if (!jobSeeker.certifications) jobSeeker.certifications = [];
    if (!jobSeeker.languages) jobSeeker.languages = [];
    if (!jobSeeker.myValues) jobSeeker.myValues = [];

    // Fix location if it's stored as stringified JSON
    if (jobSeeker.location && typeof jobSeeker.location === 'string') {
      try {
        // Check if location looks like stringified JSON
        if (jobSeeker.location.startsWith('{') && jobSeeker.location.endsWith('}')) {
          const parsed = JSON.parse(jobSeeker.location);
          if (typeof parsed === 'object') {
            // Convert object to meaningful location string
            if (parsed.country && parsed.city) {
              jobSeeker.location = `${parsed.city}, ${parsed.country}`;
            } else if (parsed.country) {
              jobSeeker.location = parsed.country;
            } else if (parsed.name) {
              jobSeeker.location = parsed.name;
            } else {
              // Use first meaningful value found
              jobSeeker.location = Object.values(parsed).filter(Boolean).join(', ');
            }
          }
        }
      } catch (e) {
        // If parsing fails, keep the original string
      }
    }

    return jobSeeker;
  }

  async getApplications(clientId: string): Promise<{
    active: JobApplication[];
    withdrawn: JobApplication[];
    total: number;
    activeCount: number;
    withdrawnCount: number;
  }> {
    // First, find the job seeker to get their ID
    let jobSeeker = await this.jobSeekerRepository.findOneBy({ clientId });
    if (!jobSeeker) {
      // Fallback: try to find by userId in case clientId is not set properly
      jobSeeker = await this.jobSeekerRepository.findOneBy({ userId: clientId });
    }
    if (!jobSeeker) {
      return {
        active: [],
        withdrawn: [],
        total: 0,
        activeCount: 0,
        withdrawnCount: 0,
      };
    }

    // Use createQueryBuilder to be explicit about columns and avoid issues with missing columns
    const allApplications = await this.jobApplicationRepository
      .createQueryBuilder('applications')
      .select([
        'applications.id',
        'applications.jobId',
        'applications.jobSeekerId',
        'applications.clientId',
        'applications.status',
        'applications.createdAt',
        'applications.updatedAt',
        'applications.deletedAt',
        'applications.coverLetter',
        'applications.withdrawalReason',
        // Don't select applicationReason as it might not exist in the database yet
      ])
      .leftJoinAndSelect('applications.job', 'job')
      .addSelect(['job.id', 'job.jobType', 'job.companyName', 'job.department', 'job.location'])
      .where('applications.jobSeekerId = :jobSeekerId', { jobSeekerId: jobSeeker.id })
      .withDeleted() // Include soft-deleted records to get withdrawn applications
      .getMany();

    // Separate active and withdrawn applications
    // Consider applications with deletedAt as withdrawn regardless of status
    const activeApplications = allApplications.filter(
      (app) => app.status !== ApplicationStatus.WITHDRAWN && !app.deletedAt,
    );

    const withdrawnApplications = allApplications.filter(
      (app) => app.status === ApplicationStatus.WITHDRAWN || app.deletedAt,
    );

    return {
      active: activeApplications,
      withdrawn: withdrawnApplications,
      total: allApplications.length,
      activeCount: activeApplications.length,
      withdrawnCount: withdrawnApplications.length,
    };
  }

  async getByClientId(clientId: string): Promise<JobSeeker | null> {
    // Try to find job seeker by clientId first, then by userId if not found
    let jobSeeker = await this.jobSeekerRepository.findOne({
      where: { clientId },
    });
    if (!jobSeeker) {
      // Fallback: try to find by userId in case clientId is not set properly
      jobSeeker = await this.jobSeekerRepository.findOne({
        where: { userId: clientId },
      });
    }

    if (jobSeeker) {
      jobSeeker = this.ensureArrayFieldsInitialized(jobSeeker);
    }

    // If we found a job seeker with a videoIntroUrl, make sure it's marked as completed
    if (jobSeeker && jobSeeker.videoIntroUrl) {
      // Check if we need to update the onboarding progress
      if (
        jobSeeker.onboardingProgress?.additionalInfo &&
        !jobSeeker.onboardingProgress.additionalInfo.completedFields.includes('videoIntroUrl')
      ) {
        // Mark video as completed
        await this.markVideoIntroCompleted(jobSeeker.id);
        // Refresh the job seeker to get the updated onboarding progress
        return await this.jobSeekerRepository.findOne({
          where: { id: jobSeeker.id },
        });
      }
    }

    return jobSeeker;
  }

  async getByUserId(userId: string): Promise<JobSeeker | null> {
    let jobSeeker = await this.jobSeekerRepository.findOne({
      where: { userId },
    });

    if (jobSeeker) {
      jobSeeker = this.ensureArrayFieldsInitialized(jobSeeker);
    }

    // If we found a job seeker with a videoIntroUrl, make sure it's marked as completed
    if (jobSeeker && jobSeeker.videoIntroUrl) {
      // Check if we need to update the onboarding progress
      if (
        jobSeeker.onboardingProgress?.additionalInfo &&
        !jobSeeker.onboardingProgress.additionalInfo.completedFields.includes('videoIntroUrl')
      ) {
        // Mark video as completed
        await this.markVideoIntroCompleted(jobSeeker.id);
        // Refresh the job seeker to get the updated onboarding progress
        return await this.jobSeekerRepository.findOne({
          where: { id: jobSeeker.id },
        });
      }
    }

    return jobSeeker;
  }

  async findByEmail(email: string): Promise<JobSeeker[] | null> {
    // Allow searching for empty email strings
    return this.jobSeekerRepository.find({
      where: { email },
    });
  }

  /**
   * Find JobSeekers by resume filename
   * This helps identify if a JobSeeker was already created from the same resume
   */
  async findByResumeFilename(filename: string): Promise<JobSeeker[]> {
    if (!filename) return [];

    // Use a query to search for JobSeekers with a resumeUrl that contains the filename
    // This is necessary because resumeUrl stores the full path, not just the filename
    const jobSeekers = await this.jobSeekerRepository
      .createQueryBuilder('jobSeeker')
      .where('jobSeeker.resumeUrl LIKE :filename', { filename: `%${filename}%` })
      .getMany();

    this.logger.log(`Found ${jobSeekers.length} JobSeekers with resume filename: ${filename}`);
    return jobSeekers;
  }

  /**
   * Update a JobSeeker's resume URL and metadata
   * This is used when a new version of a resume is uploaded
   */
  async updateResumeAndMetadata(id: string, resumeUrl: string, metadata: any): Promise<void> {
    this.logger.log(`Updating resume and metadata for JobSeeker ${id}`);

    // Use a direct query to update the JobSeeker
    await this.jobSeekerRepository.query(
      `UPDATE job_seekers
       SET "resumeUrl" = $1,
           "metadata" = $2,
           "updatedAt" = CURRENT_TIMESTAMP
       WHERE id = $3::uuid`,
      [resumeUrl, JSON.stringify(metadata), id],
    );

    this.logger.log(`Updated resume and metadata for JobSeeker ${id}`);
  }

  async findOne(id: string): Promise<JobSeeker | null> {
    let jobSeeker = await this.jobSeekerRepository.findOne({
      where: { id },
    });

    if (jobSeeker) {
      jobSeeker = this.ensureArrayFieldsInitialized(jobSeeker);
    }

    if (jobSeeker && jobSeeker.videoIntroUrl) {
      // Check if we need to update the onboarding progress
      if (
        jobSeeker.onboardingProgress?.additionalInfo &&
        !jobSeeker.onboardingProgress.additionalInfo.completedFields.includes('videoIntroUrl')
      ) {
        // Mark video as completed
        await this.markVideoIntroCompleted(id);
        // Refresh the job seeker to get the updated onboarding progress
        return await this.jobSeekerRepository.findOne({
          where: { id },
        });
      }
    }

    return jobSeeker;
  }

  async findOneOrFail(id: string): Promise<JobSeeker> {
    const jobSeeker = await this.jobSeekerRepository.findOne({
      where: { id },
    });

    if (!jobSeeker) {
      throw new NotFoundException(`Job seeker with ID ${id} not found`);
    }

    return jobSeeker;
  }

  /**
   * Find all job seekers with pagination
   * @param page Page number (1-based)
   * @param limit Number of items per page
   * @returns Paginated list of job seekers
   */
  async findAllPaginated(page: number, limit: number) {
    const skip = (page - 1) * limit;

    const [items, totalItems] = await this.jobSeekerRepository.findAndCount({
      skip,
      take: limit,
      order: { createdAt: 'DESC' },
    });

    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
      },
    };
  }

  /**
   * Get job seekers grouped by approval status and ordered by registration date
   */
  async findAllGroupedByStatus(): Promise<{
    approved: JobSeeker[];
    pending: JobSeeker[];
  }> {
    const jobSeekers = await this.jobSeekerRepository.find({
      order: {
        createdAt: 'DESC',
      },
    });

    const approved = jobSeekers.filter((jobSeeker) => jobSeeker.isApproved === true);
    const pending = jobSeekers.filter((jobSeeker) => jobSeeker.isApproved !== true);

    return {
      approved,
      pending,
    };
  }

  /**
   * Approve a job seeker registration
   */
  async approve(id: string): Promise<JobSeeker> {
    const jobSeeker = await this.findOneOrFail(id);

    // Update job seeker with approval status
    jobSeeker.isApproved = true;

    return await this.jobSeekerRepository.save(jobSeeker);
  }

  /**
   * Decline a job seeker registration with a reason
   */
  async decline(id: string, reason: string): Promise<JobSeeker> {
    const jobSeeker = await this.findOneOrFail(id);

    // Update job seeker with decline status and reason
    jobSeeker.isApproved = false;
    jobSeeker.declineReason = reason;

    return await this.jobSeekerRepository.save(jobSeeker);
  }

  async getJobSeekerCandidates(jobSeekerId: string): Promise<Candidate[]> {
    return this.candidateRepository.find({
      where: { jobSeekerId },
      relations: ['job'],
    });
  }

  async withdrawApplication(
    clientId: string,
    applicationId: string,
    reason?: string,
  ): Promise<void> {
    // First, find the job seeker to get their ID
    let jobSeeker = await this.jobSeekerRepository.findOneBy({ clientId });
    if (!jobSeeker) {
      // Fallback: try to find by userId in case clientId is not set properly
      jobSeeker = await this.jobSeekerRepository.findOneBy({ userId: clientId });
    }
    if (!jobSeeker) {
      console.error('Job seeker not found for clientId:', clientId);
      throw new NotFoundException(ERROR_MESSAGES.NOT_FOUND.JOB_SEEKER);
    }

    // Use createQueryBuilder to be explicit about columns and avoid issues with missing columns
    const application = await this.jobApplicationRepository
      .createQueryBuilder('applications')
      .select([
        'applications.id',
        'applications.jobId',
        'applications.jobSeekerId',
        'applications.clientId',
        'applications.status',
        'applications.createdAt',
        'applications.updatedAt',
        'applications.deletedAt',
        'applications.coverLetter',
        'applications.withdrawalReason',
        // Don't select applicationReason as it might not exist in the database yet
      ])
      .leftJoin('applications.job', 'job')
      .addSelect(['job.id', 'job.jobType', 'job.companyName'])
      .where('applications.id = :applicationId', { applicationId })
      .andWhere('applications.jobSeekerId = :jobSeekerId', { jobSeekerId: jobSeeker.id })
      .getOne();

    if (!application) {
      console.error('Application not found:', {
        applicationId,
        jobSeekerId: jobSeeker.id,
      });
      throw new NotFoundException(ERROR_MESSAGES.NOT_FOUND.APPLICATION);
    }

    // Update application status to WITHDRAWN
    application.status = ApplicationStatus.WITHDRAWN;
    application.withdrawalReason = reason || 'No reason provided';

    // Also soft delete the application to maintain backward compatibility
    // with existing code that might rely on deletedAt
    // application.deletedAt = new Date();

    await this.jobApplicationRepository.save(application);

    // Find the candidate for this application
    const candidate = await this.candidateRepository.findOne({
      where: { clientId, jobId: application.jobId },
    });

    if (candidate) {
      // Store previous status for history
      const previousStatus = candidate.status;

      // Update candidate status
      candidate.status = CandidateStatus.WITHDRAWN;

      // Add status history entry using PostgreSQL array operations to avoid malformed array literal errors
      const statusHistoryEntry = {
        previousStatus: previousStatus,
        newStatus: CandidateStatus.WITHDRAWN,
        changedAt: new Date(),
        changedBy: clientId,
        reason: reason || 'Application withdrawn by candidate',
      };

      // Use raw query to safely append to the JSONB array
      await this.candidateRepository.query(
        `UPDATE candidates
         SET status = $1,
             "statusHistory" = COALESCE("statusHistory", ARRAY[]::jsonb[]) || $2::jsonb
         WHERE id = $3::uuid`,
        [CandidateStatus.WITHDRAWN, JSON.stringify([statusHistoryEntry]), candidate.id],
      );
    } else {
      // Fallback if candidate not found
      await this.candidateRepository.update(
        { clientId, jobId: application.jobId },
        { status: CandidateStatus.WITHDRAWN },
      );
    }

    // Send notification with reason if provided
    await this.notificationService.createNotification({
      jobId: application.jobId,
      type: NotificationType.APPLICATION_WITHDRAWN,
      title: 'Application Withdrawn',
      message: reason
        ? `A candidate has withdrawn their application. Reason: ${reason}`
        : 'A candidate has withdrawn their application',
    });

    // TODO: Send email to employer with withdrawal reason
    // TODO: Send confirmation email to candidate
  }

  async withdrawApplicationByJobId(
    clientId: string,
    jobId: string,
    reason?: string,
  ): Promise<void> {
    // First, find the job seeker to get their ID
    let jobSeeker = await this.jobSeekerRepository.findOneBy({ clientId });
    if (!jobSeeker) {
      // Fallback: try to find by userId in case clientId is not set properly
      jobSeeker = await this.jobSeekerRepository.findOneBy({ userId: clientId });
    }
    if (!jobSeeker) {
      console.error('Job seeker not found for clientId:', clientId);
      throw new NotFoundException(ERROR_MESSAGES.NOT_FOUND.JOB_SEEKER);
    }

    // Find the application by jobId and jobSeekerId
    const application = await this.jobApplicationRepository
      .createQueryBuilder('applications')
      .select([
        'applications.id',
        'applications.jobId',
        'applications.jobSeekerId',
        'applications.clientId',
        'applications.status',
        'applications.createdAt',
        'applications.updatedAt',
        'applications.deletedAt',
        'applications.coverLetter',
        'applications.withdrawalReason',
      ])
      .where('applications.jobSeekerId = :jobSeekerId', { jobSeekerId: jobSeeker.id })
      .andWhere('applications.jobId = :jobId', { jobId })
      .getOne();

    if (!application) {
      console.error('Application not found for:', { jobSeekerId: jobSeeker.id, jobId });
      throw new NotFoundException('Application not found for this job');
    }

    // Check if application is already withdrawn
    if (application.status === ApplicationStatus.WITHDRAWN || application.deletedAt) {
      throw new BadRequestException('Application has already been withdrawn');
    }

    // Use the existing withdrawApplication method with the found application ID
    await this.withdrawApplication(clientId, application.id, reason);
  }

  async exportResumeJson(id: string): Promise<ResumeExport> {
    const jobSeeker = await this.jobSeekerRepository.findOneBy({ id });
    if (!jobSeeker) {
      throw new NotFoundException(ERROR_MESSAGES.NOT_FOUND.JOB_SEEKER);
    }

    return {
      '@context': 'https://schema.org',
      '@type': 'Resume',
      identifier: jobSeeker.id,
      name: `${jobSeeker.firstName} ${jobSeeker.lastName}`,
      contact: {
        email: jobSeeker.email,
        phone: jobSeeker.phone,
        location: jobSeeker.location,
      },
      education: jobSeeker.education,
      skills: jobSeeker.skills,
      workExperience: jobSeeker.experience,
      certifications: jobSeeker.certifications,
      languages: jobSeeker.languages,
      urls: {
        linkedin: jobSeeker.linkedinUrl,
        github: jobSeeker.githubUrl,
        portfolio: jobSeeker.portfolioUrl,
        videoIntro: jobSeeker.videoIntroUrl,
      },
      preferences: jobSeeker.preferences,
    };
  }

  async uploadVideoIntro(id: string, file: Express.Multer.File): Promise<JobSeeker> {
    const jobSeeker = await this.jobSeekerRepository.findOneBy({ id });
    if (!jobSeeker) {
      throw new NotFoundException(ERROR_MESSAGES.NOT_FOUND.JOB_SEEKER);
    }

    const videoUrl = `uploads/videos/${file.filename}`;
    jobSeeker.videoIntroUrl = videoUrl;

    // Mark video introduction as completed in onboarding progress
    await this.markVideoIntroCompleted(id);

    return await this.jobSeekerRepository.save(jobSeeker);
  }

  /**
   * Mark the video introduction as completed in the job seeker's onboarding progress
   * @param id The job seeker ID
   */
  async markVideoIntroCompleted(id: string): Promise<JobSeeker> {
    const jobSeeker = await this.jobSeekerRepository.findOneBy({ id });
    if (!jobSeeker) {
      throw new NotFoundException(ERROR_MESSAGES.NOT_FOUND.JOB_SEEKER);
    }

    // Initialize onboarding progress if it doesn't exist
    if (!jobSeeker.onboardingProgress) {
      // Run validation to initialize onboarding progress
      const userObj = {
        sub: jobSeeker.userId,
        userId: jobSeeker.userId,
        email: jobSeeker.email,
      };
      await this.validateMandatoryFields(userObj as User);

      // Refresh job seeker after validation
      const refreshedJobSeeker = await this.jobSeekerRepository.findOneBy({ id });
      if (!refreshedJobSeeker || !refreshedJobSeeker.onboardingProgress) {
        throw new NotFoundException('Failed to validate job seeker profile');
      }
      jobSeeker.onboardingProgress = refreshedJobSeeker.onboardingProgress;
    }

    // Always mark videoIntroUrl as completed if it exists, regardless of whether it's already in completedFields
    if (jobSeeker.videoIntroUrl && jobSeeker.onboardingProgress.additionalInfo) {
      // Ensure videoIntroUrl is in completedFields
      if (!jobSeeker.onboardingProgress.additionalInfo.completedFields.includes('videoIntroUrl')) {
        jobSeeker.onboardingProgress.additionalInfo.completedFields.push('videoIntroUrl');
      }

      // Define all possible fields in additionalInfo section
      const totalFields = Object.keys({
        resume: true,
        languages: true,
        videoIntroUrl: true,
      }).length;

      const completedCount = jobSeeker.onboardingProgress.additionalInfo.completedFields.length;
      const percentage = Math.round((completedCount / totalFields) * 100);

      jobSeeker.onboardingProgress.additionalInfo.percentage = percentage;
      jobSeeker.onboardingProgress.additionalInfo.completed =
        !jobSeeker.onboardingProgress.additionalInfo.mandatoryMissing ||
        jobSeeker.onboardingProgress.additionalInfo.mandatoryMissing.length === 0;

      // Update lastUpdated timestamp
      jobSeeker.onboardingProgress.lastUpdated = new Date();

      // Recalculate overall completion with weighted sections
      const sectionWeights = {
        basicInfo: 0.35, // 35%
        professionalInfo: 0.35, // 35%
        preferences: 0.25, // 25%
        additionalInfo: 0.05, // 5%
      };

      const weightedCompletion =
        jobSeeker.onboardingProgress.basicInfo.percentage * sectionWeights.basicInfo +
        jobSeeker.onboardingProgress.professionalInfo.percentage * sectionWeights.professionalInfo +
        jobSeeker.onboardingProgress.preferences.percentage * sectionWeights.preferences +
        percentage * sectionWeights.additionalInfo;

      jobSeeker.onboardingProgress.overall.percentage = Math.round(weightedCompletion);

      // Save the updated job seeker
      return await this.jobSeekerRepository.save(jobSeeker);
    }

    return jobSeeker;
  }

  async exportResumePdf(id: string): Promise<Buffer> {
    const jobSeeker = await this.jobSeekerRepository.findOneBy({ id });
    if (!jobSeeker) {
      throw new NotFoundException(ERROR_MESSAGES.NOT_FOUND.JOB_SEEKER);
    }

    const resumeData = await this.exportResumeJson(id);
    return this.pdfService.generateResumePdf(resumeData);
  }

  async importFromLinkedIn(clientId: string, linkedInData: unknown): Promise<void> {
    // Implementation pending
  }

  async importFromIndeed(clientId: string, indeedData: unknown): Promise<void> {
    // Implementation pending
  }

  async generateVerificationToken(id: string, type: VerificationType): Promise<void> {
    // Implementation pending
  }

  async verifyCredential(id: string, token: string, type: string): Promise<void> {
    // Implementation pending
  }

  async shareProfile(id: string, recipientPlatform: string): Promise<string> {
    return 'sharing_token';
  }

  async validateMandatoryFields(user: User): Promise<{
    isValid: boolean;
    missingFields: string[];
    mandatoryMissingFields: string[];
    hasCompletedOnboarding: boolean;
    completion: {
      overall: number;
      sections: {
        basicInfo: number;
        professionalInfo: number;
        preferences: number;
        additionalInfo: number;
      };
    };
    record: Partial<JobSeeker>;
  }> {
    try {
      const jobSeeker = await this.findByUser(user);

      if (!jobSeeker) {
        // If no job seeker exists, return a default validation response
        return {
          isValid: false,
          missingFields: ['Profile does not exist'],
          mandatoryMissingFields: ['Profile does not exist'],
          hasCompletedOnboarding: false,
          completion: {
            overall: 0,
            sections: {
              basicInfo: 0,
              professionalInfo: 0,
              preferences: 0,
              additionalInfo: 0,
            },
          },
          record: {},
        };
      }

      // Group fields by section
      const sections = {
        basicInfo: [
          { value: jobSeeker.clientId, field: 'clientId', required: true },
          { value: jobSeeker.firstName, field: 'firstName', required: true },
          { value: jobSeeker.lastName, field: 'lastName', required: true },
          { value: jobSeeker.email, field: 'email', required: true },
          { value: jobSeeker.phone, field: 'phone', required: false },
          { value: jobSeeker.location, field: 'location', required: false },
        ],
        professionalInfo: [
          { value: jobSeeker.summary, field: 'summary', required: false },
          { value: jobSeeker.skills, field: 'skills', required: true },
          { value: jobSeeker.experience, field: 'experience', required: false },
          { value: jobSeeker.education, field: 'education', required: false },
        ],
        preferences: [
          {
            value: jobSeeker.preferences?.jobTypes,
            field: 'jobTypes',
            required: true,
          },
          {
            value: jobSeeker.preferences?.locations,
            field: 'locations',
            required: true,
          },
          {
            value: jobSeeker.preferences?.industries,
            field: 'industries',
            required: false,
          },
          {
            value: jobSeeker.preferences?.remotePreference,
            field: 'remotePreference',
            required: false,
          },
          {
            value: jobSeeker.preferences?.desiredSalary?.currency,
            field: 'desiredSalary.currency',
            required: false,
          },
          {
            value: jobSeeker.preferences?.desiredSalary?.period,
            field: 'desiredSalary.period',
            required: false,
          },
          {
            value: jobSeeker.preferences?.desiredSalary?.min,
            field: 'desiredSalary.min',
            required: false,
          },
          {
            value: jobSeeker.preferences?.desiredSalary?.max,
            field: 'desiredSalary.max',
            required: false,
          },
        ],
        additionalInfo: [
          { value: jobSeeker.languages, field: 'languages', required: false },
          { value: jobSeeker.passportId, field: 'passportId', required: false },
          { value: jobSeeker.resumeUrl, field: 'resume', required: false },
          { value: jobSeeker.videoIntroUrl, field: 'videoIntroUrl', required: false },
        ],
      };

      // Initialize the onboarding progress if it doesn't exist
      if (!jobSeeker.onboardingProgress) {
        jobSeeker.onboardingProgress = {
          basicInfo: {
            completed: false,
            completedAt: null,
            percentage: 0,
            requiredFields: sections.basicInfo.filter((f) => f.required).map((f) => f.field),
            completedFields: [],
          },
          professionalInfo: {
            completed: false,
            completedAt: null,
            percentage: 0,
            requiredFields: sections.professionalInfo.filter((f) => f.required).map((f) => f.field),
            completedFields: [],
          },
          preferences: {
            completed: false,
            completedAt: null,
            percentage: 0,
            requiredFields: sections.preferences.filter((f) => f.required).map((f) => f.field),
            completedFields: [],
          },
          additionalInfo: {
            completed: false,
            completedAt: null,
            percentage: 0,
            requiredFields: sections.additionalInfo.filter((f) => f.required).map((f) => f.field),
            completedFields: [],
          },
          overall: {
            completed: false,
            completedAt: null,
            percentage: 0,
          },
          lastUpdated: new Date(),
        };
      }

      // Calculate completion percentage for each section
      const calculateSectionCompletion = (
        fields: { value: any; field: string; required: boolean }[],
      ): {
        percentage: number;
        missing: string[];
        mandatoryMissing: string[];
        completed: string[];
      } => {
        const sectionMissing: string[] = [];
        const mandatoryMissing: string[] = [];
        const completed: string[] = [];
        let filledFields = 0;

        fields.forEach(({ value, field, required }) => {
          if (Array.isArray(value)) {
            if (!value || value.length === 0) {
              sectionMissing.push(field);
              if (required) mandatoryMissing.push(field);
            } else {
              filledFields++;
              completed.push(field);
            }
          } else if (value === undefined || value === null || value === '') {
            sectionMissing.push(field);
            if (required) mandatoryMissing.push(field);
          } else {
            filledFields++;
            completed.push(field);
          }
        });

        return {
          percentage: Math.round((filledFields / fields.length) * 100),
          missing: sectionMissing,
          mandatoryMissing,
          completed,
        };
      };

      // Calculate completion for each section
      const basicInfoCompletion = calculateSectionCompletion(sections.basicInfo);
      const professionalInfoCompletion = calculateSectionCompletion(sections.professionalInfo);
      const preferencesCompletion = calculateSectionCompletion(sections.preferences);
      const additionalInfoCompletion = calculateSectionCompletion(sections.additionalInfo);

      // Check if videoIntroUrl exists and add it to additionalInfo completedFields if not already there
      if (
        jobSeeker.videoIntroUrl &&
        !additionalInfoCompletion.completed.includes('videoIntroUrl')
      ) {
        additionalInfoCompletion.completed.push('videoIntroUrl');
        // Recalculate percentage for additionalInfo section
        const totalAdditionalFields = sections.additionalInfo.length + 1; // +1 for videoIntroUrl
        additionalInfoCompletion.percentage = Math.round(
          (additionalInfoCompletion.completed.length / totalAdditionalFields) * 100,
        );
      }

      // Calculate overall completion with weighted sections
      const sectionWeights = {
        basicInfo: 0.35, // 35%
        professionalInfo: 0.35, // 35%
        preferences: 0.25, // 25%
        additionalInfo: 0.05, // 5%
      };

      const weightedCompletion =
        basicInfoCompletion.percentage * sectionWeights.basicInfo +
        professionalInfoCompletion.percentage * sectionWeights.professionalInfo +
        preferencesCompletion.percentage * sectionWeights.preferences +
        additionalInfoCompletion.percentage * sectionWeights.additionalInfo;

      const overallCompletion = Math.round(weightedCompletion);

      // Update onboarding progress
      const now = new Date();
      jobSeeker.onboardingProgress.basicInfo = {
        ...jobSeeker.onboardingProgress.basicInfo,
        completed: basicInfoCompletion.mandatoryMissing.length === 0,
        completedAt:
          basicInfoCompletion.mandatoryMissing.length === 0
            ? jobSeeker.onboardingProgress.basicInfo.completedAt || now
            : null,
        percentage: basicInfoCompletion.percentage,
        completedFields: basicInfoCompletion.completed,
      };

      jobSeeker.onboardingProgress.professionalInfo = {
        ...jobSeeker.onboardingProgress.professionalInfo,
        completed: professionalInfoCompletion.mandatoryMissing.length === 0,
        completedAt:
          professionalInfoCompletion.mandatoryMissing.length === 0
            ? jobSeeker.onboardingProgress.professionalInfo.completedAt || now
            : null,
        percentage: professionalInfoCompletion.percentage,
        completedFields: professionalInfoCompletion.completed,
      };

      jobSeeker.onboardingProgress.preferences = {
        ...jobSeeker.onboardingProgress.preferences,
        completed: preferencesCompletion.mandatoryMissing.length === 0,
        completedAt:
          preferencesCompletion.mandatoryMissing.length === 0
            ? jobSeeker.onboardingProgress.preferences.completedAt || now
            : null,
        percentage: preferencesCompletion.percentage,
        completedFields: preferencesCompletion.completed,
      };

      jobSeeker.onboardingProgress.additionalInfo = {
        ...jobSeeker.onboardingProgress.additionalInfo,
        completed: additionalInfoCompletion.mandatoryMissing.length === 0,
        completedAt:
          additionalInfoCompletion.mandatoryMissing.length === 0
            ? jobSeeker.onboardingProgress.additionalInfo.completedAt || now
            : null,
        percentage: additionalInfoCompletion.percentage,
        completedFields: additionalInfoCompletion.completed,
      };

      // Combine all missing fields
      const missingFields = [
        ...basicInfoCompletion.missing,
        ...professionalInfoCompletion.missing,
        ...preferencesCompletion.missing,
        ...additionalInfoCompletion.missing,
      ];

      // Combine all mandatory missing fields
      const mandatoryMissingFields = [
        ...basicInfoCompletion.mandatoryMissing,
        ...professionalInfoCompletion.mandatoryMissing,
        ...preferencesCompletion.mandatoryMissing,
        ...additionalInfoCompletion.mandatoryMissing,
      ];

      // Update overall completion
      jobSeeker.onboardingProgress.overall = {
        completed: mandatoryMissingFields.length === 0,
        completedAt:
          mandatoryMissingFields.length === 0
            ? jobSeeker.onboardingProgress.overall.completedAt || now
            : null,
        percentage: overallCompletion,
      };

      jobSeeker.onboardingProgress.lastUpdated = now;

      // Check if all mandatory fields are filled
      const isValid = mandatoryMissingFields.length === 0;

      // Update hasCompletedOnboarding if all mandatory fields are filled
      if (isValid && !jobSeeker.hasCompletedOnboarding) {
        jobSeeker.hasCompletedOnboarding = true;
      }

      // Save the updated onboarding progress
      await this.jobSeekerRepository.save(jobSeeker);

      return {
        isValid,
        missingFields,
        mandatoryMissingFields,
        hasCompletedOnboarding: jobSeeker.hasCompletedOnboarding,
        completion: {
          overall: overallCompletion,
          sections: {
            basicInfo: basicInfoCompletion.percentage,
            professionalInfo: professionalInfoCompletion.percentage,
            preferences: preferencesCompletion.percentage,
            additionalInfo: additionalInfoCompletion.percentage,
          },
        },
        record: {
          id: jobSeeker.id,
          clientId: jobSeeker.clientId,
          userId: jobSeeker.userId || jobSeeker.clientId,
          firstName: jobSeeker.firstName,
          lastName: jobSeeker.lastName,
          email: jobSeeker.email,
          phone: jobSeeker.phone,
          location: jobSeeker.location,
          summary: jobSeeker.summary,
          skills: jobSeeker.skills,
          experience: jobSeeker.experience,
          resumeUrl: jobSeeker.resumeUrl,
          linkedinUrl: jobSeeker.linkedinUrl,
          githubUrl: jobSeeker.githubUrl,
          education: jobSeeker.education,
          certifications: jobSeeker.certifications,
          languages: jobSeeker.languages,
          myValues: jobSeeker.myValues || [],
          portfolioUrl: jobSeeker.portfolioUrl,
          videoIntroUrl: jobSeeker.videoIntroUrl,
          preferences: jobSeeker.preferences,
          passportId: jobSeeker.passportId,
          verifications: jobSeeker.verifications,
          achievements: jobSeeker.achievements,
          recommendations: jobSeeker.recommendations,
          workAvailability: jobSeeker.workAvailability,
          compensation: jobSeeker.compensation,
          privacySettings: jobSeeker.privacySettings,
          socialProfiles: jobSeeker.socialProfiles,
          metadata: jobSeeker.metadata,
          hasCompletedOnboarding: jobSeeker.hasCompletedOnboarding,
          onboardingProgress: jobSeeker.onboardingProgress,
          createdAt: jobSeeker.createdAt,
          updatedAt: jobSeeker.updatedAt,
        },
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException(ERROR_MESSAGES.NOT_FOUND.JOB_SEEKER || 'Job seeker not found');
    }
  }

  async findByUser(user: User): Promise<JobSeeker | null> {
    try {
      // First try to find by userId (which should be the auth0 sub)
      const userId = user.sub || user.userId;

      if (!userId) {
        throw new BadRequestException('User ID is required');
      }

      // Try to find existing job seeker - first by userId, then by email if not found
      let jobSeeker = await this.jobSeekerRepository.findOne({
        where: { userId },
      });

      if (!jobSeeker && user.email) {
        // Try to find by email as fallback
        jobSeeker = await this.jobSeekerRepository.findOne({
          where: { email: user.email },
        });

        // If found by email, update userId to match
        if (jobSeeker) {
          jobSeeker.userId = userId;
          await this.jobSeekerRepository.save(jobSeeker);
        }
      }

      if (jobSeeker) {
        jobSeeker = this.ensureArrayFieldsInitialized(jobSeeker);
      }

      // If we found a job seeker with a videoIntroUrl, make sure it's marked as completed
      if (jobSeeker && jobSeeker.videoIntroUrl) {
        // Check if we need to update the onboarding progress
        if (
          jobSeeker.onboardingProgress?.additionalInfo &&
          !jobSeeker.onboardingProgress.additionalInfo.completedFields.includes('videoIntroUrl')
        ) {
          // Mark video as completed
          await this.markVideoIntroCompleted(jobSeeker.id);
          // Refresh the job seeker to get the updated onboarding progress
          jobSeeker = await this.jobSeekerRepository.findOne({
            where: { id: jobSeeker.id },
          });
        }
      }

      if (!jobSeeker) {
        // Extract name parts from Auth0 user data
        let firstName = '';
        let lastName = '';

        // Check if we have a full name in any field that we can split
        const fullNameSources = [user.name, user.fullName, user.nickname].filter(Boolean);

        let nameParts: string[] = [];
        for (const source of fullNameSources) {
          if (typeof source === 'string' && source.includes(' ')) {
            nameParts = source.split(' ');
            break;
          }
        }

        // Try to get first name from various sources
        if (user.given_name) {
          firstName = user.given_name;
        } else if (user.firstName) {
          firstName = user.firstName;
        } else if (nameParts.length > 0) {
          firstName = nameParts[0];
        } else if (user.name) {
          firstName = user.name; // Use full name as first name if no space
        }

        // Try to get last name from various sources
        if (user.family_name) {
          lastName = user.family_name;
        } else if (user.lastName) {
          lastName = user.lastName;
        } else if (nameParts.length > 1) {
          lastName = nameParts.slice(1).join(' ');
        }

        // We're returning null here, but we've extracted the name parts
        // which will be used by the controller to create a new job seeker
        // with better data than just 'New User'

        // Add the extracted name parts to the user object so they can be used by the controller
        user.firstName = firstName;
        user.lastName = lastName;

        // Return null with additional user data that can be used to create a new job seeker
        // The frontend should handle this by making a POST request to create a new job seeker
        return null;
      }

      // If we found a job seeker but it has placeholder data (firstName is 'New' or empty, lastName is 'User' or empty),
      // and we have better data from Auth0, update it
      if (
        jobSeeker &&
        (jobSeeker.firstName === 'New' ||
          jobSeeker.firstName === '' ||
          jobSeeker.lastName === 'User' ||
          jobSeeker.lastName === '')
      ) {
        // Extract name parts from Auth0 user data
        let firstName = '';
        let lastName = '';

        // Check if we have a full name in any field that we can split
        const fullNameSources = [user.name, user.fullName, user.nickname].filter(Boolean);

        let nameParts: string[] = [];
        for (const source of fullNameSources) {
          if (typeof source === 'string' && source.includes(' ')) {
            nameParts = source.split(' ');
            break;
          }
        }

        // Try to get first name from various sources
        if (user.given_name) {
          firstName = user.given_name;
        } else if (user.firstName) {
          firstName = user.firstName;
        } else if (nameParts.length > 0) {
          firstName = nameParts[0];
        } else if (user.name) {
          firstName = user.name; // Use full name as first name if no space
        }

        // Try to get last name from various sources
        if (user.family_name) {
          lastName = user.family_name;
        } else if (user.lastName) {
          lastName = user.lastName;
        } else if (nameParts.length > 1) {
          lastName = nameParts.slice(1).join(' ');
        }

        // Only update if we have better data
        if (
          (firstName && jobSeeker.firstName === 'New') ||
          jobSeeker.firstName === '' ||
          (lastName && jobSeeker.lastName === 'User') ||
          jobSeeker.lastName === ''
        ) {
          // Only update the fields that need updating
          if (firstName && (jobSeeker.firstName === 'New' || jobSeeker.firstName === '')) {
            jobSeeker.firstName = firstName;
          }

          if (lastName && (jobSeeker.lastName === 'User' || jobSeeker.lastName === '')) {
            jobSeeker.lastName = lastName;
          }

          await this.jobSeekerRepository.save(jobSeeker);
        }
      }

      return jobSeeker;
    } catch (error: any) {
      console.error('Error in findByUser:', error);
      throw new BadRequestException(
        `Failed to find or create job seeker: ${error.message || 'Unknown error'}`,
      );
    }
  }

  async createDraft(createDraftDto: CreateJobSeekerDraftDto): Promise<JobSeeker> {
    try {
      // Check if a draft already exists for this client
      let jobSeeker = await this.jobSeekerRepository.findOneBy({
        clientId: createDraftDto.clientId,
      });

      // Ensure role is always JOB_SEEKER
      const draftData = {
        ...createDraftDto,
        role: UserRole.JOB_SEEKER,
      };

      if (jobSeeker) {
        // Update existing draft
        Object.assign(jobSeeker, {
          ...draftData,
          updatedAt: new Date(),
        });
      } else {
        // Create new draft
        jobSeeker = this.jobSeekerRepository.create({
          ...draftData,
          userId: draftData.userId || draftData.clientId, // Use provided userId or fallback to clientId
          passportId: `JS_${Date.now()}`,
        } as unknown as JobSeeker);
      }

      const savedJobSeeker = await this.jobSeekerRepository.save(jobSeeker);

      return savedJobSeeker;
    } catch (error) {
      console.error('Error creating job seeker draft:', error);
      throw new BadRequestException('Failed to create job seeker draft. Please try again.');
    }
  }

  async update(id: string, updateJobSeekerDto: CreateJobSeekerDto) {
    try {
      // Fix location if it comes as an object or stringified JSON
      if (updateJobSeekerDto.location) {
        updateJobSeekerDto.location = this.normalizeLocation(updateJobSeekerDto.location);
      }

      // First, find the existing job seeker
      const existingJobSeeker = await this.jobSeekerRepository.findOne({
        where: { id },
      });

      if (!existingJobSeeker) {
        throw new NotFoundException('Job seeker not found');
      }

      // Don't allow updates to these unique identifiers
      const uniqueFields = ['userId', 'clientId', 'email'];
      const changedUniqueFields = uniqueFields.filter((field) => {
        const dtoValue = updateJobSeekerDto[field as keyof CreateJobSeekerDto];
        const existingValue = existingJobSeeker[field as keyof JobSeeker];
        return dtoValue !== undefined && dtoValue !== existingValue;
      });

      if (changedUniqueFields.length > 0) {
        throw new BadRequestException(
          `Cannot update unique identifiers: ${changedUniqueFields.join(', ')}`,
        );
      }

      // Process preferences if present
      let processedPreferences: any = undefined;
      if (updateJobSeekerDto.preferences) {
        processedPreferences = {
          desiredSalary: updateJobSeekerDto.preferences.desiredSalary,
          jobTypes: updateJobSeekerDto.preferences.jobTypes || [],
          locations: updateJobSeekerDto.preferences.locations || [],
          remotePreference: updateJobSeekerDto.preferences.remotePreference,
          industries: updateJobSeekerDto.preferences.industries || [],
        };
      }

      // Process education data if present
      let processedEducation = undefined;
      if (updateJobSeekerDto.education) {
        processedEducation = updateJobSeekerDto.education.map((edu) => ({
          ...edu,
          field: edu.field || 'Not specified',
          degree: edu.degree || 'Not specified',
          institution: edu.institution || 'Not specified',
          startDate: edu.startDate || null,
          endDate: edu.endDate || null,
        }));
      }

      // Process certification data if present
      let processedCertifications = undefined;
      if (updateJobSeekerDto.certifications) {
        processedCertifications = updateJobSeekerDto.certifications.map((cert) => ({
          ...cert,
          name: cert.name || 'Not specified',
          issuer: cert.issuer || 'Not specified',
          issueDate: cert.issueDate || new Date(),
          credentialUrl: !cert.credentialUrl
            ? 'https://example.com'
            : cert.credentialUrl.startsWith('http')
              ? cert.credentialUrl
              : `https://${cert.credentialUrl}`,
        }));
      }

      // Create a partial entity with only the fields we want to update
      const updateData: Record<string, any> = {};

      // Get valid entity fields from metadata
      const validEntityFields = Object.keys(this.jobSeekerRepository.metadata.propertiesMap);

      // Add only valid fields to the update data
      for (const key of Object.keys(updateJobSeekerDto)) {
        // Skip unique fields and handle special cases
        if (uniqueFields.includes(key)) continue;

        if (key === 'preferences' && processedPreferences) {
          updateData.preferences = processedPreferences;
        } else if (key === 'education' && processedEducation) {
          updateData.education = processedEducation;
        } else if (key === 'certifications' && processedCertifications) {
          updateData.certifications = processedCertifications;
        } else if (validEntityFields.includes(key)) {
          updateData[key] = (updateJobSeekerDto as Record<string, any>)[key];
        }
      }

      // Set updated timestamp
      updateData.updatedAt = new Date();

      // Use query builder for more control
      await this.jobSeekerRepository
        .createQueryBuilder()
        .update(JobSeeker)
        .set(updateData)
        .where('id = :id', { id })
        .execute();

      // Fetch updated entity
      const updatedJobSeeker = await this.jobSeekerRepository.findOne({
        where: { id },
      });

      return updatedJobSeeker;
    } catch (error: unknown) {
      this.logger.error(
        `Error updating job seeker: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error.stack : undefined,
      );

      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      if (
        typeof error === 'object' &&
        error !== null &&
        'code' in error &&
        error.code === '23505'
      ) {
        throw new ConflictException(
          'A unique constraint was violated. Cannot update to values that already exist for another record.',
        );
      }

      throw new BadRequestException(
        `Failed to update job seeker: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Partially update a job seeker profile with only the provided fields
   * This is used for LinkedIn data updates and other partial updates
   * where we don't want to validate the entire entity
   */
  async partialUpdate(id: string, updateData: Partial<CreateJobSeekerDto>) {
    try {
      // Fix location if it comes as an object or stringified JSON
      if (updateData.location) {
        updateData.location = this.normalizeLocation(updateData.location);
      }

      // First, find the existing job seeker
      const existingJobSeeker = await this.jobSeekerRepository.findOne({
        where: { id },
      });

      if (!existingJobSeeker) {
        throw new NotFoundException('Job seeker not found');
      }

      // Log the partial update
      this.logger.log(
        `Performing partial update for job seeker ${id} with fields: ${Object.keys(updateData).join(', ')}`,
      );

      // Create a partial entity with only the fields we want to update
      const updateFields: Record<string, any> = {};

      // Get valid entity fields from metadata
      const validEntityFields = Object.keys(this.jobSeekerRepository.metadata.propertiesMap);

      // Add only valid fields to the update data
      for (const key of Object.keys(updateData)) {
        if (validEntityFields.includes(key)) {
          updateFields[key] = (updateData as Record<string, any>)[key];
        }
      }

      // Set updated timestamp
      updateFields.updatedAt = new Date();

      // Use query builder for more control
      await this.jobSeekerRepository
        .createQueryBuilder()
        .update(JobSeeker)
        .set(updateFields)
        .where('id = :id', { id })
        .execute();

      // Fetch updated entity
      const updatedJobSeeker = await this.jobSeekerRepository.findOne({
        where: { id },
      });

      return updatedJobSeeker;
    } catch (error: unknown) {
      this.logger.error(
        `Error in partialUpdate for job seeker: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error.stack : undefined,
      );

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException(
        `Failed to update job seeker: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  async uploadProfileImage(user: User, file: Express.Multer.File): Promise<{ imageUrl: string }> {
    try {
      // Get the job seeker by clientId
      const jobSeeker = await this.findByUser(user);

      if (!jobSeeker) {
        throw new NotFoundException(`Job seeker with ID ${user} not found`);
      }

      // Upload the file to storage (using your existing storage service)
      const path = `profile-images/job-seekers/${user}`;
      const uploadResult = await this.digitalOceanSpacesService.uploadFile(file, path);

      // Update the job seeker with the new image URL
      jobSeeker.myProfileImage = uploadResult;
      await this.jobSeekerRepository.save(jobSeeker);

      // Update any associated candidates
      if (jobSeeker.candidates?.length) {
        for (const candidate of jobSeeker.candidates) {
          candidate.myProfileImage = uploadResult;
          await this.candidateRepository.save(candidate);
        }
      }

      return { imageUrl: uploadResult };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to upload profile image');
    }
  }

  /**
   * Check if a job seeker can apply for jobs based on their onboarding progress
   * @param jobSeekerId Job seeker ID
   * @returns Object containing eligibility status and any missing requirements
   */
  async checkJobApplicationEligibility(jobSeekerId: string): Promise<{
    canApply: boolean;
    missingRequirements: string[];
    completionPercentage: number;
    minimumRequiredPercentage: number;
  }> {
    const jobSeeker = await this.jobSeekerRepository.findOneBy({ id: jobSeekerId });

    if (!jobSeeker) {
      throw new NotFoundException(ERROR_MESSAGES.NOT_FOUND.JOB_SEEKER);
    }

    // The minimum required percentage to apply for jobs
    const minimumRequiredPercentage = 70;

    // If onboarding progress isn't set up, run validation to set it up
    if (!jobSeeker.onboardingProgress) {
      // Get a user object from the job seeker for validation
      const userObj = {
        sub: jobSeeker.userId,
        userId: jobSeeker.userId,
        email: jobSeeker.email,
      };
      await this.validateMandatoryFields(userObj as User);
      // Refresh job seeker after validation
      const refreshedJobSeeker = await this.jobSeekerRepository.findOneBy({ id: jobSeekerId });
      if (!refreshedJobSeeker || !refreshedJobSeeker.onboardingProgress) {
        throw new NotFoundException('Failed to validate job seeker profile');
      }
      jobSeeker.onboardingProgress = refreshedJobSeeker.onboardingProgress;
    }

    // At this point onboardingProgress is guaranteed to exist
    const onboardingProgress = jobSeeker.onboardingProgress;

    // Check if all required sections are completed
    const requiredSectionsCompleted =
      onboardingProgress.basicInfo.completed &&
      onboardingProgress.professionalInfo.completed &&
      onboardingProgress.preferences.completed;

    // Check overall completion percentage
    const overallCompletion = onboardingProgress.overall.percentage;

    // Determine if they can apply based on required sections and minimum percentage
    const canApply = requiredSectionsCompleted && overallCompletion >= minimumRequiredPercentage;

    // Collect missing requirements
    const missingRequirements: string[] = [];

    if (!onboardingProgress.basicInfo.completed) {
      const missing = onboardingProgress.basicInfo.requiredFields.filter(
        (field) => !onboardingProgress.basicInfo.completedFields.includes(field),
      );
      missingRequirements.push(`Basic Information: ${missing.join(', ')}`);
    }

    if (!onboardingProgress.professionalInfo.completed) {
      const missing = onboardingProgress.professionalInfo.requiredFields.filter(
        (field) => !onboardingProgress.professionalInfo.completedFields.includes(field),
      );
      missingRequirements.push(`Professional Information: ${missing.join(', ')}`);
    }

    if (!onboardingProgress.preferences.completed) {
      const missing = onboardingProgress.preferences.requiredFields.filter(
        (field) => !onboardingProgress.preferences.completedFields.includes(field),
      );
      missingRequirements.push(`Job Preferences: ${missing.join(', ')}`);
    }

    if (overallCompletion < minimumRequiredPercentage) {
      missingRequirements.push(
        `Overall profile completion (${overallCompletion}%) is below required minimum (${minimumRequiredPercentage}%)`,
      );
    }

    return {
      canApply,
      missingRequirements,
      completionPercentage: overallCompletion,
      minimumRequiredPercentage,
    };
  }

  /**
   * Find a job by its ID
   */
  async findJobById(jobId: string): Promise<Job | null> {
    return await this.jobRepository.findOne({
      where: { id: jobId },
      relations: ['candidates', 'company'],
    });
  }
}
