import { Column, <PERSON>tity, <PERSON>, Join<PERSON><PERSON>umn, OneToMany, OneToOne } from 'typeorm';

import { Candidate } from '@modules/candidate/entities/candidate.entity';
import { ReferralPartner } from '@modules/referral/entities/referral-partner.entity';
import { ActivityHistory } from '@shared/entities/activity-history.entity';
import { BaseEntity } from '@shared/entities/base.entity';
import { ContactMethod } from '@shared/enums';
import { CandidateExperience, CandidateStatus, Education, UserRole } from '@shared/types';
import {
  Achievement,
  Certification,
  Compensation,
  JobPreferences,
  JobSeekerMetadata,
  PrivacySettings,
  Recommendation,
  SocialProfile,
  Verifications,
  WorkAvailability,
} from '@shared/types/job-seeker.types';

import { JobApplication } from './job-application.entity';

/**
 * EmailCorrespondence represents an email sent to or received from a job seeker
 */
export interface EmailCorrespondence {
  id: string;
  type: 'SENT' | 'RECEIVED';
  subject: string;
  content: string;
  from: string;
  to: string;
  timestamp: Date;
  emailType: 'interview' | 'offer' | 'status' | 'general';
  metadata?: {
    jobId?: string;
    jobTitle?: string;
    companyName?: string;
    interviewDate?: string;
    meetingLink?: string;
    offerDetails?: any;
    [key: string]: any;
  };
  isRead?: boolean;
  requiresAction?: boolean;
  actionType?: 'ACCEPT_INTERVIEW' | 'DECLINE_INTERVIEW' | 'ACCEPT_OFFER' | 'DECLINE_OFFER';
  actionCompleted?: boolean;
  actionCompletedAt?: Date;
}

@Entity('job_seekers')
@Index('idx_job_seekers_user_id', ['userId'])
@Index('idx_job_seekers_email', ['email'])
@Index('idx_job_seekers_client_id', ['clientId'])
@Index('idx_job_seekers_location', ['location'])
@Index('idx_job_seekers_role', ['role'])
export class JobSeeker extends BaseEntity {
  @Column({ type: 'varchar', unique: true })
  userId!: string;

  @Column({ type: 'varchar', nullable: true })
  clientId?: string;

  @Column({ type: 'varchar' })
  firstName!: string;

  @Column({ type: 'varchar' })
  lastName!: string;

  @Column({ type: 'varchar', unique: true })
  email!: string;

  @Column({ type: 'varchar', nullable: true })
  phone?: string;

  @Column({ type: 'varchar', nullable: true })
  location?: string;

  @Column({ type: 'varchar', nullable: true })
  myProfileImage?: string;

  @Column({ type: 'text', nullable: true })
  summary?: string;

  @Column('text', { array: true, nullable: true, default: () => 'ARRAY[]::text[]' })
  skills!: string[];

  @Column('jsonb', { nullable: true, default: () => "'[]'::jsonb" })
  experience?: CandidateExperience[];

  @Column({ type: 'varchar', nullable: true })
  resumeUrl?: string;

  @Column({ type: 'varchar', nullable: true, default: 'modern' })
  resumeTemplate?: 'modern' | 'professional' | 'creative';

  @Column({ type: 'text', nullable: true })
  resumeHtml?: string;

  @Column({ type: 'varchar', nullable: true, default: 'modern' })
  resumeMobileTemplate?: 'modern' | 'professional' | 'creative';

  @Column({ type: 'text', nullable: true })
  resumeMobileHtml?: string;

  @Column({ type: 'varchar', nullable: true })
  linkedinUrl?: string;

  @Column({ type: 'varchar', nullable: true })
  githubUrl?: string;

  @Column({ type: 'enum', enum: UserRole, default: UserRole.JOB_SEEKER })
  role!: UserRole;

  @OneToMany(() => JobApplication, (application) => application.jobSeeker)
  applications?: JobApplication[];

  @OneToMany(() => Candidate, (candidate) => candidate.jobSeeker)
  candidates?: Candidate[];

  @Column({
    type: 'jsonb',
    nullable: true,
    default: () => "'[]'::jsonb",
    transformer: {
      to: (value: Education[]) => {
        if (!value) return [];
        return value.map((edu) => ({
          ...edu,
          startDate: edu.startDate ? new Date(edu.startDate) : null,
          endDate: edu.endDate ? new Date(edu.endDate) : null,
        }));
      },
      from: (value: Education[]) => value || [],
    },
  })
  education?: Education[];

  @Column('jsonb', { nullable: true, default: () => "'[]'::jsonb" })
  certifications?: Certification[];

  @Column('text', { array: true, nullable: true, default: () => 'ARRAY[]::text[]' })
  languages?: string[];

  @Column('text', { array: true, nullable: true, default: () => 'ARRAY[]::text[]' })
  myValues?: string[];

  @Column({ type: 'varchar', nullable: true })
  portfolioUrl?: string;

  @Column({ type: 'varchar', nullable: true })
  videoIntroUrl?: string;

  @Column('jsonb', { nullable: true, default: null })
  preferences?: JobPreferences;

  @Column({ type: 'varchar', nullable: true })
  passportId?: string;

  @Column('jsonb', { nullable: true, default: null })
  achievements?: Achievement[];

  @Column('jsonb', { nullable: true, default: null })
  recommendations?: Recommendation[];

  @Column('jsonb', { nullable: true, default: null })
  workAvailability?: WorkAvailability;

  @Column('jsonb', { nullable: true, default: null })
  compensation?: Compensation;

  @Column('jsonb', { nullable: true, default: null })
  privacySettings?: PrivacySettings;

  @Column('jsonb', { nullable: true, default: null })
  socialProfiles?: SocialProfile[];

  @Column('jsonb', { nullable: true, default: null })
  metadata?: JobSeekerMetadata;

  @Column({ type: 'boolean', default: false })
  hasCompletedOnboarding!: boolean;

  @Column('jsonb', { nullable: true, default: null })
  linkedInProfile?: any;

  @Column('jsonb', {
    nullable: true,
    default: {
      basicInfo: {
        completed: false,
        completedAt: null,
        percentage: 0,
        requiredFields: ['firstName', 'lastName', 'email'],
        completedFields: [],
      },
      professionalInfo: {
        completed: false,
        completedAt: null,
        percentage: 0,
        requiredFields: ['skills'],
        completedFields: [],
      },
      preferences: {
        completed: false,
        completedAt: null,
        percentage: 0,
        requiredFields: [
          'jobTypes',
          'locations',
          'remotePreference',
          'desiredSalary.currency',
          'desiredSalary.period',
          'desiredSalary.min',
          'desiredSalary.max',
        ],
        completedFields: [],
      },
      additionalInfo: {
        completed: false,
        completedAt: null,
        percentage: 0,
        requiredFields: [],
        completedFields: [],
      },
      overall: {
        completed: false,
        completedAt: null,
        percentage: 0,
      },
      lastUpdated: null,
    },
  })
  onboardingProgress?: {
    basicInfo: {
      completed: boolean;
      completedAt: Date | null;
      percentage: number;
      requiredFields: string[];
      completedFields: string[];
    };
    professionalInfo: {
      completed: boolean;
      completedAt: Date | null;
      percentage: number;
      requiredFields: string[];
      completedFields: string[];
    };
    preferences: {
      completed: boolean;
      completedAt: Date | null;
      percentage: number;
      requiredFields: string[];
      completedFields: string[];
    };
    additionalInfo: {
      mandatoryMissing?: any;
      completed: boolean;
      completedAt: Date | null;
      percentage: number;
      requiredFields: string[];
      completedFields: string[];
    };
    overall: {
      completed: boolean;
      completedAt: Date | null;
      percentage: number;
    };
    lastUpdated: Date | null;
  };

  @Column('jsonb', {
    nullable: true,
    default: {
      email: false,
      phone: false,
      identity: {
        documentUrl: null,
        documentType: null,
        isVerified: false,
        verifiedAt: null,
        status: 'PENDING',
      },
      video: {
        videoUrl: null,
        isVerified: false,
        verifiedAt: null,
        verificationPhrase: null,
        status: 'PENDING',
      },
      education: false,
      employment: false,
      skills: false,
    },
  })
  verifications?: Verifications;

  @Column({ type: 'boolean', default: false })
  isImportedFromLinkedIn?: boolean;

  @Column({ type: 'timestamp', nullable: true })
  linkedInImportDate?: Date;

  @Column({ default: null, nullable: true })
  isApproved?: boolean;

  @Column({ nullable: true })
  declineReason?: string;

  @Column('jsonb', { nullable: true, default: [] })
  emailCorrespondence?: EmailCorrespondence[];

  @Column('jsonb', { nullable: true, default: [], select: false })
  activityHistory?: ActivityHistory[];

  @Column({ nullable: true })
  referralPartnerId?: string;

  @Column({ default: false })
  isReferralPartner?: boolean;

  @OneToOne(() => ReferralPartner, (partner) => partner.jobSeeker, { nullable: true })
  @JoinColumn({ name: 'referralPartnerId' })
  referralPartner?: ReferralPartner;

  toCandidate(jobId: string): Partial<Candidate> {
    // Handle cases where firstName or lastName might be undefined
    const firstName = this.firstName && this.firstName !== 'undefined' ? this.firstName : '';
    const lastName = this.lastName && this.lastName !== 'undefined' ? this.lastName : '';
    const fullName =
      firstName && lastName ? `${firstName} ${lastName}` : firstName || lastName || '';

    return {
      jobId,
      jobSeekerId: this.id,
      externalId: `JS_${this.id}`,
      fullName,
      firstName,
      lastName,
      jobTitle: '',
      location: this.location,
      summary: this.summary,
      skills: this.skills,
      experience: this.experience,
      linkedinUrl: this.linkedinUrl,
      githubUrl: this.githubUrl,
      profileUrl: this.resumeUrl,
      myProfileImage: this.myProfileImage,
      source: 'JOB_SEEKER',
      status: CandidateStatus.NEW,
      contacted: false,
      contactMethod: ContactMethod.EMAIL,
    };
  }
}
