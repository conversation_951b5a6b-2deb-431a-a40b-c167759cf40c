import { RateLimiter } from 'limiter';
import { OpenAI } from 'openai';

import { Candidate } from '@/modules/entities';

import { extractFileContents } from './fileExtractor';
import { OpenAIModel } from './services/openai-base.service';
import { CandidateStatus, ContactMethod } from './types';
import { safeJsonParse } from '../utils/json-cleaner.util';

const defaultModel = OpenAIModel.GPT_4O_MINI; // Using GPT-4o mini for faster resume processing

const limiter = new RateLimiter({
  tokensPerInterval: 50, // Increased from 3 to 50 requests per second
  interval: 'second',
});

function sanitizeData(data: string): string {
  // Preserve line breaks and structure for better parsing
  return data
    .replace(/\r\n/g, '\n')
    .replace(/\n{3,}/g, '\n\n')
    .trim();
}

export async function parseResumeData(
  file: {
    mimetype: string;
    buffer: Buffer;
    originalname: string;
  },
  jobId: string,
) {
  const openaiApi = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

  // Extract text content using the helper
  const { contents, duplicateFiles } = await extractFileContents(file, []);

  if (duplicateFiles.length > 0) {
    throw new Error('Duplicate resume detected.');
  }

  if (contents.length === 0) {
    throw new Error('No content extracted from the resume.');
  }

  const cleanedResume = sanitizeData(contents[0].content);
  const improvedUserPrompt = `
Given this resume content, extract the key information and return ONLY valid JSON.
DO NOT include any markdown formatting, code blocks, backticks, or explanatory text.
Your response must start with { and end with }.

Resume content: ${cleanedResume}

Analyze the entire resume carefully and extract:
1. Name from the header or contact section
2. Current job title from the most recent work experience
3. All skills mentioned throughout the resume including those implied in work experience
4. All work experiences with accurate dates and companies

Output this exact JSON structure (no text before or after):
{
  "fullName": "extract actual name",
  "jobTitle": "extract most recent job title",
  "location": "extract location if present",
  "summary": "extract or create brief summary of experience",
  "skills": ["extract all skills found"],
  "experience": [
    {
      "title": "exact job title",
      "company": "exact company name",
      "duration": "calculate total time",
      "startDate": "find start date YYYY-MM-DD",
      "endDate": "find end date YYYY-MM-DD or null if current"
    }
  ],
  "linkedinUrl": "find linkedin URL or null",
  "githubUrl": "find github URL or null"
}`;

  try {
    await limiter.removeTokens(1);

    const response = await openaiApi.chat.completions.create({
      model: defaultModel,
      messages: [
        {
          role: 'system',
          content:
            'Extract factual information from the provided resume. Do not invent or assume information not present in the text. CRITICAL: Return ONLY valid JSON without any markdown formatting, code blocks, or explanatory text. Start your response with { and end with }.',
        },
        {
          role: 'user',
          content: improvedUserPrompt,
        },
      ],
      max_tokens: 5000,
      temperature: 0,
    });

    const content = response?.choices[0]?.message?.content;

    if (!content) {
      throw new Error('Failed to extract resume information');
    }

    const parsedData = safeJsonParse(content);

    const candidate = new Candidate();
    candidate.externalId = `UPLOAD_${Date.now()}`;
    candidate.jobId = jobId;

    // Map extracted data
    if (
      !parsedData.fullName ||
      !parsedData.jobTitle ||
      !parsedData.skills ||
      !parsedData.experience
    ) {
      throw new Error('Failed to extract required information from resume');
    }

    candidate.fullName = parsedData.fullName;
    candidate.jobTitle = parsedData.jobTitle;
    candidate.location = parsedData.location || '';
    candidate.summary = parsedData.summary || '';
    candidate.skills = Array.isArray(parsedData.skills) ? parsedData.skills : [];
    candidate.experience = Array.isArray(parsedData.experience) ? parsedData.experience : [];
    candidate.linkedinUrl = parsedData.linkedinUrl || '';
    candidate.githubUrl = parsedData.githubUrl || '';
    candidate.profileUrl = '';
    candidate.source = 'RESUME_UPLOAD';
    candidate.status = CandidateStatus.NEW;
    candidate.contacted = false;
    candidate.contactMethod = ContactMethod.OTHER;
    candidate.notes = '';

    // Initialize evaluation with default values
    candidate.evaluation = {
      matchScore: 0,
      criterionMatchedOn: ['Skills Match', 'Experience Relevance', 'Location and Availability'],
      yourReasoningForScoring: 'Pending evaluation',
      hiringRecommendation: 'CONSIDER',
      interviewRecommendation: true, // Set to true for CONSIDER recommendation
      detailedScoreAnalysis: {
        areasOfStrength: [],
        detailedReasoning: {
          skillsMatch: 0,
          skillsAnalysis: {
            technicalSkillsScore: 0,
            domainSkillsScore: 0,
            toolsAndTechnologiesScore: 0,
            softSkillsScore: 0,
            matchedSkills: [],
            relatedSkills: [],
            criticalSkillsPresent: [],
            skillGaps: [],
          },
          experienceRelevance: {
            yearsOfRelevantExperience: 0,
            industryExpertise: 0,
            roleLevel: 0,
            projectComplexity: 0,
            teamSize: undefined,
            relevantRoles: [],
            relevantCompanies: [],
          },
          locationAndAvailability: 0,
          locationAnalysis: {
            exactLocationMatch: false,
            distanceFromJobLocation: 'Unknown',
            timeZoneCompatibility: 0,
            remoteWorkCompatibility: 0,
            relocationRequired: false,
            relocationFeasibility: 0,
          },
        },
        areasForImprovement: [],
        overallMatchPercentage: 0,
        specificCriteriaMatched: {
          skillsMatch: 0,
          experienceRelevance: 0,
          locationAndAvailability: 0,
          softSkillsMatch: 0,
          leadershipCapability: 0,
        },
        missingCriticalRequirements: [],
        potentialGrowthAreas: [],
        culturalFitIndicators: [],
        trainingNeeds: [],
        retentionRiskFactors: [],
        compensationConsiderations: {
          marketRateAlignment: 'Unknown',
          experienceToCompensationRatio: 'Unknown',
        },
        interviewFocusAreas: [],
        teamFitAssessment: {
          skillComplementarity: 0,
          uniquePerspectives: [],
          collaborationPotential: 0,
          diversityContribution: 'Not assessed',
          leadershipFit: 'Not assessed',
          teamDynamicsImpact: 'Not assessed',
        },
      },
      lastEvaluatedAt: new Date(),
    };

    // Split full name
    const nameParts = parsedData.fullName.split(' ');
    candidate.firstName = nameParts[0];
    candidate.lastName = nameParts.slice(1).join(' ');

    // update job status to Matched!

    return candidate;
  } catch (error) {
    console.error('Resume parsing failed:', error);
    throw new Error(
      error instanceof Error
        ? `Resume parsing failed: ${error.message}`
        : 'Resume parsing failed: Unknown error',
    );
  }
}
