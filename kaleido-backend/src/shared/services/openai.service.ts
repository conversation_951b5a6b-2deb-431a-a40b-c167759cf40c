import OpenAI from 'openai';

import { Job } from '@/modules/entities';
import { safeJsonParse } from '@/utils/json-cleaner.util';
import { Injectable } from '@nestjs/common';

import { JobTLDR } from '../types/job.types';
import { ProcessApplicationProps, ProcessedApplication } from '../types/openai.types';
import { ApplicationProcessorService } from './application-processor.service';
import { ContentGeneratorService } from './content-generator.service';
import { OpenAIService as BaseOpenAIService, OpenAIModel } from './openai-base.service';

@Injectable()
export class OpenaiService extends BaseOpenAIService {
  constructor(
    private readonly applicationProcessor: ApplicationProcessorService,
    private readonly contentGenerator: ContentGeneratorService,
  ) {
    super();
  }

  async processApplications(props: ProcessApplicationProps): Promise<ProcessedApplication[]> {
    return this.applicationProcessor.processApplications(props);
  }

  async generateSkillsAndResponsibilities(industry: string, jobType: string, experience?: string) {
    return this.contentGenerator.generateSkillsAndResponsibilities(industry, jobType, experience);
  }

  async generateSummary(data: string): Promise<string | null> {
    return this.contentGenerator.generateSummary(data);
  }

  async generateCompanySummary(data: string): Promise<string | null> {
    return this.contentGenerator.generateCompanySummary(data);
  }
  async generateVideoScript(
    job: Job,
    tone: string,
    language: string = 'en',
    scriptLength: string = 'medium',
  ): Promise<string> {
    return this.contentGenerator.generateVideoScript(job, tone, language, scriptLength);
  }

  async generateJobTLDR(job: Job): Promise<JobTLDR> {
    const summary = await this.contentGenerator.generateSummary(
      `${job.companyDescription || ''} ${job.finalDraft || ''} ${job.jobResponsibilities?.join(' ') || ''}`,
    );

    return {
      summary: summary || '',
      salary: job.salaryRange,
      location: job.location,
      experience: job.experience,
      jobType: job.jobType,
      typeOfHiring: job.typeOfHiring,
      typeOfJob: job.typeOfJob,
    };
  }

  async generateSocialMediaDescription(job: Job): Promise<string | null> {
    return this.contentGenerator.generateSocialMediaDescription(job);
  }

  /**
   * Generate career insights for job seekers
   * @param params Career insight generation parameters
   * @returns Generated career insight data
   */
  async generateCareerInsight(params: {
    type: string;
    jobSeekerProfile: any;
    targetRole?: string;
  }): Promise<any> {
    try {
      const { type, jobSeekerProfile, targetRole } = params;

      // Estimate context size
      const contextSize = this.estimateTokenCount(JSON.stringify(params));
      const selectedModel = this.selectModel(contextSize, 'quality');

      const systemPrompt = this.getCareerInsightPrompt(type);

      const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: systemPrompt,
        },
        {
          role: 'user',
          content: JSON.stringify({
            jobSeekerProfile,
            targetRole,
            requestType: type,
          }),
        },
      ];

      const response = await this.openai.chat.completions.create({
        model: selectedModel,
        messages,
        response_format: { type: 'json_object' },
        temperature: 0.3,
        max_tokens: 3000,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content returned from OpenAI');
      }

      return safeJsonParse(content);
    } catch (error) {
      console.error('Error generating career insight:', error);
      throw new Error('Failed to generate career insight');
    }
  }

  private getCareerInsightPrompt(type: string): string {
    const prompts: Record<string, string> = {
      SKILL_GAP_ANALYSIS: `You are a career development expert specializing in skill gap analysis. Analyze the job seeker's current skills against their target role and provide:

1. A comprehensive skill gap analysis including:
   - Current skills assessment
   - Required skills for target role
   - Skill gaps with priority levels (HIGH, MEDIUM, LOW)
   - Learning recommendations with resources
   - Estimated time to close each gap
   - Overall readiness score (0-100)

2. Return ONLY valid JSON (no markdown or text) with:
   - summary: Brief overview of the analysis
   - detailedAnalysis: Comprehensive narrative
   - skillGapAnalysis: Structured data matching the SkillGapAnalysis interface
   - aiInsights: Strengths, opportunities, challenges, and recommendations`,

      CAREER_PATH_RECOMMENDATION: `You are a career strategist providing personalized career path recommendations. Based on the job seeker's profile:

1. Analyze their current position and potential career trajectories
2. Recommend 2-3 viable career paths with:
   - Step-by-step progression
   - Timeline for each step
   - Required skills and qualifications
   - Salary ranges and growth potential
   - Success probability

3. Return ONLY valid JSON (no markdown or text) with:
   - summary: Brief overview
   - detailedAnalysis: Comprehensive narrative
   - careerPathRecommendation: Structured paths with steps
   - aiInsights: Key recommendations and action items`,

      MARKET_TREND_ANALYSIS: `You are a labor market analyst providing industry insights. Analyze:

1. Current market trends in the job seeker's industry/location
2. Emerging opportunities and declining areas
3. Skills in demand
4. Future outlook

5. Return ONLY valid JSON (no markdown or text) with:
   - summary: Brief market overview
   - detailedAnalysis: Comprehensive analysis
   - marketTrendAnalysis: Trends, opportunities, and insights
   - aiInsights: Actionable recommendations`,

      ROLE_TRANSITION_GUIDANCE: `You are a career transition specialist. Provide guidance for transitioning from current to target role:

1. Assess transition difficulty and requirements
2. Identify transferable skills
3. List new skills needed
4. Create a phased transition plan
5. Include success stories if relevant

6. Return ONLY valid JSON (no markdown or text) with:
   - summary: Brief transition overview
   - detailedAnalysis: Detailed guidance
   - roleTransitionGuidance: Structured transition plan
   - aiInsights: Key success factors and recommendations`,

      COMPENSATION_BENCHMARK: `You are a compensation analyst. Provide salary benchmarking:

1. Current market rates for the role/location
2. Factors affecting compensation
3. Percentile ranking
4. Negotiation strategies
5. Benefits comparison

6. Return ONLY valid JSON (no markdown or text) with:
   - summary: Brief compensation overview
   - detailedAnalysis: Detailed analysis
   - compensationBenchmark: Structured compensation data
   - aiInsights: Negotiation tips and recommendations`,
    };

    return prompts[type] || prompts.SKILL_GAP_ANALYSIS;
  }

  /**
   * Extract multiple job information in batches for better performance
   * @param jobDescriptions Array of job descriptions to process
   * @returns Array of extracted job information
   */
  async batchExtractJobInformation(
    jobDescriptions: { id: string; description: string }[],
  ): Promise<{ id: string; extractedData: any; error?: string }[]> {
    const extractFn = async (
      job: { id: string; description: string },
      model: OpenAIModel,
    ): Promise<{ id: string; extractedData: any; error?: string }> => {
      try {
        const extractedData = await this.extractJobInformation(job.description);
        return { id: job.id, extractedData };
      } catch (error: any) {
        return { id: job.id, extractedData: null, error: error.message };
      }
    };

    // Use optimized batch processing with GPT-4O-MINI
    return this.processBatch(jobDescriptions, extractFn, {
      batchSize: 100, // Increased from 50 to 100 for better throughput
      concurrentBatches: 20, // Increased from 10 to 20 for higher concurrency
      model: OpenAIModel.GPT_4O_MINI,
    });
  }

  /**
   * Extract structured job information from a raw job description text
   * @param jobDescriptionText Raw job description text
   * @returns Extracted job information matching the Job entity structure
   */
  async extractJobInformation(jobDescriptionText: string): Promise<any> {
    try {
      // Estimate context size
      const contextSize = this.estimateTokenCount(jobDescriptionText);

      // Use GPT-4O-MINI for faster job extraction (optimized for speed)
      const selectedModel = OpenAIModel.GPT_4O_MINI;

      const extractionPrompt: OpenAI.Chat.ChatCompletionMessageParam = {
        role: 'system',
        content: `You are an expert HR assistant specializing in job description analysis. Extract structured information from the provided job description text.

CRITICAL: Return ONLY valid JSON without any markdown formatting, code blocks (no json code blocks), backticks, or explanatory text.
Your response must start with { and end with }.
Return the extracted information in this exact JSON structure:

{
  "title": "Job title/position name",
  "companyName": "Company name if mentioned",
  "companyDescription": "Brief company description if provided",
  "department": "Department (must be one of: Engineering, Product, Design, Marketing, Sales, Customer Support, Finance, Human Resources, Operations, Legal, Research & Development, Information Technology, Data Science, Business Development, Administration, Quality Assurance, Project Management, Supply Chain, Public Relations, Strategy)",
  "jobType": "Job type from this list: Software Engineer, Data Scientist, Product Manager, UX Designer, Marketing Specialist, Sales Representative, Customer Support Representative, Financial Analyst, Human Resources Manager, Project Manager, Business Analyst, Graphic Designer, Content Writer, Social Media Manager, Systems Administrator, Network Engineer, Database Administrator, DevOps Engineer, Quality Assurance Tester, Front-end Developer, Back-end Developer, Full-stack Developer, Mobile App Developer, AI/Machine Learning Engineer, Cloud Architect, Cybersecurity Specialist, Data Analyst, Business Intelligence Analyst, Operations Manager, Supply Chain Manager, Accountant, Legal Counsel",
  "typeOfHiring": "EMPLOYMENT or PROJECT",
  "typeOfJob": "PERMANENT, CONTRACT, or PART_TIME",
  "experienceLevel": "graduate, junior, mid, senior, or lead",
  "experience": "Experience requirements as text",
  "skills": ["Array of technical skills mentioned"],
  "jobResponsibilities": ["Array of key responsibilities/duties"],
  "requirements": ["Array of requirements/qualifications"],
  "benefits": ["Array of benefits/perks mentioned"],
  "salaryRange": "Salary range if mentioned",
  "currency": "Currency code (AED, USD, EUR, GBP, SAR, QAR, KWD, BHD, OMR) if salary mentioned",
  "location": "Job location",
  "hiringManagerDescription": "Brief description of hiring manager or team if mentioned",
  "socialMediaDescription": "A concise 1-2 sentence description suitable for social media posting"
}

Important guidelines:
- Use null for fields that are not mentioned or cannot be determined
- For enum fields, use EXACTLY these values:
  * department: Must match one of the listed departments exactly
  * jobType: Must match one of the listed job types exactly
  * typeOfHiring: Must be exactly "EMPLOYMENT" or "PROJECT"
  * typeOfJob: Must be exactly "PERMANENT", "CONTRACT", or "PART_TIME"
  * experienceLevel: Must be exactly "graduate", "junior", "mid", "senior", or "lead"
  * currency: Must be exactly one of: AED, USD, EUR, GBP, SAR, QAR, KWD, BHD, OMR
- Extract skills as an array of individual technical skills, tools, or technologies
- Keep responsibilities and requirements as separate, clear bullet points
- Make the social media description engaging and concise (under 280 characters)
- If salary is mentioned, extract both the range and currency
- Be conservative - only extract information that is clearly stated or strongly implied
- If you cannot find an exact match for department or jobType, choose the closest match or use null`,
      };

      const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
        extractionPrompt,
        {
          role: 'user',
          content: `Please extract structured information from this job description:\n\n${jobDescriptionText}`,
        },
      ];

      const response = await this.openai.chat.completions.create({
        model: selectedModel,
        messages,
        response_format: { type: 'json_object' },
        temperature: 0.1, // Low temperature for consistent extraction
        max_tokens: 2000,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content returned from OpenAI');
      }

      try {
        const extractedData = safeJsonParse(content);
        return extractedData;
      } catch (parseError) {
        console.error('Error parsing OpenAI response:', parseError);
        throw new Error('Failed to parse extracted job information');
      }
    } catch (error) {
      console.error('Error extracting job information:', error);
      throw new Error('Failed to extract job information from description');
    }
  }
}
