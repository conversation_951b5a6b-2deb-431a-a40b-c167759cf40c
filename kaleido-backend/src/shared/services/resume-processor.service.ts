import OpenAI from 'openai';
import * as os from 'os';
import { Observable, Subject } from 'rxjs';

import { Graduate } from '@/modules/graduate/entities/graduate.entity';
import { JobSeeker } from '@/modules/job-seeker/entities/job-seeker.entity';
import { CandidateExperience } from '@/shared/types';
import { safeJsonParse } from '@/utils/json-cleaner.util';
import { Injectable, Logger } from '@nestjs/common';

import { OpenAIModel } from './openai-base.service';
import { OpenaiService } from './openai.service';
import { SLMService } from './slm.service';

interface ResumeExperience {
  title: string;
  company: string;
  location?: string;
  startDate: string;
  endDate?: string;
  description: string;
  achievements?: string[];
}

interface ResumeEducation {
  institution: string;
  degree: string;
  field: string;
  startDate: string;
  endDate?: string;
  description?: string;
}

interface ResumeCertification {
  name: string;
  issuer: string;
  issueDate: string;
  expiryDate?: string;
  credentialId?: string;
  credentialUrl?: string;
}

interface ResumeProject {
  name: string;
  description: string;
  startDate: string;
  endDate?: string;
  url?: string;
  technologies: string[];
}

interface ParsedResumeData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  location?: string;
  summary?: string;
  skills: string[];
  experience: ResumeExperience[];
  education: ResumeEducation[];
  certifications?: ResumeCertification[];
  projects?: ResumeProject[];
  languages: string[];
  linkedinUrl?: string;
  githubUrl?: string;
  portfolioUrl?: string;
}

interface GraduateResumeData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  location?: string;
  summary?: string;
  skills: string[];
  university?: string;
  degreeProgram?: string;
  major?: string;
  graduationYear?: string;
  gpa?: number;
  academicAchievements?: string[];
  academicProjects?: Array<{
    name: string;
    description: string;
    technologies: string[];
    link?: string;
    date?: string;
  }>;
  internships?: Array<{
    company: string;
    role: string;
    startDate?: string;
    endDate?: string;
    description?: string;
    skills?: string[];
    achievements?: string[];
  }>;
  languages?: string[];
  linkedinUrl?: string;
  githubUrl?: string;
  portfolioUrl?: string;
}

@Injectable()
export class ResumeProcessorService {
  private readonly logger = new Logger(ResumeProcessorService.name);
  private useSlm = false; // DISABLED: Using optimized multi-AI load balancing instead

  private readonly COMPREHENSIVE_SYSTEM_PROMPT = `You are a resume parser. Your task is to carefully extract structured information from resumes. Pay special attention to education details as they are critical.

CRITICAL INSTRUCTION: Return ONLY valid JSON. Do not include any markdown formatting, code blocks (no json), backticks, or explanatory text. Your response must start with { and end with }.

REQUIRED FIELDS (must be present in response):
- First name and last name (split appropriately)
- Email address (must be extracted correctly from the resume)
- Skills list (all technical and soft skills mentioned)
- Education history (all degrees and certifications)
- Experience history (all work experience)
- Languages (if not explicitly mentioned, use ["English"])

OPTIONAL FIELDS (include if present):
- Phone number
- Location
- Summary/Profile
- Certifications
- Projects
- Social links (LinkedIn, GitHub, Portfolio)

EDUCATION EXTRACTION RULES:
1. Always include education section, even if only one entry
2. Look for keywords: "Bachelor", "Master", "PhD", "Diploma", "Certificate", "University", "College", "School"
3. Extract full institution names
4. Specify degree type and field clearly
5. Include dates in YYYY-MM-DD format (use YYYY if only year available)
6. If education information is minimal, still create a valid entry with available data
7. NEVER return null for education - use an array with at least one entry

EXPERIENCE EXTRACTION RULES:
1. Always include proper startDate and endDate fields (not a combined 'dates' field)
2. Always include title, company, and description (use company description if individual role description is unavailable)
3. Format all dates consistently
4. NEVER return null for description fields - use empty string if no description available
5. Extract achievements when possible
6. NEVER return null for experience array - use empty array if no experience found
7. Always use the 'title' field for job titles, not 'jobTitle'

SKILLS EXTRACTION RULES:
1. Return skills as an array of strings, not objects
2. Example: skills: ["JavaScript", "React", "Project Management"] NOT skills: [{"skill": "JavaScript"}, {"skill": "React"}]
3. Be comprehensive in extracting all skills mentioned
4. Include both technical and soft skills

Example Education Entry:
{
  "institution": "University of California, Berkeley",
  "degree": "Bachelor of Science",
  "field": "Computer Science",
  "startDate": "2018-09-01",
  "endDate": "2022-05-15",
  "description": "Major in Software Engineering, Minor in Business Administration"
}

Example Experience Entry:
{
  "title": "Software Engineer",
  "company": "Google",
  "location": "Mountain View, CA",
  "startDate": "2022-06-01",
  "endDate": "2023-12-31",
  "description": "Developed and maintained cloud infrastructure",
  "achievements": ["Reduced system latency by 30%", "Implemented CI/CD pipeline"]
}

Output ONLY valid JSON (no text before or after, no markdown, no backticks). Required structure:
{
  "firstName": string,
  "lastName": string,
  "email": string,
  "phone": string (optional),
  "location": string (optional),
  "summary": string (optional),
  "skills": string[] (required, never empty),
  "experience": [{
    "title": string,
    "company": string,
    "location": string (optional),
    "startDate": string (YYYY-MM-DD format),
    "endDate": string (YYYY-MM-DD format or null for current positions),
    "description": string,
    "achievements": string[] (optional)
  }] (required, never empty),
  "education": [{
    "institution": string,
    "degree": string,
    "field": string,
    "startDate": string (YYYY-MM-DD format),
    "endDate": string (YYYY-MM-DD format or null for current education),
    "description": string (optional)
  }] (required, never empty),
  "certifications": [{
    "name": string,
    "issuer": string,
    "issueDate": string,
    "expiryDate": string (optional),
    "credentialId": string (optional),
    "credentialUrl": string (optional)
  }] (optional),
  "projects": [{
    "name": string,
    "description": string,
    "startDate": string,
    "endDate": string (optional),
    "url": string (optional),
    "technologies": string[]
  }] (optional),
  "languages": string[] (required, default to ["English"]),
  "linkedinUrl": string (optional),
  "githubUrl": string (optional),
  "portfolioUrl": string (optional)
}

IMPORTANT RULES:
1. NEVER return null for required arrays (skills, experience, education, languages)
2. Use empty arrays [] if no items found
3. Be thorough in extracting education - it's a critical field
4. Format dates consistently
5. Validate email format
6. Split full names correctly into first and last name
7. Include all skills mentioned, both technical and soft skills`;

  private readonly COMPREHENSIVE_GRADUATE_PROMPT = `You are a resume parser specializing in graduate profiles. Your primary focus is academic achievements and education details.

CRITICAL INSTRUCTION: Return ONLY valid JSON. Do not include any markdown formatting, code blocks (no \`\`\`json), backticks, or explanatory text. Your response must start with { and end with }.

REQUIRED FIELDS (must be present in response):
- First name and last name (split appropriately)
- Email address
- Skills list (all technical and soft skills)
- University name
- Degree program
- Major/Field of study

EDUCATION EXTRACTION RULES:
1. Extract complete university/institution name
2. Specify exact degree type (e.g., "Bachelor of Science" not just "Bachelor")
3. Include specific major/concentration
4. Note any honors or distinctions
5. Include GPA if mentioned
6. Look for relevant coursework
7. Note any thesis or research projects

Example Graduate Profile:
{
  "firstName": "Jane",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "university": "Massachusetts Institute of Technology",
  "degreeProgram": "Master of Science",
  "major": "Artificial Intelligence",
  "graduationYear": "2024",
  "gpa": 3.85,
  "academicAchievements": [
    "Published research paper on ML optimization",
    "Dean's List 2022-2023"
  ]
}

Output ONLY valid JSON (no text before or after, no markdown, no backticks). Required structure:
{
  "firstName": string,
  "lastName": string,
  "email": string,
  "phone": string (optional),
  "location": string (optional),
  "summary": string (optional),
  "skills": string[] (required, never empty),
  "university": string (required),
  "degreeProgram": string (required),
  "major": string (required),
  "graduationYear": string (required),
  "gpa": number (optional),
  "academicAchievements": string[] (optional),
  "academicProjects": [{
    "name": string,
    "description": string,
    "technologies": string[],
    "link": string (optional),
    "date": string (optional)
  }] (optional),
  "internships": [{
    "company": string,
    "role": string,
    "startDate": string (YYYY-MM-DD format),
    "endDate": string (YYYY-MM-DD format or null),
    "description": string (optional),
    "skills": string[] (optional),
    "achievements": string[] (optional)
  }] (optional),
  "languages": string[] (required, default to ["English"]),
  "linkedinUrl": string (optional),
  "githubUrl": string (optional),
  "portfolioUrl": string (optional)
}

IMPORTANT RULES:
1. NEVER return null for required fields
2. Education details are CRITICAL - be thorough
3. Extract ALL academic achievements
4. Include relevant coursework in skills
5. Format dates consistently
6. Validate email format
7. Include both technical and soft skills
8. Default to empty arrays [] if no items found for array fields`;

  private processingQueue: Array<{
    resumeText: string;
    isGraduate: boolean;
    resolve: (result: any) => void;
    reject: (error: any) => void;
  }> = [];
  private processingInProgress = false;
  private maxConcurrent: number;
  private resultSubject = new Subject<{ id: string; result: any; error?: any }>();

  constructor(
    private readonly slmService: SLMService,
    private readonly openaiService: OpenaiService,
  ) {
    // Use 75% of available CPU cores for parallel processing (minimum 2, maximum 8)
    const availableCores = os.cpus().length;
    this.maxConcurrent = Math.min(Math.max(Math.floor(availableCores * 0.75), 2), 8);
    this.logger.log(
      `📝 Resume processor using ${this.useSlm ? 'SLM' : 'OpenAI'} implementation by default`,
    );
  }

  // Add method to toggle between implementations
  public setUseSLM(value: boolean): void {
    this.useSlm = value;
    this.logger.log(
      `🔄 Resume processor now using ${this.useSlm ? 'SLM' : 'OpenAI'} for processing`,
    );
  }

  public getUseSLM(): boolean {
    return this.useSlm;
  }

  async processResume(resumeText: string, useSlm: boolean = false): Promise<any> {
    // Call correct implementation based on the flag
    const result = useSlm
      ? await this.processSLMResume(resumeText)
      : await this.processOpenAIResume(resumeText);

    // Apply extra sanitization to education data
    if (result && result.education) {
      result.education = this.sanitizeEducation(result.education);
    }

    return this.sanitizeResponse(result);
  }

  async processGraduateResume(resumeText: string): Promise<Partial<Graduate>> {
    if (this.useSlm) {
      return this.processSLMGraduateResume(resumeText);
    } else {
      return this.processOpenAIGraduateResume(resumeText);
    }
  }

  // OpenAI methods for processing resumes
  private async processOpenAIResume(resumeText: string): Promise<Partial<JobSeeker>> {
    try {
      const startTime = Date.now();

      // Process different sections of the resume concurrently
      const [basicInfo, experienceEducation, projectsCertifications] = await Promise.all([
        this.processBasicInfo(resumeText),
        this.processExperienceEducation(resumeText),
        this.processProjectsCertifications(resumeText),
      ]);

      // Merge the results
      const parsedData: ParsedResumeData = {
        firstName: basicInfo.firstName || '',
        lastName: basicInfo.lastName || '',
        email: basicInfo.email || '',
        phone: basicInfo.phone,
        location: basicInfo.location,
        summary: basicInfo.summary,
        skills: basicInfo.skills || [],
        experience: experienceEducation.experience || [],
        education: experienceEducation.education || [],
        certifications: projectsCertifications.certifications,
        projects: projectsCertifications.projects,
        languages: basicInfo.languages || [],
        linkedinUrl: basicInfo.linkedinUrl,
        githubUrl: basicInfo.githubUrl,
        portfolioUrl: basicInfo.portfolioUrl,
      };

      // Log extracted data summary

      const processingTime = (Date.now() - startTime) / 1000;

      return this.mapToJobSeeker(parsedData);
    } catch (error) {
      console.error('❌ Error processing resume with OpenAI:', error);
      throw new Error('Failed to process resume with OpenAI');
    }
  }

  // Helper methods for concurrent processing of job seeker resumes
  private async processBasicInfo(resumeText: string): Promise<Partial<ParsedResumeData>> {
    const prompt = `Extract only the following basic information from the resume:
    - First name and last name
    - Email address
    - Phone number
    - Location
    - Professional summary
    - Skills and technologies
    - Languages
    - LinkedIn, GitHub, and portfolio URLs

    Return the data in this JSON format:
    {
      "firstName": string,
      "lastName": string,
      "email": string,
      "phone": string (optional),
      "location": string (optional),
      "summary": string (optional),
      "skills": string[],
      "languages": string[],
      "linkedinUrl": string (optional),
      "githubUrl": string (optional),
      "portfolioUrl": string (optional)
    }`;

    return this.makeOpenAIRequest(prompt, resumeText);
  }

  private async processExperienceEducation(resumeText: string): Promise<Partial<ParsedResumeData>> {
    const prompt = `Extract only the work experience and education details from the resume.

    Return the data in this JSON format:
    {
      "experience": [{
        "title": string,
        "company": string,
        "location": string (optional),
        "startDate": string (YYYY-MM format),
        "endDate": string (YYYY-MM format, optional),
        "description": string,
        "achievements": string[] (optional)
      }],
      "education": [{
        "institution": string,
        "degree": string,
        "field": string,
        "startDate": string (YYYY-MM format),
        "endDate": string (YYYY-MM format, optional),
        "description": string (optional)
      }]
    }`;

    return this.makeOpenAIRequest(prompt, resumeText);
  }

  private async processProjectsCertifications(
    resumeText: string,
  ): Promise<Partial<ParsedResumeData>> {
    const prompt = `Extract only the projects and certifications from the resume.

    Return the data in this JSON format:
    {
      "certifications": [{
        "name": string,
        "issuer": string,
        "issueDate": string (YYYY-MM format),
        "expiryDate": string (YYYY-MM format, optional),
        "credentialId": string (optional),
        "credentialUrl": string (optional)
      }],
      "projects": [{
        "name": string,
        "description": string,
        "startDate": string (YYYY-MM format),
        "endDate": string (YYYY-MM format, optional),
        "url": string (optional),
        "technologies": string[]
      }]
    }`;

    return this.makeOpenAIRequest(prompt, resumeText);
  }

  // OpenAI implementation for graduate resumes
  private async processOpenAIGraduateResume(resumeText: string): Promise<Partial<Graduate>> {
    try {
      const startTime = Date.now();

      // Process different sections of the graduate resume concurrently
      const [basicInfo, educationInfo, experienceProjects] = await Promise.all([
        this.processGraduateBasicInfo(resumeText),
        this.processGraduateEducation(resumeText),
        this.processGraduateExperienceProjects(resumeText),
      ]);

      // Merge the results with proper type handling
      const parsedData: GraduateResumeData = {
        firstName: basicInfo.firstName || '',
        lastName: basicInfo.lastName || '',
        email: basicInfo.email || '',
        phone: basicInfo.phone,
        location: basicInfo.location,
        summary: basicInfo.summary,
        skills: basicInfo.skills || [],
        university: educationInfo.university,
        degreeProgram: educationInfo.degreeProgram,
        major: educationInfo.major,
        graduationYear: educationInfo.graduationYear,
        gpa: educationInfo.gpa,
        academicAchievements: educationInfo.academicAchievements || [],
        academicProjects: experienceProjects.academicProjects || [],
        internships: experienceProjects.internships || [],
        languages: basicInfo.languages || [],
        linkedinUrl: basicInfo.linkedinUrl,
        githubUrl: basicInfo.githubUrl,
        portfolioUrl: basicInfo.portfolioUrl,
      };

      // Log extracted data summary

      const processingTime = (Date.now() - startTime) / 1000;

      return this.mapToGraduate(parsedData);
    } catch (error) {
      console.error('❌ Error processing graduate resume with OpenAI:', error);
      if (error instanceof Error) {
        throw new Error(`Failed to process graduate resume with OpenAI: ${error.message}`);
      }
      throw new Error('Failed to process graduate resume with OpenAI');
    }
  }

  // Helper methods for concurrent processing of graduate resumes
  private async processGraduateBasicInfo(resumeText: string): Promise<Partial<GraduateResumeData>> {
    const prompt = `Extract only the following basic information from the graduate resume:
    - First name and last name
    - Email address
    - Phone number
    - Location
    - Professional summary
    - Skills and technologies
    - Languages
    - LinkedIn, GitHub, and portfolio URLs

    Return the data in this JSON format:
    {
      "firstName": string,
      "lastName": string,
      "email": string,
      "phone": string (optional),
      "location": string (optional),
      "summary": string (optional),
      "skills": string[],
      "languages": string[],
      "linkedinUrl": string (optional),
      "githubUrl": string (optional),
      "portfolioUrl": string (optional)
    }`;

    return this.makeOpenAIRequest(prompt, resumeText);
  }

  private async processGraduateEducation(resumeText: string): Promise<Partial<GraduateResumeData>> {
    const prompt = `Extract only the education details from the graduate resume:

    Return the data in this JSON format:
    {
      "university": string,
      "degreeProgram": string,
      "major": string,
      "graduationYear": string,
      "gpa": number,
      "academicAchievements": string[]
    }`;

    return this.makeOpenAIRequest(prompt, resumeText);
  }

  private async processGraduateExperienceProjects(
    resumeText: string,
  ): Promise<Partial<GraduateResumeData>> {
    const prompt = `Extract only the internships and academic projects from the graduate resume:

    Return the data in this JSON format:
    {
      "academicProjects": [{
        "name": string,
        "description": string,
        "technologies": string[],
        "link": string (optional),
        "date": string (YYYY-MM format)
      }],
      "internships": [{
        "company": string,
        "role": string,
        "startDate": string (YYYY-MM format),
        "endDate": string (YYYY-MM format, optional),
        "description": string (optional),
        "skills": string[],
        "achievements": string[]
      }]
    }`;

    return this.makeOpenAIRequest(prompt, resumeText);
  }

  // Generic method to make OpenAI API requests
  private async makeOpenAIRequest<T>(prompt: string, resumeText: string): Promise<T> {
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
      {
        role: 'system',
        content: prompt,
      },
      {
        role: 'user',
        content: resumeText,
      },
    ];

    const response = await openai.chat.completions.create({
      model: OpenAIModel.GPT_4O_MINI, // Using GPT-4o mini for faster resume processing with good accuracy
      messages,
      response_format: { type: 'json_object' },
      temperature: 0.3,
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No content returned from OpenAI');
    }

    // Clean the content string to remove any markdown formatting
    const cleanContent = content.replace(/```json\n|\n```/g, '').trim();

    try {
      return JSON.parse(cleanContent) as T;
    } catch (parseError) {
      console.error('Error parsing OpenAI response:', parseError);
      console.error('Raw content:', content);
      throw new Error('Failed to parse resume data');
    }
  }

  // ============ SLM Implementation (New) ============

  private async processSLMResume(resumeText: string): Promise<Partial<JobSeeker>> {
    const startTime = Date.now();
    const resumeTimerId = `resumeProcessing_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
    console.time(resumeTimerId);
    try {
      // Check if it's a simple resume we can process with minimal LLM usage
      const hasBasicInfo = this.hasRequiredResumeInfo(resumeText);

      // Process with SLM but use the optimized path
      const parsedData = await this.makeSLMRequest<ParsedResumeData>(
        this.COMPREHENSIVE_SYSTEM_PROMPT,
        resumeText,
      );

      const mappedData = this.mapToJobSeeker(parsedData);

      // Log extracted data summary for debugging

      const processingTime = (Date.now() - startTime) / 1000;
      console.timeEnd(resumeTimerId);

      const minutes = Math.floor(processingTime / 60);
      const seconds = (processingTime % 60).toFixed(2);
      const itemsProcessed =
        (parsedData.skills?.length || 0) +
        (parsedData.experience?.length || 0) +
        (parsedData.education?.length || 0) +
        (parsedData.projects?.length || 0) +
        (parsedData.certifications?.length || 0);

      return mappedData;
    } catch (error) {
      const processingTime = (Date.now() - startTime) / 1000;
      console.timeEnd(resumeTimerId);
      console.error('❌ Error processing resume:', error);

      const minutes = Math.floor(processingTime / 60);
      const seconds = (processingTime % 60).toFixed(2);

      throw new Error('Failed to process resume');
    }
  }

  private async processSLMGraduateResume(resumeText: string): Promise<Partial<Graduate>> {
    const startTime = Date.now();
    const graduateTimerId = `graduateResumeProcessing_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
    console.time(graduateTimerId);
    try {
      const parsedData = await this.makeSLMRequest<GraduateResumeData>(
        this.COMPREHENSIVE_GRADUATE_PROMPT,
        resumeText,
      );

      const processingTime = (Date.now() - startTime) / 1000;
      console.timeEnd(graduateTimerId);

      // Log detailed timing stats

      const minutes = Math.floor(processingTime / 60);
      const seconds = (processingTime % 60).toFixed(2);
      const itemsProcessed =
        (parsedData.skills?.length || 0) +
        (parsedData.academicProjects?.length || 0) +
        (parsedData.internships?.length || 0) +
        (parsedData.languages?.length || 0) +
        (parsedData.academicAchievements?.length || 0) +
        (parsedData.university ? 1 : 0);

      return this.mapToGraduate(parsedData);
    } catch (error) {
      const processingTime = (Date.now() - startTime) / 1000;
      console.timeEnd(graduateTimerId);
      console.error('❌ Error processing graduate resume:', error);

      const minutes = Math.floor(processingTime / 60);
      const seconds = (processingTime % 60).toFixed(2);
      throw new Error('Failed to process graduate resume');
    }
  }

  // Keep the original SLM request method
  private async makeSLMRequest<T>(prompt: string, resumeText: string): Promise<T> {
    const requestStartTime = Date.now();
    const totalSLMTimerId = `totalSLMRequest_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
    console.time(totalSLMTimerId);

    try {
      const directExtractionTimerId = `directExtraction_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
      console.time(directExtractionTimerId);

      // Extract critical data directly using regex for faster processing
      const backupData: Record<string, any> = {};

      // Extract basic info using regex (much faster than LLM for these fields)
      const nameMatch = resumeText.match(/Name:\s*([^\n]+)/);
      const emailMatch = resumeText.match(/Email:\s*([^\n]+)/);
      const phoneMatch = resumeText.match(/Phone:\s*([^\n]+)/);
      const linkedinMatch = resumeText.match(/LinkedIn:\s*([^\n]+)/);
      const educationMatch = resumeText.match(/Education:\s*([^\n]+)/);

      if (nameMatch) {
        const nameParts = nameMatch[1].trim().split(' ');
        backupData.firstName = nameParts[0];
        backupData.lastName = nameParts.slice(1).join(' ');
      }

      if (emailMatch) backupData.email = emailMatch[1].trim();
      if (phoneMatch) backupData.phone = phoneMatch[1].trim();
      if (linkedinMatch) backupData.linkedinUrl = linkedinMatch[1].trim();

      // Process education info directly
      if (educationMatch) {
        const eduText = educationMatch[1].trim();
        const degreeMatch = eduText.match(/^([^,]+?)(?:\s+from\s+|,\s*)/i);
        const institutionMatch = eduText.match(/from\s+([^,]+)/i);
        const yearMatch = eduText.match(/(\d{4})/);

        if (degreeMatch && institutionMatch) {
          backupData.education = [
            {
              institution: institutionMatch[1].trim(),
              degree: degreeMatch[1].trim(),
              field: degreeMatch[1].includes(' in ')
                ? degreeMatch[1].split(' in ')[1].trim()
                : degreeMatch[1].includes(' of ')
                  ? degreeMatch[1].split(' of ')[1].trim()
                  : 'Not specified',
              startDate: yearMatch ? yearMatch[1] : '0000',
              endDate: yearMatch ? yearMatch[1] : '0000',
            },
          ];
        }
      }

      // Extract skills from the resume directly (if they're in a clearly labeled section)
      const skillsMatch = resumeText.match(/Skills:([^\n]+)(?:\n|$)/);
      if (skillsMatch) {
        backupData.skills = skillsMatch[1]
          .split(/[,;]/)
          .map((s: string) => s.trim())
          .filter(Boolean);
      }

      console.timeEnd(directExtractionTimerId);
      const directExtractionTime = (Date.now() - requestStartTime) / 1000;

      // If we have all critical fields, try to extract experience with minimal LLM usage
      const isCriticalInfoComplete =
        backupData.firstName &&
        backupData.email &&
        (backupData.education || []).length > 0 &&
        (backupData.skills || []).length > 0;

      let promptLength = prompt.length;
      let instructionsAddition = 0;

      if (isCriticalInfoComplete) {
        // Use even smaller token limits and simple instructions for faster processing
        promptLength = promptLength * 0.5; // Cut prompt length
        instructionsAddition = -2; // Remove some instructions
      }

      const resumeOptimizationTimerId = `resumeOptimization_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
      console.time(resumeOptimizationTimerId);

      // Truncate and optimize the resume text to focus on most important parts
      // This significantly reduces tokens for the LLM
      const truncatedResume = this.optimizeResumeForProcessing(resumeText);

      console.timeEnd(resumeOptimizationTimerId);
      const optimizationTime =
        (Date.now() - (requestStartTime + directExtractionTime * 1000)) / 1000;

      // Create a more concise prompt for faster processing
      const fullPrompt = `${prompt}\n\nRESUME TEXT:\n${truncatedResume}\n\nINSTRUCTIONS:
1. Extract only the information not already provided in the pre-extracted data
2. Focus on experience and additional skills
3. Format dates consistently as YYYY-MM-DD or YYYY-MM or YYYY
4. Return valid JSON with no explanations
5. Return skills as an array of strings, NOT objects
6. Keep the response minimal and focused on missing information`;

      const slmTimerId = `slmRequestDuration_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
      console.time(slmTimerId);
      const llmStartTime = Date.now();

      // Try SLM first, with fallback to OpenAI
      try {
        // Calculate a reasonable timeout based on resume complexity
        const resumeComplexity = Math.min(1, truncatedResume.length / 5000); // 0-1 scale
        const dynamicTimeout = 60000 + Math.round(resumeComplexity * 120000); // 1-3 minutes based on complexity

        // Use a lower max_tokens setting and set priority for better concurrency
        const result = await this.slmService.generateStructuredOutput<T>(fullPrompt, 0.1, {
          maxTokens: isCriticalInfoComplete ? 1024 : 1536, // Lower token limit for faster responses
          formatInstructions: true,
          priority: 'normal', // Can be adjusted based on job priority
        });

        console.timeEnd(slmTimerId);
        const llmTime = (Date.now() - llmStartTime) / 1000;

        // Process the result as normal
        const postProcessingTimerId = `postProcessing_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
        console.time(postProcessingTimerId);

        // Merge pre-extracted data with LLM results
        const resultWithBackup = {
          ...result,
          ...backupData,
          // Keep backup data from result if not in pre-extracted
          firstName: backupData.firstName || (result as any).firstName,
          lastName: backupData.lastName || (result as any).lastName,
          email: backupData.email || (result as any).email,
          phone: backupData.phone || (result as any).phone,
          linkedinUrl: backupData.linkedinUrl || (result as any).linkedinUrl,
        };

        // Sanitize response to ensure no null values for required fields
        const sanitizedResult = this.sanitizeResponse(resultWithBackup);

        console.timeEnd(postProcessingTimerId);
        const postProcessingTime = (Date.now() - (llmStartTime + llmTime * 1000)) / 1000;

        console.timeEnd(totalSLMTimerId);
        const totalRequestTime = (Date.now() - requestStartTime) / 1000;

        return sanitizedResult;
      } catch (error) {
        console.timeEnd(slmTimerId);
        this.logger.warn(
          'SLM service failed, falling back to OpenAI: ' +
            (error instanceof Error ? error.message : 'Unknown error'),
        );

        // FALLBACK TO OPENAI
        this.logger.log('Attempting to process resume with OpenAI instead...');
        const openaiStartTime = Date.now();

        try {
          // Create a direct OpenAI client (similar to ResumeParserService)
          const openaiApi = new OpenAI({
            apiKey: process.env.OPENAI_API_KEY,
          });

          if (!openaiApi) {
            throw new Error('Failed to initialize OpenAI client');
          }

          // Use a similar prompt structure to what's used in ResumeParserService
          const improvedUserPrompt = `
Given this resume content, extract the key information and return ONLY a JSON object that matches the structure required below.
Resume content: ${truncatedResume}

Analyze the entire resume carefully and extract:
1. Personal information (name, email, phone, location)
2. Summary/profile description
3. All skills mentioned throughout the resume
4. All work experiences with accurate dates
5. Education details
6. Languages and additional information

Return as valid JSON with this structure:
{
  "firstName": "first name",
  "lastName": "last name",
  "email": "email",
  "phone": "phone if present",
  "location": "location if present",
  "summary": "professional summary or objective",
  "skills": ["skill1", "skill2", ...],
  "experience": [
    {
      "title": "job title",
      "company": "company name",
      "location": "job location if available",
      "startDate": "YYYY-MM-DD",
      "endDate": "YYYY-MM-DD or null if current",
      "description": "job description"
    }
  ],
  "education": [
    {
      "institution": "university/school name",
      "degree": "degree type",
      "field": "field of study",
      "startDate": "YYYY-MM-DD",
      "endDate": "YYYY-MM-DD or null if current"
    }
  ],
  "languages": ["language1", "language2", ...],
  "linkedinUrl": "LinkedIn URL if found",
  "githubUrl": "GitHub URL if found"
};

Example Education Entry:
{
  "institution": "University of California, Berkeley",
  "degree": "Bachelor of Science",
  "field": "Computer Science",
  "startDate": "2018-09-01",
  "endDate": "2022-05-15",
  "description": "Major in Software Engineering, Minor in Business Administration"
}

Example Experience Entry:
{
  "title": "Software Engineer",
  "company": "Google",
  "location": "Mountain View, CA",
  "startDate": "2022-06-01",
  "endDate": "2023-12-31",
  "description": "Developed and maintained cloud infrastructure",
  "achievements": ["Reduced system latency by 30%", "Implemented CI/CD pipeline"]
}

Output ONLY valid JSON (no text before or after, no markdown, no backticks). Required structure:
{
  "firstName": string,
  "lastName": string,
  "email": string,
  "phone": string (optional),
  "location": string (optional),
  "summary": string (optional),
  "skills": string[] (required, never empty),
  "experience": [{
    "title": string,
    "company": string,
    "location": string (optional),
    "startDate": string (YYYY-MM-DD format),
    "endDate": string (YYYY-MM-DD format or null for current positions),
    "description": string,
    "achievements": string[] (optional)
  }] (required, never empty),
  "education": [{
    "institution": string,
    "degree": string,
    "field": string,
    "startDate": string (YYYY-MM-DD format),
    "endDate": string (YYYY-MM-DD format or null for current education),
    "description": string (optional)
  }] (required, never empty),
  "certifications": [{
    "name": string,
    "issuer": string,
    "issueDate": string,
    "expiryDate": string (optional),
    "credentialId": string (optional),
    "credentialUrl": string (optional)
  }] (optional),
  "projects": [{
    "name": string,
    "description": string,
    "startDate": string,
    "endDate": string (optional),
    "url": string (optional),
    "technologies": string[]
  }] (optional),
  "languages": string[] (required, default to ["English"]),
  "linkedinUrl": string (optional),
  "githubUrl": string (optional),
  "portfolioUrl": string (optional)
}

IMPORTANT RULES:
1. NEVER return null for required arrays (skills, experience, education, languages)
2. Use empty arrays [] if no items found
3. Be thorough in extracting education - it's a critical field
4. Format dates consistently
5. Validate email format
6. Split full names correctly into first and last name
7. Include all skills mentioned, both technical and soft skills`;

          // Call OpenAI API
          const response = await openaiApi.chat.completions.create({
            model: OpenAIModel.GPT_4O_MINI, // Using GPT-4o mini for faster resume processing with good accuracy
            messages: [
              {
                role: 'system',
                content:
                  'You are a resume parser. Extract structured information accurately from the resume text.',
              },
              {
                role: 'user',
                content: improvedUserPrompt,
              },
            ],
            max_tokens: 4000,
            temperature: 0,
          });

          const content = response.choices[0]?.message?.content;

          if (!content) {
            throw new Error('Empty response from OpenAI');
          }

          // Parse the JSON response
          let openAiResult;
          try {
            openAiResult = safeJsonParse(content);
          } catch (parseError) {
            this.logger.error('Failed to parse OpenAI response', parseError);
            throw new Error('Invalid JSON response from OpenAI');
          }

          // Log successful OpenAI processing
          const openaiTime = (Date.now() - openaiStartTime) / 1000;
          this.logger.log(`OpenAI fallback successful in ${openaiTime.toFixed(2)} seconds`);

          // Merge pre-extracted data with OpenAI results
          const resultWithBackup = {
            ...openAiResult,
            ...backupData,
            // Keep backup data from result if not in pre-extracted
            firstName: backupData.firstName || openAiResult.firstName,
            lastName: backupData.lastName || openAiResult.lastName,
            email: backupData.email || openAiResult.email,
            phone: backupData.phone || openAiResult.phone,
            linkedinUrl: backupData.linkedinUrl || openAiResult.linkedinUrl,
          };

          // Sanitize response to ensure no null values for required fields
          const sanitizedResult = this.sanitizeResponse(resultWithBackup as unknown as T);

          console.timeEnd(totalSLMTimerId);
          const totalRequestTime = (Date.now() - requestStartTime) / 1000;
          this.logger.log(
            `TOTAL REQUEST TIME (with OpenAI fallback): ${totalRequestTime.toFixed(2)} seconds`,
          );

          return sanitizedResult;
        } catch (openaiError) {
          this.logger.error(
            'OpenAI fallback also failed:',
            openaiError instanceof Error ? openaiError.message : 'Unknown error',
          );

          // If both SLM and OpenAI fail but we have critical fields, return what we have
          if (backupData.firstName && backupData.email) {
            this.logger.warn('Using basic extracted data due to both SLM and OpenAI failures');
            // Add missing required fields
            if (!backupData.skills) backupData.skills = [];
            if (!backupData.experience) backupData.experience = [];
            if (!backupData.education) backupData.education = [];
            if (!backupData.languages) backupData.languages = ['English'];

            console.timeEnd(totalSLMTimerId);
            const totalRequestTime = (Date.now() - requestStartTime) / 1000;

            return this.sanitizeResponse(backupData as unknown as T);
          }
          throw openaiError;
        }
      }
    } catch (error) {
      console.timeEnd(totalSLMTimerId);
      const totalRequestTime = (Date.now() - requestStartTime) / 1000;

      console.error(
        '❌ Error processing resume:',
        error instanceof Error ? error.message : 'Unknown error',
      );
      throw error;
    }
  }

  /**
   * Optimizes resume text to focus on most important parts
   * Reduces token count significantly
   */
  private optimizeResumeForProcessing(resumeText: string): string {
    // Already extracted name, email, phone, education via regex
    // Now focus on professional experience
    const experienceMatch = resumeText.match(/Professional Experience:([\s\S]+?)(Skills:|$)/i);
    const skillsMatch = resumeText.match(/Skills:([^\n]+)(?:\n|$)/);

    let experienceText = '';
    if (experienceMatch && experienceMatch[1]) {
      experienceText = experienceMatch[1].trim();
    }

    let skillsText = '';
    if (skillsMatch && skillsMatch[1]) {
      skillsText = 'Skills: ' + skillsMatch[1].trim();
    }

    // Include header info (always useful) and most important parts
    const headerMatch = resumeText.match(/^([\s\S]*?Professional Experience:)/i);
    const header = headerMatch ? headerMatch[1] : '';

    return `${header}\n${experienceText}\n${skillsText}`.trim();
  }

  private calculateDuration(startDate: Date, endDate?: Date): number {
    const end = endDate || new Date();
    const diffInMonths =
      (end.getFullYear() - startDate.getFullYear()) * 12 + (end.getMonth() - startDate.getMonth());
    return Math.max(0, diffInMonths);
  }

  private mapToJobSeeker(parsedData: ParsedResumeData): Partial<JobSeeker> {
    return {
      firstName: parsedData.firstName,
      lastName: parsedData.lastName,
      email: parsedData.email,
      phone: parsedData.phone,
      location: parsedData.location,
      summary: parsedData.summary,
      skills: parsedData.skills,
      experience: parsedData.experience.map((exp) => {
        const startDate = new Date(exp.startDate);
        const endDate = exp.endDate ? new Date(exp.endDate) : undefined;

        return {
          title: exp.title,
          company: exp.company,
          location: exp.location,
          startDate,
          endDate,
          description: exp.description,
          achievements: exp.achievements,
          duration: this.calculateDuration(startDate, endDate),
        } as unknown as CandidateExperience;
      }),
      education: this.sanitizeEducation(parsedData.education).map((edu) => ({
        institution: edu.institution,
        degree: edu.degree,
        field: edu.field,
        startDate: new Date(edu.startDate),
        endDate: edu.endDate ? new Date(edu.endDate) : undefined,
        description: edu.description,
      })),
      certifications: parsedData.certifications?.map((cert) => ({
        name: cert.name,
        issuer: cert.issuer,
        issueDate: new Date(cert.issueDate),
        expiryDate: cert.expiryDate ? new Date(cert.expiryDate) : undefined,
        credentialId: cert.credentialId,
        credentialUrl: cert.credentialUrl,
      })),
      languages: parsedData.languages,
      linkedinUrl: parsedData.linkedinUrl,
      githubUrl: parsedData.githubUrl,
      portfolioUrl: parsedData.portfolioUrl,
    };
  }

  private mapToGraduate(parsedData: GraduateResumeData): Partial<Graduate> {
    return {
      firstName: parsedData.firstName,
      lastName: parsedData.lastName,
      email: parsedData.email,
      phone: parsedData.phone,
      location: parsedData.location,
      summary: parsedData.summary,
      skills: parsedData.skills || [],
      university: parsedData.university,
      degreeProgram: parsedData.degreeProgram,
      major: parsedData.major,
      graduationYear: parsedData.graduationYear,
      gpa: parsedData.gpa,
      academicAchievements: parsedData.academicAchievements || [],
      academicProjects: parsedData.academicProjects?.map((project) => ({
        name: project.name,
        description: project.description,
        technologies: project.technologies,
        link: project.link,
        date: project.date ? new Date(project.date) : undefined,
      })),
      internships: parsedData.internships?.map((internship) => ({
        company: internship.company,
        role: internship.role,
        startDate: internship.startDate ? new Date(internship.startDate) : undefined,
        endDate: internship.endDate ? new Date(internship.endDate) : undefined,
        duration: internship.startDate
          ? `${new Date(internship.startDate).toLocaleDateString()} - ${internship.endDate ? new Date(internship.endDate).toLocaleDateString() : 'Present'}`
          : '',
        description: internship.description || `${internship.role} role at ${internship.company}`,
        skills: internship.skills || [],
        achievements: internship.achievements || [],
      })),
      languages: parsedData.languages || [],
      linkedinUrl: parsedData.linkedinUrl,
      githubUrl: parsedData.githubUrl,
      portfolioUrl: parsedData.portfolioUrl,
    };
  }

  // Sanitize the response to ensure no null values for required fields
  private sanitizeResponse<T>(response: T): T {
    if (!response) return response;

    const sanitized = { ...response } as any;

    // Use backup data if main fields are missing
    if (sanitized.emailText && (!sanitized.email || sanitized.email === '')) {
      sanitized.email = sanitized.emailText;
      delete sanitized.emailText;
    }

    if (
      sanitized.nameText &&
      (!sanitized.firstName ||
        sanitized.firstName === '' ||
        !sanitized.lastName ||
        sanitized.lastName === '')
    ) {
      const nameParts = sanitized.nameText.split(' ').filter(Boolean);
      if (nameParts.length > 0) {
        if (!sanitized.firstName || sanitized.firstName === '') {
          sanitized.firstName = nameParts[0];
        }
        if (nameParts.length > 1 && (!sanitized.lastName || sanitized.lastName === '')) {
          sanitized.lastName = nameParts.slice(1).join(' ');
        }
      }
      delete sanitized.nameText;
    }

    if (sanitized.phoneText && (!sanitized.phone || sanitized.phone === '')) {
      sanitized.phone = sanitized.phoneText;
      delete sanitized.phoneText;
    }

    if (sanitized.linkedinText && (!sanitized.linkedinUrl || sanitized.linkedinUrl === '')) {
      sanitized.linkedinUrl = sanitized.linkedinText;
      delete sanitized.linkedinText;
    }

    // Handle common fields that should never be null
    if (sanitized.education === null) sanitized.education = [];
    if (sanitized.experience === null) sanitized.experience = [];
    if (sanitized.skills === null) sanitized.skills = [];
    if (sanitized.languages === null) sanitized.languages = ['English'];
    if (sanitized.certifications === null) sanitized.certifications = [];
    if (sanitized.projects === null) sanitized.projects = [];

    // Fix skills array format - if objects with 'skill' property, extract to strings
    if (Array.isArray(sanitized.skills)) {
      sanitized.skills = sanitized.skills
        .map((skill: any) => {
          if (typeof skill === 'object' && skill !== null && 'skill' in skill) {
            return skill.skill;
          }
          return skill;
        })
        .filter(Boolean);
    }

    // Ensure experience items have description
    if (Array.isArray(sanitized.experience)) {
      sanitized.experience = sanitized.experience.map((exp: any) => {
        // Fix combined dates field issue seen in logs
        if (exp.dates && (!exp.startDate || !exp.endDate)) {
          const dates = exp?.dates?.split('-').map((d: string) => d.trim()) || [];
          exp.startDate = dates[0];
          exp.endDate = dates.length > 1 ? dates[1] : 'Present';
          delete exp.dates;
        }

        // Handle job title field variations
        if (exp.jobTitle && !exp.title) {
          exp.title = exp.jobTitle;
          delete exp.jobTitle;
        }

        // Ensure description exists
        if (exp.description === null) exp.description = '';
        if (exp.achievements === null) exp.achievements = [];
        return exp;
      });
    }

    // Ensure education has at least one entry with all required fields
    if (Array.isArray(sanitized.education)) {
      // Filter out empty education entries
      sanitized.education = sanitized.education.filter((edu: any) => {
        return (
          edu && (edu.institution || edu.university || edu.degree || edu.fieldOfStudy || edu.field)
        );
      });

      // Map education fields to expected format and ensure no null values for required string fields
      sanitized.education = sanitized.education.map((edu: any) => {
        const education: any = { ...edu };

        // Map field variations
        if (education.university && !education.institution) {
          education.institution = education.university;
          delete education.university;
        }

        if (education.fieldOfStudy && !education.field) {
          education.field = education.fieldOfStudy;
          delete education.fieldOfStudy;
        }

        // Ensure required fields exist and are not null
        education.degree =
          typeof education.degree === 'string' ? education.degree : 'Not specified';
        education.field = typeof education.field === 'string' ? education.field : 'Not specified';
        education.institution =
          typeof education.institution === 'string' ? education.institution : 'Not specified';
        education.startDate =
          typeof education.startDate === 'string' ? education.startDate : '0000';

        return education;
      });
    }

    // If education is still empty but we have education info in the resume, create an entry
    if (
      Array.isArray(sanitized.education) &&
      sanitized.education.length === 0 &&
      sanitized.educationText
    ) {
      const educationText = sanitized.educationText;

      try {
        // Try to parse education from the text
        // Format like "BSc Psychology from University of Manchester, 2014"
        const degreeMatch = educationText.match(/^([^,]+?)(?:\s+from\s+|,\s*)/i);
        const degree = degreeMatch ? degreeMatch[1].trim() : 'Not specified';

        // Try to extract institution
        const institutionMatch = educationText.match(/from\s+([^,]+)/i);
        const institution = institutionMatch ? institutionMatch[1].trim() : 'Not specified';

        // Extract year if present
        const yearMatch = educationText.match(/(\d{4})/);
        const year = yearMatch ? yearMatch[1] : '0000';

        // Try to extract field
        const fieldMatch =
          degree.match(/([A-Za-z]+)\s+in\s+(.+)/i) || degree.match(/([A-Za-z]+)\s+of\s+(.+)/i);
        const field = fieldMatch ? fieldMatch[2] : 'Not specified';

        sanitized.education = [
          {
            institution,
            degree,
            field,
            startDate: year,
            endDate: year,
          },
        ];
      } catch (e) {
        // Fallback if parsing fails
        sanitized.education = [
          {
            institution: 'Extracted from resume',
            degree: educationText,
            field: 'Not specified',
            startDate: '0000',
            endDate: '0000',
          },
        ];
      }

      delete sanitized.educationText;
    }

    // Double-check education degree fields are never null
    if (Array.isArray(sanitized.education)) {
      sanitized.education = sanitized.education.map((edu: any) => {
        if (edu.degree === null || edu.degree === undefined || edu.degree === '') {
          edu.degree = 'Not specified';
        }
        if (edu.field === null || edu.field === undefined || edu.field === '') {
          edu.field = 'Not specified';
        }
        return edu;
      });
    }

    // Sanitize certifications array
    if (Array.isArray(sanitized.certifications)) {
      sanitized.certifications = sanitized.certifications
        .filter(
          (cert: any) =>
            cert && (typeof cert === 'string' || 'name' in cert || 'certification' in cert),
        )
        .map((cert: any) => {
          if (typeof cert === 'string') {
            return {
              name: cert,
              issuer: 'Not specified',
              issueDate: new Date().toISOString(),
              credentialId: '',
              credentialUrl: 'https://example.com',
            };
          } else if ('certification' in cert && typeof cert.certification === 'string') {
            return {
              name: cert.certification,
              issuer: cert.issuer || 'Not specified',
              issueDate: cert.issueDate
                ? new Date(cert.issueDate).toISOString()
                : new Date().toISOString(),
              credentialId: cert.credentialId || '',
              credentialUrl: cert.credentialUrl || 'https://example.com',
            };
          }

          // Ensure required fields have valid values
          const sanitizedCert = { ...cert };
          sanitizedCert.name =
            typeof sanitizedCert.name === 'string' ? sanitizedCert.name : 'Not specified';
          sanitizedCert.issuer =
            typeof sanitizedCert.issuer === 'string' ? sanitizedCert.issuer : 'Not specified';

          // Make sure issueDate is a valid date
          if (!sanitizedCert.issueDate) {
            sanitizedCert.issueDate = new Date().toISOString();
          } else if (typeof sanitizedCert.issueDate === 'string') {
            try {
              // Try to parse the date string
              sanitizedCert.issueDate = new Date(sanitizedCert.issueDate).toISOString();
            } catch (e) {
              sanitizedCert.issueDate = new Date().toISOString();
            }
          }

          sanitizedCert.credentialId =
            typeof sanitizedCert.credentialId === 'string' ? sanitizedCert.credentialId : '';
          // Ensure credentialUrl is a valid URL or empty
          if (!sanitizedCert.credentialUrl || sanitizedCert.credentialUrl === '') {
            sanitizedCert.credentialUrl = 'https://example.com';
          } else if (!sanitizedCert.credentialUrl.startsWith('http')) {
            sanitizedCert.credentialUrl = 'https://' + sanitizedCert.credentialUrl;
          }

          return sanitizedCert;
        });
    }

    return sanitized as T;
  }

  /**
   * Process multiple resumes concurrently
   * @param resumes Array of resume texts to process
   * @param isGraduate Whether these are graduate resumes
   * @returns Promise that resolves when all resumes are processed
   */
  async processBatch(
    resumes: string[],
    isGraduate = false,
  ): Promise<Array<Partial<JobSeeker | Graduate>>> {
    const batchStartTime = Date.now();

    // Create array of promises for each resume
    const promises = resumes.map((resumeText) => {
      return new Promise<Partial<JobSeeker | Graduate>>((resolve, reject) => {
        // Add to processing queue
        this.processingQueue.push({
          resumeText,
          isGraduate,
          resolve,
          reject,
        });
      });
    });

    // Start processing the queue if not already in progress
    if (!this.processingInProgress) {
      this.processQueue();
    }

    // Wait for all promises to resolve
    const results = await Promise.all(promises);

    const totalTime = (Date.now() - batchStartTime) / 1000;
    const avgTime = totalTime / resumes.length;

    return results;
  }

  /**
   * Process multiple resumes asynchronously with real-time updates
   * @param resumes Array of resume texts to process
   * @param isGraduate Whether these are graduate resumes
   * @returns Observable that emits results as they become available
   */
  processBatchAsync(
    resumes: Array<{ id: string; text: string }>,
    isGraduate = false,
  ): Observable<{ id: string; result: any; error?: any }> {
    // Add each resume to the queue
    resumes.forEach(({ id, text }) => {
      // Add to processing queue
      this.processingQueue.push({
        resumeText: text,
        isGraduate,
        resolve: (result) => {
          this.resultSubject.next({ id, result });
        },
        reject: (error) => {
          this.resultSubject.next({ id, result: null, error });
        },
      });
    });

    // Start processing the queue if not already in progress
    if (!this.processingInProgress) {
      this.processQueue();
    }

    return this.resultSubject.asObservable();
  }

  /**
   * Process the queue with controlled concurrency
   */
  private async processQueue() {
    if (this.processingQueue.length === 0) {
      this.processingInProgress = false;
      return;
    }

    this.processingInProgress = true;

    // Calculate how many new processes we can start
    const runningCount = 0; // This would track currently running processes
    const availableSlots = this.maxConcurrent - runningCount;
    const itemsToProcess = this.processingQueue.splice(0, availableSlots);

    // Process items concurrently using the selected implementation
    await Promise.all(
      itemsToProcess.map(async (item) => {
        try {
          // Use the main methods which will respect the useSlm flag
          const result = item.isGraduate
            ? await this.processGraduateResume(item.resumeText)
            : await this.processResume(item.resumeText);

          item.resolve(result);
        } catch (error) {
          item.reject(error);
        }
      }),
    );

    // Continue processing the queue
    setImmediate(() => this.processQueue());
  }

  /**
   * Checks if resume has all required info in easily extractable format
   * @returns boolean
   */
  private hasRequiredResumeInfo(resumeText: string): boolean {
    // Check for critical fields in the expected format
    const hasName = resumeText.match(/Name:\s*[^\n]+/) !== null;
    const hasEmail = resumeText.match(/Email:\s*[^\n]+/) !== null;
    const hasEducation = resumeText.match(/Education:\s*[^\n]+/) !== null;
    const hasExperience =
      resumeText.match(/Professional Experience:[\s\S]+?(\n\n|\n[A-Z]|$)/) !== null;

    return hasName && hasEmail && (hasEducation || hasExperience);
  }

  // Deep sanitize education data to ensure all fields are valid
  private sanitizeEducation(educationArray: any[]): any[] {
    if (!Array.isArray(educationArray)) {
      return [];
    }

    return educationArray
      .filter((item) => item && typeof item === 'object')
      .map((education) => {
        // Create a new object with default values for all required fields
        const sanitized = {
          institution:
            typeof education.institution === 'string' ? education.institution : 'Not specified',
          degree:
            typeof education.degree === 'string' && education.degree !== ''
              ? education.degree
              : 'Not specified',
          field:
            typeof education.field === 'string' && education.field !== ''
              ? education.field
              : 'Not specified',
          startDate: education.startDate || new Date().toISOString(),
          endDate: education.endDate || null,
          description: typeof education.description === 'string' ? education.description : '',
        };

        // Ensure dates are valid
        try {
          if (sanitized.startDate && typeof sanitized.startDate === 'string') {
            sanitized.startDate = new Date(sanitized.startDate).toISOString();
          }
        } catch (e) {
          sanitized.startDate = new Date().toISOString();
        }

        try {
          if (sanitized.endDate && typeof sanitized.endDate === 'string') {
            sanitized.endDate = new Date(sanitized.endDate).toISOString();
          }
        } catch (e) {
          sanitized.endDate = null;
        }

        return sanitized;
      });
  }
}
