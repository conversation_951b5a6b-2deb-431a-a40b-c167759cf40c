import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { MultiAIResumeParserService } from '../multi-ai-resume-parser.service';
import { GroqService } from '../groq.service';
import { OpenAIService } from '../openai-base.service';
import { CreateCandidateDto } from '@/modules/candidate/dto/create-candidate.dto';

describe('MultiAIResumeParserService Enhanced Conversion', () => {
  let service: MultiAIResumeParserService;
  let groqService: GroqService;
  let openaiService: OpenAIService;

  const mockEnhancedExtractedData = {
    personalInfo: {
      fullName: '<PERSON>',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '******-0123',
      location: 'San Francisco, CA',
    },
    professional: {
      currentJobTitle: 'Senior Software Engineer',
      currentCompany: 'TechCorp Inc.',
      summary: 'Experienced software engineer with 8 years building scalable web applications.',
      yearsOfExperience: 8,
      isCurrentlyEmployed: true,
    },
    skills: ['JavaScript', 'TypeScript', 'React', 'Node.js', 'Python', 'AWS', 'Docker'],
    experience: [
      {
        title: 'Senior Software Engineer',
        company: 'TechCorp Inc.',
        location: 'San Francisco, CA',
        startDate: '2022-01-01',
        endDate: null,
        description: 'Led development of microservices',
        responsibilities: ['Led team', 'Designed architecture'],
        achievements: ['Improved performance by 40%'],
      },
      {
        title: 'Software Engineer',
        company: 'StartupXYZ',
        location: 'San Francisco, CA',
        startDate: '2018-06-01',
        endDate: '2021-12-31',
        description: 'Built APIs',
        responsibilities: ['Developed features'],
        achievements: ['Reduced latency by 60%'],
      },
    ],
    education: [
      {
        degree: 'BS Computer Science',
        field: 'Computer Science',
        institution: 'Stanford University',
        location: 'Stanford, CA',
        gpa: 3.8,
      },
    ],
    certifications: [
      {
        name: 'AWS Certified Solutions Architect',
        issuer: 'Amazon Web Services',
        issueDate: '2023-01-01',
      },
    ],
    languages: ['English', 'Spanish'],
    urls: {
      linkedin: 'https://linkedin.com/in/johndoe',
      github: 'https://github.com/johndoe',
      portfolio: 'https://johndoe.dev',
      otherSocial: ['https://twitter.com/johndoe'],
    },
    preferences: {
      locations: ['San Francisco, CA', 'Remote'],
      remoteOnly: false,
      jobTypes: ['FULL_TIME'],
      industries: ['Technology', 'Software'],
    },
    availability: {
      availableFrom: '2024-02-01',
      noticePeriod: '4 weeks',
    },
    compensation: {
      expectedMin: 200000,
      expectedMax: 250000,
      currency: 'USD',
    },
  };

  const mockLegacyExtractedData = {
    fullName: 'Jane Smith',
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '555-0987',
    location: 'New York, NY',
    jobTitle: 'Product Manager',
    currentCompany: 'BigCorp',
    yearsOfExperience: 5,
    summary: 'Experienced PM',
    skills: ['Product Management', 'Agile'],
    experience: [
      {
        title: 'Product Manager',
        company: 'BigCorp',
        startDate: '2020-01-01',
        endDate: null,
      },
    ],
    linkedinUrl: 'linkedin.com/in/janesmith',
    githubUrl: null,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MultiAIResumeParserService,
        {
          provide: GroqService,
          useValue: {
            extractResumeData: jest.fn(),
            isAvailable: jest.fn().mockReturnValue(true),
          },
        },
        {
          provide: OpenAIService,
          useValue: {
            extractResumeUsingGPT: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('test-key'),
          },
        },
      ],
    }).compile();

    service = module.get<MultiAIResumeParserService>(MultiAIResumeParserService);
    groqService = module.get<GroqService>(GroqService);
    openaiService = module.get<OpenAIService>(OpenAIService);
  });

  describe('convertToCreateCandidateDto', () => {
    it('should convert enhanced nested structure to CreateCandidateDto', () => {
      const jobId = 'test-job-id';
      const result = service['convertToCreateCandidateDto'](mockEnhancedExtractedData, jobId);

      expect(result).toMatchObject({
        jobId,
        fullName: 'John Doe',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '******-0123',
        location: 'San Francisco, CA',
        jobTitle: 'Senior Software Engineer',
        currentCompany: 'TechCorp Inc.',
        summary: expect.stringContaining('Experienced software engineer'),
        skills: expect.arrayContaining(['JavaScript', 'TypeScript']),
        linkedinUrl: 'https://linkedin.com/in/johndoe',
        githubUrl: 'https://github.com/johndoe',
        profileUrl: 'https://johndoe.dev',
        source: 'resume_upload',
      });

      // Verify enhanced data is stored in notes
      expect(result.notes).toBeTruthy();
      const notesData = JSON.parse(result.notes || '{}');
      expect(notesData.enhancedExtraction).toMatchObject({
        yearsOfExperience: 8,
        languages: ['English', 'Spanish'],
        certifications: expect.arrayContaining([
          expect.objectContaining({
            name: 'AWS Certified Solutions Architect',
          }),
        ]),
        preferredLocation: 'San Francisco, CA',
        isRemoteOnly: false,
        availableFrom: '2024-02-01',
        isCurrentlyEmployed: true,
        compensation: {
          min: 200000,
          max: 250000,
          currency: 'USD',
        },
      });
    });

    it('should handle legacy flat structure for backward compatibility', () => {
      const jobId = 'test-job-id';
      const result = service['convertToCreateCandidateDto'](mockLegacyExtractedData, jobId);

      expect(result).toMatchObject({
        jobId,
        fullName: 'Jane Smith',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '555-0987',
        location: 'New York, NY',
        jobTitle: 'Product Manager',
        currentCompany: 'BigCorp',
        summary: 'Experienced PM',
        skills: ['Product Management', 'Agile'],
        linkedinUrl: 'linkedin.com/in/janesmith',
        source: 'resume_upload',
      });
    });

    it('should generate summary when not provided', () => {
      const dataWithoutSummary = {
        ...mockEnhancedExtractedData,
        professional: {
          ...mockEnhancedExtractedData.professional,
          summary: null,
        },
      };

      const result = service['convertToCreateCandidateDto'](dataWithoutSummary, 'job-id');
      expect(result.summary).toBeTruthy();
      expect(result.summary).toContain('Senior Software Engineer');
      expect(result.summary).toContain('8 years');
    });

    it('should calculate years of experience when not provided', () => {
      const dataWithoutYears = {
        ...mockEnhancedExtractedData,
        professional: {
          ...mockEnhancedExtractedData.professional,
          yearsOfExperience: null,
        },
      };

      const result = service['convertToCreateCandidateDto'](dataWithoutYears, 'job-id');
      const notesData = JSON.parse(result.notes || '{}');
      expect(notesData.enhancedExtraction.yearsOfExperience).toBeGreaterThan(0);
    });

    it('should use most recent job as fallback for missing fields', () => {
      const minimalData = {
        experience: [
          {
            title: 'Lead Developer',
            company: 'Tech Inc',
            startDate: '2020-01-01',
            endDate: null,
          },
        ],
        skills: ['Python'],
      };

      const result = service['convertToCreateCandidateDto'](minimalData, 'job-id');
      expect(result.jobTitle).toBe('Lead Developer');
      expect(result.currentCompany).toBe('Tech Inc');
    });

    it('should handle missing URLs gracefully', () => {
      const dataWithoutUrls = {
        ...mockEnhancedExtractedData,
        urls: {},
      };

      const result = service['convertToCreateCandidateDto'](dataWithoutUrls, 'job-id');
      expect(result.linkedinUrl).toBe('');
      expect(result.githubUrl).toBe('');
      expect(result.profileUrl).toBe('');
    });

    it('should extract portfolio URL correctly', () => {
      const result = service['convertToCreateCandidateDto'](mockEnhancedExtractedData, 'job-id');
      expect(result.profileUrl).toBe('https://johndoe.dev');
    });

    it('should handle compensation data correctly', () => {
      const result = service['convertToCreateCandidateDto'](mockEnhancedExtractedData, 'job-id');
      const notesData = JSON.parse(result.notes || '{}');
      expect(notesData.enhancedExtraction.compensation).toEqual({
        min: 200000,
        max: 250000,
        currency: 'USD',
      });
    });

    it('should clean undefined fields from the DTO', () => {
      const minimalData = {
        firstName: 'Test',
        lastName: 'User',
        fullName: 'Test User',
      };

      const result = service['convertToCreateCandidateDto'](minimalData, 'job-id');
      expect(result).not.toHaveProperty('undefined');
      Object.values(result).forEach((value) => {
        expect(value).not.toBe(undefined);
      });
    });

    it('should store provider metadata in notes', () => {
      const result = service['convertToCreateCandidateDto'](mockEnhancedExtractedData, 'job-id');
      const notesData = JSON.parse(result.notes || '{}');
      expect(notesData.provider).toBe('multi-ai');
      expect(notesData.extractedAt).toBeTruthy();
    });
  });

  describe('generateSummary', () => {
    it('should generate meaningful summary from experience', () => {
      const summary = service['generateSummary'](mockEnhancedExtractedData);
      expect(summary).toContain('Senior Software Engineer');
      expect(summary).toContain('8 years');
      expect(summary).toContain('TechCorp Inc.');
      expect(summary).toContain('JavaScript');
    });

    it('should handle empty experience gracefully', () => {
      const dataWithoutExperience = {
        experience: [],
        skills: ['JavaScript'],
      };
      const summary = service['generateSummary'](dataWithoutExperience);
      expect(summary).toBe('');
    });
  });

  describe('calculateYearsOfExperience', () => {
    it('should calculate total years correctly', () => {
      const experience = [
        {
          startDate: '2020-01-01',
          endDate: '2022-01-01', // 2 years
        },
        {
          startDate: '2018-01-01',
          endDate: '2019-12-31', // 2 years
        },
      ];
      const years = service['calculateYearsOfExperience'](experience);
      expect(years).toBe(4);
    });

    it('should handle current job (no end date)', () => {
      const experience = [
        {
          startDate: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000 * 3).toISOString(), // 3 years ago
          endDate: null,
        },
      ];
      const years = service['calculateYearsOfExperience'](experience);
      expect(years).toBeGreaterThanOrEqual(3);
    });

    it('should handle overlapping experiences', () => {
      const experience = [
        {
          startDate: '2020-01-01',
          endDate: '2022-01-01',
        },
        {
          startDate: '2021-01-01', // Overlaps with first
          endDate: '2022-01-01',
        },
      ];
      const years = service['calculateYearsOfExperience'](experience);
      expect(years).toBeGreaterThan(0);
    });

    it('should return 0 for empty experience', () => {
      const years = service['calculateYearsOfExperience']([]);
      expect(years).toBe(0);
    });

    it('should handle invalid dates gracefully', () => {
      const experience = [
        {
          startDate: 'invalid-date',
          endDate: '2022-01-01',
        },
      ];
      const years = service['calculateYearsOfExperience'](experience);
      expect(years).toBe(0);
    });
  });

  describe('parseResumeData with enhanced extraction', () => {
    it('should use Groq service for extraction and apply enhanced conversion', async () => {
      const mockFile = {
        mimetype: 'application/pdf',
        buffer: Buffer.from('mock content'),
        originalname: 'resume.pdf',
        fieldname: 'file',
        encoding: '7bit',
        size: 1000,
        stream: null as any,
        destination: '',
        filename: 'resume.pdf',
        path: '',
      };

      jest.spyOn(groqService, 'extractResumeData').mockResolvedValue({
        ...mockEnhancedExtractedData,
        _rawExtractedData: mockEnhancedExtractedData,
      });

      // Mock file extraction
      jest
        .spyOn(service as any, 'extractWithProvider')
        .mockResolvedValue(mockEnhancedExtractedData);

      const result = await service.parseResumeData(mockFile, 'job-id');

      expect(result.fullName).toBe('John Doe');
      expect(result.notes).toBeTruthy();
      const notesData = JSON.parse(result.notes || '{}');
      expect(notesData.enhancedExtraction.languages).toContain('English');
    });
  });

  describe('provider fallback logic', () => {
    it('should fallback to OpenAI when Groq fails', async () => {
      const mockFile = {
        mimetype: 'application/pdf',
        buffer: Buffer.from('mock content'),
        originalname: 'resume.pdf',
        fieldname: 'file',
        encoding: '7bit',
        size: 1000,
        stream: null as any,
        destination: '',
        filename: 'resume.pdf',
        path: '',
      };

      // Make Groq fail
      jest.spyOn(groqService, 'extractResumeData').mockRejectedValue(new Error('Groq failed'));

      // Mock OpenAI to succeed
      jest.spyOn(service as any, 'extractWithOpenAI').mockResolvedValue(mockLegacyExtractedData);

      const result = await service.parseResumeData(mockFile, 'job-id');

      expect(result.fullName).toBe('Jane Smith');
    });
  });
});
