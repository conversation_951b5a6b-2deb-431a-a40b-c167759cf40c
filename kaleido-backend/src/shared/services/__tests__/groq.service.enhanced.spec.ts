import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { GroqService } from '../groq.service';

describe('GroqService Enhanced Extraction', () => {
  let service: GroqService;
  let mockGroqCreate: jest.Mock;

  const mockResumeContent = `
    <PERSON>
    Senior Software Engineer
    <EMAIL> | ******-0123 | San Francisco, CA
    LinkedIn: linkedin.com/in/johndoe | GitHub: github.com/johndoe

    SUMMARY
    Experienced software engineer with 8 years building scalable web applications.

    EXPER<PERSON>NCE
    Senior Software Engineer - TechCorp Inc. | San Francisco, CA | Remote
    Jan 2022 - Present
    • Led development of microservices architecture
    • Managed team of 5 engineers
    • Improved system performance by 40%

    Software Engineer - StartupXYZ | San Francisco, CA
    Jun 2018 - Dec 2021
    • Built RESTful APIs using Node.js
    • Implemented CI/CD pipelines
    • Reduced deployment time by 60%

    Junior Developer - WebAgency | New York, NY
    Jul 2016 - May 2018
    • Developed responsive web applications
    • Collaborated with design team

    EDUCATION
    BS Computer Science - Stanford University - 2016
    GPA: 3.8/4.0

    SKILLS
    JavaScript, TypeScript, React, Node.js, Python, AWS, Docker, Kubernetes

    CERTIFICATIONS
    AWS Certified Solutions Architect - 2023

    LANGUAGES
    English (Native), Spanish (Fluent)
  `;

  const mockEnhancedResponse = {
    personalInfo: {
      fullName: 'John Doe',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '******-0123',
      location: 'San Francisco, CA',
    },
    professional: {
      currentJobTitle: 'Senior Software Engineer',
      currentCompany: 'TechCorp Inc.',
      summary: 'Experienced software engineer with 8 years building scalable web applications.',
      yearsOfExperience: 8,
      isCurrentlyEmployed: true,
    },
    skills: [
      'JavaScript',
      'TypeScript',
      'React',
      'Node.js',
      'Python',
      'AWS',
      'Docker',
      'Kubernetes',
    ],
    experience: [
      {
        title: 'Senior Software Engineer',
        company: 'TechCorp Inc.',
        location: 'San Francisco, CA',
        startDate: '2022-01-01',
        endDate: null,
        description: 'Led development of microservices architecture',
        responsibilities: [
          'Led development of microservices architecture',
          'Managed team of 5 engineers',
        ],
        achievements: ['Improved system performance by 40%'],
      },
      {
        title: 'Software Engineer',
        company: 'StartupXYZ',
        location: 'San Francisco, CA',
        startDate: '2018-06-01',
        endDate: '2021-12-31',
        description: 'Built RESTful APIs using Node.js',
        responsibilities: ['Built RESTful APIs using Node.js', 'Implemented CI/CD pipelines'],
        achievements: ['Reduced deployment time by 60%'],
      },
    ],
    education: [
      {
        degree: 'BS Computer Science',
        field: 'Computer Science',
        institution: 'Stanford University',
        location: 'Stanford, CA',
        startDate: '2012-09-01',
        endDate: '2016-05-31',
        gpa: 3.8,
        honors: [],
      },
    ],
    certifications: [
      {
        name: 'AWS Certified Solutions Architect',
        issuer: 'Amazon Web Services',
        issueDate: '2023-01-01',
        expiryDate: '2026-01-01',
        credentialId: null,
      },
    ],
    languages: ['English', 'Spanish'],
    achievements: [],
    urls: {
      linkedin: 'https://linkedin.com/in/johndoe',
      github: 'https://github.com/johndoe',
      portfolio: null,
      otherSocial: [],
    },
    preferences: {
      locations: ['San Francisco, CA'],
      remoteOnly: false,
      jobTypes: ['FULL_TIME'],
      industries: ['Technology', 'Software'],
    },
    availability: {
      availableFrom: '2024-02-01',
      noticePeriod: '4 weeks',
    },
    compensation: {
      current: 180000,
      expectedMin: 200000,
      expectedMax: 250000,
      currency: 'USD',
    },
  };

  beforeEach(async () => {
    mockGroqCreate = jest.fn();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GroqService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key) => {
              if (key === 'GROQ_API_KEY') return 'test-api-key';
              return null;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<GroqService>(GroqService);

    // Mock the Groq client
    service['groq'].chat = {
      completions: {
        create: mockGroqCreate,
      },
    } as any;
  });

  describe('extractResumeData with Enhanced Intelligence', () => {
    it('should extract and intelligently infer all fields from a complete resume', async () => {
      mockGroqCreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(mockEnhancedResponse),
            },
          },
        ],
      });

      const result = await service.extractResumeData(mockResumeContent);

      // Verify the enhanced prompt was used
      expect(mockGroqCreate).toHaveBeenCalledWith(
        expect.objectContaining({
          model: 'llama-3.3-70b-versatile',
          temperature: 0,
          messages: expect.arrayContaining([
            expect.objectContaining({
              role: 'system',
              content: expect.stringContaining('ALWAYS INFER these fields'),
            }),
          ]),
        }),
      );

      // Verify transformed data structure
      expect(result).toMatchObject({
        fullName: 'John Doe',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '******-0123',
        location: 'San Francisco, CA',
        jobTitle: 'Senior Software Engineer',
        currentCompany: 'TechCorp Inc.',
        yearsOfExperience: 8,
        summary: expect.any(String),
        skills: expect.arrayContaining(['JavaScript', 'TypeScript']),
        languages: expect.arrayContaining(['English']),
        isCurrentlyEmployed: true,
      });
    });

    it('should infer languages from resume content when not explicitly stated', async () => {
      const responseWithoutLanguages = { ...mockEnhancedResponse };
      responseWithoutLanguages.languages = ['English']; // AI should always add this

      mockGroqCreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(responseWithoutLanguages),
            },
          },
        ],
      });

      const result = await service.extractResumeData('Resume content in English...');

      expect(result.languages).toContain('English');
    });

    it('should calculate availability based on employment status', async () => {
      // Test currently employed candidate
      const employedResponse = { ...mockEnhancedResponse };
      employedResponse.professional.isCurrentlyEmployed = true;
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 28); // 4 weeks
      employedResponse.availability.availableFrom = futureDate.toISOString().split('T')[0];

      mockGroqCreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(employedResponse),
            },
          },
        ],
      });

      const employedResult = await service.extractResumeData(mockResumeContent);
      expect(employedResult.availability.availableFrom).toBeTruthy();
      expect(employedResult.availability.noticePeriod).toMatch(/weeks/);

      // Test unemployed candidate
      const unemployedResponse = { ...mockEnhancedResponse };
      unemployedResponse.professional.isCurrentlyEmployed = false;
      unemployedResponse.availability.availableFrom = new Date().toISOString().split('T')[0];
      unemployedResponse.availability.noticePeriod = 'Immediate';

      mockGroqCreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(unemployedResponse),
            },
          },
        ],
      });

      const unemployedResult = await service.extractResumeData(mockResumeContent);
      expect(unemployedResult.availability.noticePeriod).toBe('Immediate');
    });

    it('should detect remote work preference from job history', async () => {
      const remoteJobsResponse = { ...mockEnhancedResponse };
      remoteJobsResponse.experience[0].location = 'Remote';
      remoteJobsResponse.experience[1].location = 'Remote';
      remoteJobsResponse.preferences.remoteOnly = true;

      mockGroqCreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(remoteJobsResponse),
            },
          },
        ],
      });

      const result = await service.extractResumeData(mockResumeContent);
      expect(result.preferences.remoteOnly).toBe(true);
    });

    it('should generate summary when not provided', async () => {
      const responseWithoutSummary = { ...mockEnhancedResponse };
      responseWithoutSummary.professional.summary =
        'Senior Software Engineer with 8 years of experience. Most recently at TechCorp Inc. Skilled in JavaScript, TypeScript, React.';

      mockGroqCreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(responseWithoutSummary),
            },
          },
        ],
      });

      const result = await service.extractResumeData('Resume without summary section');
      expect(result.summary).toBeTruthy();
      expect(result.summary).toContain('experience');
    });

    it('should estimate salary based on role and location', async () => {
      const responseWithSalaryEstimate = { ...mockEnhancedResponse };
      responseWithSalaryEstimate.compensation = {
        current: 0, // Use 0 for unknown current salary
        expectedMin: 180000,
        expectedMax: 220000,
        currency: 'USD',
      };

      mockGroqCreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(responseWithSalaryEstimate),
            },
          },
        ],
      });

      const result = await service.extractResumeData(mockResumeContent);
      expect(result.compensation).toBeTruthy();
      expect(result.compensation.currency).toBe('USD');
      expect(result.compensation.expectedMin).toBeGreaterThan(0);
    });

    it('should handle malformed JSON responses gracefully', async () => {
      mockGroqCreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: '```json\n' + JSON.stringify(mockEnhancedResponse) + '\n```',
            },
          },
        ],
      });

      const result = await service.extractResumeData(mockResumeContent);
      expect(result).toBeTruthy();
      expect(result.fullName).toBe('John Doe');
    });

    it('should extract skills from entire document, not just skills section', async () => {
      const resumeWithSkillsInDescription = `
        Jane Smith
        <EMAIL>
        
        EXPERIENCE
        Software Engineer
        - Developed applications using React and Redux
        - Implemented GraphQL APIs
        - Used Docker for containerization
        
        SKILLS
        JavaScript
      `;

      const responseWithAllSkills = { ...mockEnhancedResponse };
      responseWithAllSkills.skills = ['JavaScript', 'React', 'Redux', 'GraphQL', 'Docker'];

      mockGroqCreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(responseWithAllSkills),
            },
          },
        ],
      });

      const result = await service.extractResumeData(resumeWithSkillsInDescription);
      expect(result.skills).toContain('React');
      expect(result.skills).toContain('GraphQL');
      expect(result.skills).toContain('Docker');
    });

    it('should calculate years of experience from work history', async () => {
      const responseWithCalculatedExperience = { ...mockEnhancedResponse };
      responseWithCalculatedExperience.professional.yearsOfExperience = 8;

      mockGroqCreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(responseWithCalculatedExperience),
            },
          },
        ],
      });

      const result = await service.extractResumeData(mockResumeContent);
      expect(result.yearsOfExperience).toBe(8);
    });

    it('should use current location as preferred location when not specified', async () => {
      const responseWithDefaultLocation = { ...mockEnhancedResponse };
      responseWithDefaultLocation.preferences.locations = ['San Francisco, CA'];

      mockGroqCreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(responseWithDefaultLocation),
            },
          },
        ],
      });

      const result = await service.extractResumeData(mockResumeContent);
      expect(result.preferences.locations).toContain('San Francisco, CA');
    });

    it('should handle retry logic on failure', async () => {
      // First call fails, second succeeds
      mockGroqCreate.mockRejectedValueOnce(new Error('Network error')).mockResolvedValueOnce({
        choices: [
          {
            message: {
              content: JSON.stringify(mockEnhancedResponse),
            },
          },
        ],
      });

      const result = await service.extractResumeData(mockResumeContent);
      expect(result).toBeTruthy();
      expect(mockGroqCreate).toHaveBeenCalledTimes(2);
    });

    it('should preserve raw extracted data for future processing', async () => {
      mockGroqCreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(mockEnhancedResponse),
            },
          },
        ],
      });

      const result = await service.extractResumeData(mockResumeContent);
      expect(result._rawExtractedData).toBeTruthy();
      expect(result._rawExtractedData.personalInfo).toBeTruthy();
      expect(result._rawExtractedData.professional).toBeTruthy();
    });
  });

  describe('extractMultipleResumes with Enhanced Extraction', () => {
    it('should process multiple resumes in batches with enhanced extraction', async () => {
      const resumeContents = Array(150).fill(mockResumeContent);

      mockGroqCreate.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify(mockEnhancedResponse),
            },
          },
        ],
      });

      const results = await service.extractMultipleResumes(resumeContents);

      expect(results.successful.length).toBeGreaterThan(0);
      expect(results.successful[0]).toMatchObject({
        fullName: 'John Doe',
        languages: expect.arrayContaining(['English']),
      });

      // Verify batching (100 per batch)
      expect(mockGroqCreate.mock.calls.length).toBeLessThanOrEqual(150);
    });
  });
});
