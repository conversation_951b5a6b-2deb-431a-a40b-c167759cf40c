import OpenAI from 'openai';
import { Repository } from 'typeorm';

import { CandidateEvaluation } from '@/modules/candidate/entities/candidate-evaluation.entity';
import { CandidateTier } from '@/modules/candidate/entities/candidate.entity';
import { Candidate } from '@/modules/entities';
import { Job } from '@/modules/job/entities/job.entity';
import { CandidateStatus } from '@/shared/types';
import { safeJsonParse } from '@/utils/json-cleaner.util';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { ProcessApplicationProps, ProcessedApplication } from '../types/openai.types';
import { OpenAIModel, OpenAIService } from './openai-base.service';

@Injectable()
export class ApplicationProcessorService extends OpenAIService {
  constructor(
    @InjectRepository(Candidate)
    private readonly candidateRepository: Repository<Candidate>,
    @InjectRepository(CandidateEvaluation)
    private readonly candidateEvaluationRepository: Repository<CandidateEvaluation>,
  ) {
    super();
  }

  private extractJobMatchingInfo(job: Job) {
    return {
      department: job.department,
      skills: job.skills || [],
      jobType: job.jobType,
      requirements: job.requirements || [],
      jobResponsibilities: job.jobResponsibilities,
      experience: job.experience,
      location: job.location || [],
      education: job.education || [],
      softSkills: job.softSkills || [],
      language: job.language || [],
      typeOfJob: job.typeOfJob,
      typeOfHiring: job.typeOfHiring,
      cultureFitDescription: job.cultureFitDescription,
      companyValues: job.companyValues || [],
    };
  }

  async processApplications({ job }: ProcessApplicationProps): Promise<ProcessedApplication[]> {
    const jobId = job?.id;
    try {
      if (!job?.candidates?.length) {
        throw new Error('You do not have any candidates attached to this job.');
      }

      // Validate that tier thresholds are set
      if (!job.topCandidateThreshold || !job.secondTierCandidateThreshold) {
        throw new Error(
          'Tier thresholds must be set before matching and ranking candidates. Please configure the top candidate threshold and second tier threshold for this job.',
        );
      }

      const jobImportantInfo = this.extractJobMatchingInfo(job);

      // Process candidates that are new or marked for re-evaluation
      const candidatesToProcess = job.candidates.filter((candidate) => {
        // If the candidate has no evaluations or is not in MATCHED status, process them
        if (!candidate.evaluations || candidate.evaluations.length === 0) {
          return true;
        }

        // Check if the candidate has an evaluation for this job
        const jobEvaluation = candidate.evaluations.find(
          (evaluation) => evaluation.jobId === jobId,
        );

        // Process if no evaluation exists for this job
        if (!jobEvaluation) {
          return true;
        }

        // Process if the candidate is marked for re-evaluation (MATCH_MODIFIED)
        if (jobEvaluation.status === CandidateStatus.MATCH_MODIFIED) {
          return true;
        }

        // Don't process if already matched
        return jobEvaluation.status !== CandidateStatus.MATCHED;
      });

      const skippedCandidateIds = job.candidates
        .filter((c) => !candidatesToProcess.includes(c))
        .map((c) => c.id);

      if (skippedCandidateIds.length > 0) {
      }

      if (candidatesToProcess.length === 0) {
        return [];
      }

      // Estimate context size for model selection
      const jobContextSize = this.estimateTokenCount(
        JSON.stringify(jobImportantInfo) +
          JSON.stringify(job.requirements) +
          job.department +
          (job.finalDraft || ''),
      );

      // Process candidates in batches
      const processedApplications = await this.processBatch(
        candidatesToProcess,
        async (candidate, model) => {
          const candidateProfile = {
            fullName: candidate.fullName,
            jobTitle: candidate.jobTitle,
            skills: candidate.skills,
            experience: candidate.experience,
            summary: candidate.summary,
            location: candidate.location,
          };

          // Estimate candidate context size
          const candidateContextSize = this.estimateTokenCount(JSON.stringify(candidateProfile));
          const totalContextSize = jobContextSize + candidateContextSize;

          // Use the provided model or select based on context size
          const selectedModel = model || this.selectModel(totalContextSize, 'balanced');

          const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
            {
              role: 'system',
              content: `You are an expert AI recruiter specializing in talent assessment and job matching. Your primary focus is on ensuring candidates have the right skills, experience, and potential for success in the role. Be fair but critical - a manager cannot do an architect's job and vice versa.

CRITICAL INSTRUCTIONS:
1. Base your evaluation on the candidate's actual experience and skills
2. If no years of experience are provided, DO NOT assume 0 years - evaluate based on roles and achievements
3. ROLE MATCHING IS CRUCIAL - A project manager cannot score high for a technical architect role

Analyze the candidate profile against the job requirements with these scoring criteria:

1. Role & Function Match (35% of total score) - MOST CRITICAL:
- Job function alignment
  * Same exact role/function: 85-100 points
  * Closely related technical role: 60-84 points
  * Adjacent role in same domain: 35-59 points
  * Management role for technical position: 15-34 points (PENALIZE HEAVILY)
  * Completely different function: 0-14 points
- Core responsibilities match
  * For architects: Must have architecture/design experience
  * For developers: Must have hands-on coding experience
  * For managers: Must have team/project management experience
  * For analysts: Must have analysis/data experience

2. Skills Match (35% of total score):
- Required technical skills (80% of skills score)
  * Direct experience with exact required skills: 85-100 points
  * Related skills with evidence of use: 60-84 points
  * Adjacent skills requiring ramp-up: 35-59 points
  * Missing core technical skills: 0-34 points
- Supporting skills (20% of skills score)
  * Nice-to-have skills that enhance candidacy

3. Experience Level & Quality (25% of total score):
- Years of relevant experience (50% of experience score)
  * Meet or exceed requirements: 85-100 points
  * Within 1-2 years of requirement: 60-84 points
  * 3+ years gap from requirement: 35-59 points
  * Significant experience gap: 0-34 points
- Quality and complexity of experience (50% of experience score)
  * Enterprise/complex projects: Higher score
  * Relevant industry experience: Bonus points
  * Progressive responsibility: Positive indicator

4. Location (5% of total score only):
- Geographic compatibility
  * Local or remote-eligible: 90-100 points
  * Willing to relocate: 70-89 points
  * Requires sponsorship: 30-69 points
  * Cannot relocate: 0-29 points

CRITICAL SCORING RULES:
1. ROLE MISMATCH PENALTIES:
   - Manager applying for architect role: Cap at 40% total
   - Non-technical role for technical position: Cap at 35% total
   - Junior applying for senior architect: Cap at 45% total
   - Different domain entirely: Cap at 30% total

2. MINIMUM REQUIREMENTS:
   - Cloud Architect: MUST have cloud architecture experience
   - Software Engineer: MUST have programming experience
   - Data Scientist: MUST have data analysis/ML experience
   - DevOps Engineer: MUST have infrastructure/automation experience
   - Project Manager: MUST have project management experience

3. SCORING INTERPRETATION:
   - 75-100: Excellent match - strongly recommend
   - 60-74: Good match - recommend interview
   - 45-59: Possible match - consider if strong in key areas
   - 30-44: Weak match - significant gaps
   - 0-29: Poor match - not recommended

EXAMPLE PENALTIES:
- IT Manager → Cloud Architect: Max 40% (lacks technical architecture experience)
- Project Manager → Software Engineer: Max 35% (lacks coding experience)
- Developer → Data Scientist: Max 55% (different technical domain)
- Sales Manager → DevOps Engineer: Max 25% (completely different field)

When calculating experience:
1. yearsOfRelevantExperience: Only count years in the SAME or CLOSELY RELATED role
   - Management years don't count for technical roles
   - General IT experience doesn't equal specialized experience
   - Only roles that directly relate to the job requirements
2. totalYearsOfWorkExperience: Calculate from first job to current date
   - Include ALL professional experience regardless of relevance
   - If candidate started in 2020 and it's now 2025, total = 5 years
   - This shows career progression and work history

CRITICAL: Return ONLY valid JSON without any markdown formatting, code blocks (no json code blocks), backticks, or explanatory text.
Your response must start with { and end with }.
Return your analysis in this exact JSON structure:
{
  "matchScore": number (0-100),
  "criterionMatchedOn": ["Skills Match", "Experience Relevance", "Location and Availability"],
  "yourReasoningForScoring": "string - detailed explanation of the candidate's fit",
  "hiringRecommendation": "STRONG_HIRE" | "HIRE" | "CONSIDER" | "NOT_RECOMMENDED",
  "interviewRecommendation": boolean,
  "detailedScoreAnalysis": {
    "areasOfStrength": ["string array of 3-5 key strengths"],
    "detailedReasoning": {
      "skillsMatch": number (0-100),
      "skillsAnalysis": {
        "technicalSkillsScore": number (0-100),
        "domainSkillsScore": number (0-100),
        "toolsAndTechnologiesScore": number (0-100),
        "softSkillsScore": number (0-100),
        "matchedSkills": ["array of exact matched skills"],
        "relatedSkills": ["array of related/transferable skills"],
        "criticalSkillsPresent": ["array of critical skills the candidate has"],
        "skillGaps": ["array of important skills the candidate is missing"]
      },
      "experienceRelevance": {
        "yearsOfRelevantExperience": number,
        "totalYearsOfWorkExperience": number,
        "industryExpertise": number (0-100),
        "roleLevel": number (0-100),
        "projectComplexity": number (0-100),
        "teamSize": number | null,
        "relevantRoles": ["array of relevant previous roles"],
        "relevantCompanies": ["array of relevant previous companies"]
      },
      "locationAndAvailability": number (0-100),
      "locationAnalysis": {
        "exactLocationMatch": boolean,
        "distanceFromJobLocation": "string description",
        "timeZoneCompatibility": number (0-100),
        "remoteWorkCompatibility": number (0-100),
        "relocationRequired": boolean,
        "relocationFeasibility": number (0-100, if relocation required)
      }
    },
    "areasForImprovement": ["array of 2-3 improvement areas"],
    "overallMatchPercentage": number (same as matchScore),
    "specificCriteriaMatched": {
      "skillsMatch": number (0-100),
      "experienceRelevance": number (0-100),
      "locationAndAvailability": number (0-100),
      "softSkillsMatch": number (0-100),
      "leadershipCapability": number (0-100)
    },
    "missingCriticalRequirements": ["array of missing requirements or empty array"],
    "potentialGrowthAreas": ["array of potential growth areas"],
    "culturalFitIndicators": ["array of cultural fit indicators"],
    "trainingNeeds": ["array of training areas that would improve candidate fit"],
    "retentionRiskFactors": ["array of factors that might affect retention"],
    "compensationConsiderations": {
      "marketRateAlignment": "string assessment",
      "experienceToCompensationRatio": "string assessment"
    },
    "interviewFocusAreas": ["array of areas to focus on during interviews"],
    "teamFitAssessment": {
      "skillComplementarity": number (0-100),
      "uniquePerspectives": ["array of unique perspectives the candidate brings"],
      "collaborationPotential": number (0-100),
      "diversityContribution": "string assessment",
      "leadershipFit": "string assessment",
      "teamDynamicsImpact": "string assessment"
    }
  }
}`,
            },
            {
              role: 'user',
              content: JSON.stringify({
                job: jobImportantInfo,
                department: job.department,
                topCandidateThreshold: job.topCandidateThreshold,
                secondTierCandidateThreshold: job.secondTierCandidateThreshold,
                candidateProfile,
                evaluationRequest:
                  'Please provide a complete candidate evaluation based on the provided criteria.',
                requirements: job.requirements,
              }),
            },
          ];

          const response = await this.openai.chat.completions.create({
            model: selectedModel,
            messages,
            response_format: { type: 'json_object' },
            temperature: 0.2,
          });

          const content = response.choices[0]?.message?.content;

          if (!content) {
            throw new Error('No content returned from OpenAI');
          }

          try {
            const parsed = safeJsonParse(content);

            const scores = {
              experienceScore: this.normalizeScore(
                parsed.detailedScoreAnalysis.specificCriteriaMatched.experienceRelevance,
              ),
              skillsScore: this.normalizeScore(
                parsed.detailedScoreAnalysis.specificCriteriaMatched.skillsMatch,
              ),
              locationScore: this.normalizeScore(
                parsed.detailedScoreAnalysis.specificCriteriaMatched.locationAndAvailability,
              ),
              educationScore: undefined,
            };

            // Ensure we always have a match score, defaulting to 0 if not provided
            const matchScore =
              typeof parsed.matchScore === 'number' ? this.normalizeScore(parsed.matchScore) : 0;

            const processedApplication: ProcessedApplication = {
              jobId,
              candidateId: candidate.id,
              matchScore, // This will now always be a number (0 if not provided)
              criterionMatchedOn: parsed.criterionMatchedOn || [],
              yourReasoningForScoring: parsed.yourReasoningForScoring || 'No reasoning provided',
              hiringRecommendation: parsed.hiringRecommendation || 'CONSIDER',
              // Set interviewRecommendation to true for STRONG_HIRE, HIRE, and CONSIDER recommendations
              interviewRecommendation:
                parsed.interviewRecommendation !== undefined
                  ? parsed.interviewRecommendation
                  : ['STRONG_HIRE', 'HIRE', 'CONSIDER'].includes(
                      parsed.hiringRecommendation || 'CONSIDER',
                    ),
              scores,
              detailedAnalysis: {
                strengthAreas: parsed.detailedScoreAnalysis.areasOfStrength || [],
                improvementAreas: parsed.detailedScoreAnalysis.areasForImprovement || [],
                keySkillsMatched:
                  parsed.criterionMatchedOn?.filter((criterion: string) =>
                    criterion.toLowerCase().includes('skill'),
                  ) || [],
                missingCriticalSkills:
                  parsed.detailedScoreAnalysis.missingCriticalRequirements || [],
                relevantExperience: {
                  yearsOfExperience:
                    parsed.detailedScoreAnalysis.detailedReasoning.experienceRelevance
                      .yearsOfRelevantExperience || 0,
                  totalYearsOfExperience:
                    parsed.detailedScoreAnalysis.detailedReasoning.experienceRelevance
                      .totalYearsOfWorkExperience || 0,
                  relevantRoles:
                    parsed.detailedScoreAnalysis.detailedReasoning.experienceRelevance
                      .relevantRoles || [],
                  relevantCompanies:
                    parsed.detailedScoreAnalysis.detailedReasoning.experienceRelevance
                      .relevantCompanies || [],
                  industryMatch:
                    parsed.detailedScoreAnalysis.detailedReasoning.experienceRelevance
                      .industryExpertise > 50,
                },
                locationAnalysis: parsed.detailedScoreAnalysis.detailedReasoning
                  .locationAnalysis || {
                  exactLocationMatch: false,
                  distanceFromJobLocation: 'Unknown',
                  timeZoneCompatibility: 0,
                  remoteWorkCompatibility: 0,
                  relocationRequired: false,
                  relocationFeasibility: 0,
                },
                trainingNeeds: parsed.detailedScoreAnalysis.trainingNeeds || [],
                retentionRiskFactors: parsed.detailedScoreAnalysis.retentionRiskFactors || [],
                compensationConsiderations: parsed.detailedScoreAnalysis
                  .compensationConsiderations || {
                  marketRateAlignment: 'Unknown',
                  experienceToCompensationRatio: 'Unknown',
                },
                interviewFocusAreas: parsed.detailedScoreAnalysis.interviewFocusAreas || [],
                skillsAnalysis: {
                  criticalSkillsPresent:
                    parsed.detailedScoreAnalysis.detailedReasoning.skillsAnalysis
                      ?.criticalSkillsPresent || [],
                  skillGaps:
                    parsed.detailedScoreAnalysis.detailedReasoning.skillsAnalysis?.skillGaps || [],
                },
              },
              detailedScoreAnalysis: parsed.detailedScoreAnalysis,
            };

            if (candidate.id) {
              // Create a new evaluation for this candidate-job pair
              const evaluationData = {
                // Use the same matchScore we calculated above to ensure consistency
                matchScore: matchScore,
                criterionMatchedOn: parsed.criterionMatchedOn || [],
                yourReasoningForScoring: parsed.yourReasoningForScoring || null,
                hiringRecommendation: parsed.hiringRecommendation || 'CONSIDER',
                // Set interviewRecommendation to true for STRONG_HIRE, HIRE, and CONSIDER recommendations
                interviewRecommendation:
                  parsed.interviewRecommendation !== undefined
                    ? parsed.interviewRecommendation
                    : ['STRONG_HIRE', 'HIRE', 'CONSIDER'].includes(
                        parsed.hiringRecommendation || 'CONSIDER',
                      ),
                detailedScoreAnalysis: parsed.detailedScoreAnalysis
                  ? {
                      areasOfStrength: parsed.detailedScoreAnalysis.areasOfStrength || [],
                      detailedReasoning: {
                        skillsMatch:
                          parsed.detailedScoreAnalysis.detailedReasoning.skillsMatch || 0,
                        skillsAnalysis: parsed.detailedScoreAnalysis.detailedReasoning
                          .skillsAnalysis || {
                          technicalSkillsScore: 0,
                          domainSkillsScore: 0,
                          toolsAndTechnologiesScore: 0,
                          softSkillsScore: 0,
                          matchedSkills: [],
                          relatedSkills: [],
                          criticalSkillsPresent: [],
                          skillGaps: [],
                        },
                        experienceRelevance: {
                          yearsOfRelevantExperience:
                            parsed.detailedScoreAnalysis.detailedReasoning.experienceRelevance
                              .yearsOfRelevantExperience || 0,
                          totalYearsOfWorkExperience:
                            parsed.detailedScoreAnalysis.detailedReasoning.experienceRelevance
                              .totalYearsOfWorkExperience || 0,
                          industryExpertise:
                            parsed.detailedScoreAnalysis.detailedReasoning.experienceRelevance
                              .industryExpertise || 0,
                          roleLevel:
                            parsed.detailedScoreAnalysis.detailedReasoning.experienceRelevance
                              .roleLevel || 0,
                          projectComplexity:
                            parsed.detailedScoreAnalysis.detailedReasoning.experienceRelevance
                              .projectComplexity || 0,
                          teamSize:
                            parsed.detailedScoreAnalysis.detailedReasoning.experienceRelevance
                              .teamSize || null,
                          relevantRoles:
                            parsed.detailedScoreAnalysis.detailedReasoning.experienceRelevance
                              .relevantRoles || [],
                          relevantCompanies:
                            parsed.detailedScoreAnalysis.detailedReasoning.experienceRelevance
                              .relevantCompanies || [],
                        },
                        locationAndAvailability:
                          parsed.detailedScoreAnalysis.detailedReasoning.locationAndAvailability ||
                          0,
                        locationAnalysis: parsed.detailedScoreAnalysis.detailedReasoning
                          .locationAnalysis || {
                          exactLocationMatch: false,
                          distanceFromJobLocation: 'Unknown',
                          timeZoneCompatibility: 0,
                          remoteWorkCompatibility: 0,
                          relocationRequired: false,
                          relocationFeasibility: 0,
                        },
                      },
                      areasForImprovement: parsed.detailedScoreAnalysis.areasForImprovement || [],
                      overallMatchPercentage:
                        parsed.detailedScoreAnalysis.overallMatchPercentage || 0,
                      specificCriteriaMatched: {
                        skillsMatch:
                          parsed.detailedScoreAnalysis.specificCriteriaMatched.skillsMatch || 0,
                        experienceRelevance:
                          parsed.detailedScoreAnalysis.specificCriteriaMatched
                            .experienceRelevance || 0,
                        locationAndAvailability:
                          parsed.detailedScoreAnalysis.specificCriteriaMatched
                            .locationAndAvailability || 0,
                        softSkillsMatch:
                          parsed.detailedScoreAnalysis.specificCriteriaMatched.softSkillsMatch || 0,
                        leadershipCapability:
                          parsed.detailedScoreAnalysis.specificCriteriaMatched
                            .leadershipCapability || 0,
                      },
                      missingCriticalRequirements:
                        parsed.detailedScoreAnalysis.missingCriticalRequirements || [],
                      potentialGrowthAreas: parsed.detailedScoreAnalysis.potentialGrowthAreas || [],
                      culturalFitIndicators:
                        parsed.detailedScoreAnalysis.culturalFitIndicators || [],
                      trainingNeeds: parsed.detailedScoreAnalysis.trainingNeeds || [],
                      retentionRiskFactors: parsed.detailedScoreAnalysis.retentionRiskFactors || [],
                      compensationConsiderations: parsed.detailedScoreAnalysis
                        .compensationConsiderations || {
                        marketRateAlignment: 'Unknown',
                        experienceToCompensationRatio: 'Unknown',
                      },
                      interviewFocusAreas: parsed.detailedScoreAnalysis.interviewFocusAreas || [],
                      teamFitAssessment: parsed.detailedScoreAnalysis.teamFitAssessment || {
                        skillComplementarity: 0,
                        uniquePerspectives: [],
                        collaborationPotential: 0,
                        diversityContribution: 'Not assessed',
                        leadershipFit: 'Not assessed',
                        teamDynamicsImpact: 'Not assessed',
                      },
                    }
                  : null,
                lastEvaluatedAt: new Date(),
              };

              // First update the candidate status
              await this.candidateRepository.update(
                { id: candidate.id },
                {
                  status: CandidateStatus.MATCHED,
                  userId: job.clientId,
                  clientId: job.clientId,
                },
              );

              // Then create a new evaluation entry
              await this.candidateEvaluationRepository.save({
                candidateId: candidate.id,
                jobId: jobId,
                status: CandidateStatus.MATCHED,
                tier: CandidateTier.OTHER, // Will be updated in updateCandidateRanks
                // Ensure matchScore is always a number (0 if not provided)
                matchScore:
                  typeof evaluationData.matchScore === 'number' ? evaluationData.matchScore : 0,
                evaluation: evaluationData,
              });
            } else {
              console.warn('Skipping update for candidate with no ID');
            }

            return processedApplication;
          } catch (parseError) {
            console.error('Error parsing OpenAI response:', parseError);
            throw new Error('Failed to parse OpenAI response');
          }
        },
        {
          // Configure batch processing - balanced for performance and stability
          batchSize: 5, // Process 5 candidates at a time (back to original, but with timeout protection)
          concurrentBatches: 2, // Run 2 batches concurrently (10 candidates total at once)
          model: OpenAIModel.GPT_4O_MINI, // Use the cost-efficient model with large context window
          temperature: 0.2,
        },
      );

      // Update candidate ranks using the existing method
      await this.updateCandidateRanks(jobId);

      // Return the processed applications
      return processedApplications;
    } catch (error) {
      console.error('Error processing applications:', error);
      throw new Error('Failed to process applications');
    }
  }

  private async updateCandidateRanks(jobId: string): Promise<void> {
    // Get all candidate evaluations for this job
    const candidateEvaluations = await this.candidateEvaluationRepository.find({
      where: { jobId },
      relations: ['candidate', 'job'],
      order: { matchScore: 'DESC' },
    });

    if (!candidateEvaluations.length) return;

    // Get the job to access tier thresholds
    const job = candidateEvaluations[0].job;
    // Use job entity thresholds - thresholds are guaranteed to exist at this point
    const topThreshold = job.topCandidateThreshold!;
    const secondTierThreshold = job.secondTierCandidateThreshold!;

    // Update tiers based on match scores and thresholds
    const updatePromises = candidateEvaluations.map(async (evaluation) => {
      let tier = CandidateTier.OTHER;
      // Ensure score is always a number (0 if not provided or invalid)
      const score =
        typeof evaluation.matchScore === 'number' && !isNaN(evaluation.matchScore)
          ? evaluation.matchScore
          : 0;

      if (score >= topThreshold) {
        tier = CandidateTier.TOP;
      } else if (score >= secondTierThreshold) {
        tier = CandidateTier.SECOND;
      }

      // Update the evaluation tier
      return this.candidateEvaluationRepository.update({ id: evaluation.id }, { tier });
    });

    await Promise.all(updatePromises);
  }
}
