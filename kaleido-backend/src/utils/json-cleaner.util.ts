/**
 * Utility to clean JSON responses from AI models that may include markdown formatting
 */

export function cleanJsonResponse(content: string): string {
  if (!content) {
    throw new Error('Empty content provided');
  }

  let cleaned = content.trim();

  // Remove markdown code blocks
  if (cleaned.includes('```json')) {
    cleaned = cleaned.replace(/```json\s*/g, '');
    cleaned = cleaned.replace(/```\s*/g, '');
  }

  // Remove any backticks
  cleaned = cleaned.replace(/`/g, '');

  // Remove any text before the first {
  const firstBrace = cleaned.indexOf('{');
  if (firstBrace > 0) {
    // Check if there's meaningful text before the brace (not just whitespace)
    const textBefore = cleaned.substring(0, firstBrace).trim();
    if (textBefore.length > 0 && !textBefore.match(/^[\s\n]*$/)) {
      console.warn('Removing text before JSON:', textBefore);
    }
    cleaned = cleaned.substring(firstBrace);
  }

  // Remove any text after the last }
  const lastBrace = cleaned.lastIndexOf('}');
  if (lastBrace > -1 && lastBrace < cleaned.length - 1) {
    const textAfter = cleaned.substring(lastBrace + 1).trim();
    if (textAfter.length > 0 && !textAfter.match(/^[\s\n]*$/)) {
      console.warn('Removing text after JSON:', textAfter);
    }
    cleaned = cleaned.substring(0, lastBrace + 1);
  }

  // Remove any "Based on..." or explanatory text at the beginning
  cleaned = cleaned.replace(/^Based on.*?:\s*/i, '');
  cleaned = cleaned.replace(/^The parsed JSON.*?:\s*/i, '');
  cleaned = cleaned.replace(/^Here is.*?:\s*/i, '');
  cleaned = cleaned.replace(/^JSON output.*?:\s*/i, '');

  // Validate that it's valid JSON
  try {
    JSON.parse(cleaned);
    return cleaned;
  } catch (error) {
    // If still not valid, try to extract JSON from common patterns
    const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      try {
        JSON.parse(jsonMatch[0]);
        return jsonMatch[0];
      } catch {
        // Fall through to throw error
      }
    }
    throw new Error(`Invalid JSON after cleaning: ${error.message}`);
  }
}

/**
 * Safe JSON parse with cleaning
 */
export function safeJsonParse<T = any>(content: string): T {
  try {
    // First try direct parsing
    return JSON.parse(content);
  } catch (firstError) {
    // Try cleaning the content
    try {
      const cleaned = cleanJsonResponse(content);
      return JSON.parse(cleaned);
    } catch (cleanError) {
      console.error('Failed to parse JSON even after cleaning:', content);
      throw new Error(`JSON parsing failed: ${firstError.message}`);
    }
  }
}
