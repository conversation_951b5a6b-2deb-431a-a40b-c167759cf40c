# Talent Upload Implementation Guide

## Overview

This guide outlines the implementation of an "Upload Existing Talent" feature for the Talent Hub page, including CSV/PDF upload capabilities and improved resume parsing using Groq AI.

## 1. Frontend Implementation (`talent-hub.tsx`)

### 1.1 Add Upload Button Component

```tsx
import { useState } from "react";
import { Upload, FileText, Users } from "lucide-react";

const TalentUploadButton = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  return (
    <div className="flex gap-2">
      <button
        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        onClick={() => document.getElementById("talent-upload")?.click()}
        disabled={isUploading}>
        <Upload className="w-4 h-4" />
        {isUploading
          ? `Uploading... ${uploadProgress}%`
          : "Upload Existing Talent"}
      </button>

      <input
        id="talent-upload"
        type="file"
        multiple
        accept=".pdf,.doc,.docx,.csv,.xlsx"
        className="hidden"
        onChange={handleFileUpload}
      />
    </div>
  );
};
```

### 1.2 File Upload Handler

```tsx
const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
  const files = Array.from(event.target.files || []);
  if (files.length === 0) return;

  setIsUploading(true);
  setUploadProgress(0);

  try {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`files`, file);
    });

    const response = await fetch("//talent/bulk-upload", {
      method: "POST",
      body: formData,
      onUploadProgress: (progressEvent) => {
        const progress = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        );
        setUploadProgress(progress);
      },
    });

    if (response.ok) {
      const result = await response.json();
      // Show success notification
      toast.success(`Successfully processed ${result.successCount} candidates`);
      // Refresh candidate list
      refetchCandidates();
    }
  } catch (error) {
    toast.error("Upload failed. Please try again.");
  } finally {
    setIsUploading(false);
    setUploadProgress(0);
  }
};
```

## 2. Backend API Implementation

### 2.1 Upload Endpoint (`//talent/bulk-upload`)

```typescript
// src//talent/bulk-upload.controller.ts
import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFiles,
} from "@nestjs/common";
import { FilesInterceptor } from "@nestjs/platform-express";
import { BulkTalentUploadService } from "./bulk-talent-upload.service";

@Controller("talent")
export class BulkTalentUploadController {
  constructor(private readonly bulkUploadService: BulkTalentUploadService) {}

  @Post("bulk-upload")
  @UseInterceptors(FilesInterceptor("files", 50)) // Max 50 files
  async uploadTalent(@UploadedFiles() files: Express.Multer.File[]) {
    return this.bulkUploadService.processBulkUpload(files);
  }
}
```

### 2.2 Bulk Upload Service

```typescript
// src/services/bulk-talent-upload.service.ts
import { Injectable, Logger } from "@nestjs/common";
import { ResumeExtractionService } from "./resume-extraction.service";
import { CsvParserService } from "./csv-parser.service";
import { CandidateService } from "./candidate.service";

@Injectable()
export class BulkTalentUploadService {
  private readonly logger = new Logger(BulkTalentUploadService.name);

  constructor(
    private readonly resumeExtraction: ResumeExtractionService,
    private readonly csvParser: CsvParserService,
    private readonly candidateService: CandidateService
  ) {}

  async processBulkUpload(files: Express.Multer.File[]) {
    const results = {
      successCount: 0,
      errorCount: 0,
      errors: [],
      processedCandidates: [],
    };

    for (const file of files) {
      try {
        const fileType = this.getFileType(file.originalname);
        let candidateData;

        switch (fileType) {
          case "resume":
            candidateData = await this.processResumeFile(file);
            break;
          case "csv":
            candidateData = await this.processCsvFile(file);
            break;
          default:
            throw new Error(`Unsupported file type: ${file.originalname}`);
        }

        // Create candidate(s) in database
        if (Array.isArray(candidateData)) {
          // CSV processing returns array
          for (const candidate of candidateData) {
            await this.createCandidate(candidate, file.originalname);
            results.successCount++;
          }
        } else {
          // Resume processing returns single object
          await this.createCandidate(candidateData, file.originalname);
          results.successCount++;
        }
      } catch (error) {
        results.errorCount++;
        results.errors.push({
          filename: file.originalname,
          error: error.message,
        });
      }
    }

    return results;
  }

  private getFileType(filename: string): "resume" | "csv" {
    const ext = filename.toLowerCase().split(".").pop();
    if (["csv", "xlsx"].includes(ext)) return "csv";
    if (["pdf", "doc", "docx"].includes(ext)) return "resume";
    throw new Error(`Unsupported file extension: ${ext}`);
  }

  private async processResumeFile(file: Express.Multer.File) {
    // Convert file to text using existing resume parsing logic
    const resumeText = await this.extractTextFromFile(file);
    return this.resumeExtraction.extractResumeData(resumeText);
  }

  private async processCsvFile(file: Express.Multer.File) {
    return this.csvParser.parseCandidatesCsv(file);
  }

  private async createCandidate(candidateData: any, originalFilename: string) {
    // Map extracted data to Candidate entity
    const mappedCandidate = this.mapToCandidateEntity(
      candidateData,
      originalFilename
    );
    return this.candidateService.create(mappedCandidate);
  }

  private mapToCandidateEntity(data: any, originalFilename: string) {
    return {
      ...data,
      source: "BULK_UPLOAD",
      originalFilename,
      extractionMetadata: [
        {
          filename: originalFilename,
          extractedAt: new Date(),
          extractionSource: "groq_ai",
          rawExtractedData: data,
        },
      ],
      status: CandidateStatus.NEW,
      contacted: false,
    };
  }
}
```

## 3. Improved Resume Extraction Prompt

### 3.1 Enhanced Groq Prompt

Replace the existing `systemPrompt` in your `extractResumeData` method with this improved version:

```typescript
const systemPrompt = `You are an expert resume parser and candidate profiler. Extract comprehensive information from resumes and return ONLY a valid JSON object. Your goal is to populate as many fields as possible from the available data.

REQUIRED JSON STRUCTURE:
{
  "fullName": "string",
  "firstName": "string",
  "lastName": "string",
  "email": "string",
  "phone": "string",
  "location": "string",
  "jobTitle": "string",
  "currentCompany": "string",
  "yearsOfExperience": number,
  "summary": "string",
  "skills": ["string"],
  "experience": [
    {
      "title": "string",
      "company": "string",
      "duration": "string",
      "startDate": "YYYY-MM-DD",
      "endDate": "YYYY-MM-DD or null for current",
      "location": "string",
      "description": "string"
    }
  ],
  "education": [
    {
      "degree": "string",
      "institution": "string",
      "year": "string",
      "location": "string",
      "gpa": "string",
      "fieldOfStudy": "string"
    }
  ],
  "linkedinUrl": "string",
  "githubUrl": "string",
  "preferredLocation": "string",
  "isRemoteOnly": boolean,
  "salary": {
    "min": number,
    "max": number,
    "currency": "string"
  },
  "availableFrom": "YYYY-MM-DD",
  "certifications": ["string"],
  "languages": ["string"],
  "projects": [
    {
      "name": "string",
      "description": "string",
      "technologies": ["string"],
      "url": "string"
    }
  ]
}

EXTRACTION RULES:
1. CRITICAL: Return ONLY valid JSON, no markdown code blocks, no backticks, no explanations
2. Output must be parseable by JSON.parse() directly
3. Use null for missing values, never leave fields undefined
4. Extract ALL available information, don't leave fields empty if data exists

NAME EXTRACTION:
- Parse full name carefully, handle titles (Dr., Mr., Ms.) and suffixes (Jr., Sr., III)
- Split into firstName and lastName appropriately

CONTACT INFORMATION:
- Extract email addresses carefully, validate format
- Parse phone numbers, include country code if visible
- Determine current location from address or "Based in" statements

PROFESSIONAL DETAILS:
- jobTitle: Use most recent position title or stated objective
- currentCompany: Extract from most recent experience or current employer
- yearsOfExperience: Calculate from work history timeline, count overlapping roles

EXPERIENCE PROCESSING:
- Convert all date ranges to startDate/endDate format (YYYY-MM-DD)
- For "Present", "Current", "Now" use null for endDate
- Parse duration strings like "Jan 2020 - Mar 2022" into proper dates
- Include detailed job descriptions and achievements

SKILLS EXTRACTION:
- Extract both technical and soft skills
- Include programming languages, frameworks, tools, methodologies
- Parse from skills sections, experience descriptions, and project details

EDUCATION:
- Extract degree type, institution name, graduation year
- Include GPA if mentioned
- Parse field of study/major

ADDITIONAL DATA:
- Look for salary expectations, availability dates
- Identify remote work preferences
- Extract certifications, licenses, awards
- Parse project details and portfolio links
- Identify spoken languages

INTELLIGENT INFERENCE:
- If location shows multiple cities, use most recent as current location
- If experience shows remote work, set isRemoteOnly accordingly
- Infer preferred location from job applications or stated preferences
- Calculate total experience including overlapping roles smartly`;
```

### 3.2 Enhanced User Prompt

```typescript
const userPrompt = `Extract comprehensive information from this resume. Pay special attention to:
- All contact details and social profiles
- Complete work history with accurate dates
- Technical and soft skills mentioned anywhere in the document
- Education details including specializations
- Any salary expectations or availability mentioned
- Remote work preferences or location flexibility
- Certifications, projects, and achievements

Resume content:
${resumeContent}`;
```

## 4. CSV Parser Service

### 4.1 CSV Template

Create a standardized CSV template for bulk uploads:

```csv
fullName,firstName,lastName,email,phone,location,jobTitle,currentCompany,yearsOfExperience,skills,linkedinUrl,githubUrl,summary
John Doe,John,Doe,<EMAIL>,+1234567890,"New York, NY",Software Engineer,Tech Corp,5,"JavaScript,React,Node.js",https://linkedin.com/in/johndoe,https://github.com/johndoe,"Experienced developer..."
```

### 4.2 CSV Parser Implementation

```typescript
// src/services/csv-parser.service.ts
import { Injectable } from "@nestjs/common";
import * as csv from "csv-parser";
import { Readable } from "stream";

@Injectable()
export class CsvParserService {
  async parseCandidatesCsv(file: Express.Multer.File): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const results = [];
      const stream = Readable.from(file.buffer);

      stream
        .pipe(csv())
        .on("data", (data) => {
          // Process and validate each row
          const processedCandidate = this.processCsvRow(data);
          if (processedCandidate) {
            results.push(processedCandidate);
          }
        })
        .on("end", () => resolve(results))
        .on("error", reject);
    });
  }

  private processCsvRow(row: any) {
    // Validate required fields
    if (!row.fullName || !row.email) {
      return null;
    }

    return {
      fullName: row.fullName?.trim(),
      firstName: row.firstName?.trim() || row.fullName?.split(" ")[0],
      lastName:
        row.lastName?.trim() || row.fullName?.split(" ").slice(1).join(" "),
      email: row.email?.trim(),
      phone: row.phone?.trim(),
      location: row.location?.trim(),
      jobTitle: row.jobTitle?.trim(),
      currentCompany: row.currentCompany?.trim(),
      yearsOfExperience: parseInt(row.yearsOfExperience) || 0,
      summary: row.summary?.trim(),
      skills: row.skills ? row.skills.split(",").map((s) => s.trim()) : [],
      linkedinUrl: row.linkedinUrl?.trim(),
      githubUrl: row.githubUrl?.trim(),
      preferredLocation: row.preferredLocation?.trim(),
      isRemoteOnly: row.isRemoteOnly?.toLowerCase() === "true",
    };
  }
}
```

## 5. Progress Tracking & Error Handling

### 5.1 WebSocket Progress Updates (Optional)

```typescript
// For real-time progress updates during bulk processing
@WebSocketGateway()
export class UploadProgressGateway {
  @WebSocketServer()
  server: Server;

  notifyProgress(clientId: string, progress: any) {
    this.server.to(clientId).emit("upload-progress", progress);
  }
}
```

### 5.2 Error Handling & Validation

```typescript
// Add comprehensive validation
private validateCandidateData(data: any): string[] {
  const errors = [];

  if (!data.fullName) errors.push('Full name is required');
  if (!data.email || !/\S+@\S+\.\S+/.test(data.email)) {
    errors.push('Valid email is required');
  }

  return errors;
}
```

## 6. Testing Strategy

### 6.1 Test Files

- Create sample PDF resumes with various formats
- Prepare test CSV files with edge cases
- Test with malformed files and unsupported formats

### 6.2 Unit Tests

```typescript
describe("BulkTalentUploadService", () => {
  it("should process PDF resume correctly", async () => {
    // Test resume processing
  });

  it("should handle CSV with missing fields", async () => {
    // Test CSV error handling
  });

  it("should validate extracted data", async () => {
    // Test data validation
  });
});
```

## 7. UI/UX Considerations

### 7.1 Upload Interface

- Drag & drop zone for files
- Progress indicator for bulk uploads
- Preview of extracted data before saving
- Error reporting with specific file issues
- Success summary with processed count

### 7.2 Validation & Preview

- Show extracted data in a table format
- Allow manual editing before final import
- Highlight potential duplicates
- Provide mapping interface for CSV columns

## 8. Performance Optimization

### 8.1 Batch Processing

- Process files in batches to avoid memory issues
- Implement queue system for large uploads
- Add rate limiting for Groq API calls

### 8.2 Caching & Storage

- Cache parsed results temporarily
- Store original files for reprocessing if needed
- Implement cleanup for temporary files

This implementation provides a robust, scalable solution for bulk talent upload with comprehensive error handling and data extraction capabilities.
